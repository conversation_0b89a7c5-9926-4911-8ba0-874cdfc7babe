<?php
return [
    'modules' => [
        'Magento_Store' => 1,
        'Magento_Config' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_AdobeIms' => 1,
        'Magento_AdobeImsApi' => 1,
        'Magento_AdvancedPricingImportExport' => 1,
        'Magento_Directory' => 1,
        'Magento_Amqp' => 1,
        'Magento_Theme' => 1,
        'Magento_Backend' => 1,
        'Magento_Variable' => 1,
        'Magento_Eav' => 1,
        'Magento_User' => 1,
        'Magento_Backup' => 1,
        'Magento_Customer' => 1,
        'Magento_Indexer' => 1,
        'Magento_BundleImportExport' => 1,
        'Magento_CacheInvalidate' => 1,
        'Magento_Cms' => 1,
        'Magento_Rule' => 1,
        'Magento_Authorization' => 1,
        'Magento_GraphQl' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Payment' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_CheckoutAgreementsGraphQl' => 1,
        'Magento_Robots' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_CmsUrlRewriteGraphQl' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_Security' => 1,
        'Magento_Msrp' => 1,
        'Magento_Sales' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_Checkout' => 1,
        'Magento_Contact' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_Widget' => 1,
        'Magento_Bundle' => 1,
        'Magento_Downloadable' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_CustomerImportExport' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_ImportExport' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_Elasticsearch7' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GoogleAnalytics' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_CatalogCmsGraphQl' => 1,
        'Magento_PageCache' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedImportExport' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_DownloadableImportExport' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_Integration' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 1,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 1,
        'Magento_InventoryBundleProductAdminUi' => 1,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 1,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_InventoryCatalogSearch' => 1,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_InventoryConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 1,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventoryGroupedProduct' => 1,
        'Magento_InventoryGroupedProductAdminUi' => 1,
        'Magento_InventoryGroupedProductIndexer' => 1,
        'Magento_InventoryImportExport' => 1,
        'Magento_InventoryInStorePickupApi' => 1,
        'Magento_Ui' => 1,
        'Magento_InventorySourceSelectionApi' => 1,
        'Magento_InventoryInStorePickup' => 0,
        'Magento_InventoryInStorePickupGraphQl' => 0,
        'Magento_Shipping' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 0,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 0,
        'Magento_InventoryInStorePickupSales' => 0,
        'Magento_InventoryInStorePickupSalesApi' => 0,
        'Magento_InventoryInStorePickupQuote' => 0,
        'Magento_InventoryInStorePickupShipping' => 0,
        'Magento_InventoryInStorePickupShippingAdminUi' => 0,
        'Magento_InventoryInStorePickupMultishipping' => 0,
        'Magento_Webapi' => 1,
        'Magento_InventoryCache' => 1,
        'Magento_InventoryLowQuantityNotification' => 1,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 1,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 1,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAdminUi' => 1,
        'Magento_CatalogInventoryGraphQl' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 1,
        'Magento_InventorySetupFixtureGenerator' => 1,
        'Magento_InventoryShipping' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventoryInStorePickupFrontend' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 1,
        'Magento_Newsletter' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_Sitemap' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_Captcha' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_Vault' => 1,
        'Magento_Paypal' => 1,
        'Magento_Persistent' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 0,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 0,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_Rss' => 1,
        'Magento_AwsS3' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_ConfigurableImportExport' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_TwoFactorAuth' => 0,
        'Magento_SendFriend' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 0,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_Swagger' => 1,
        'Magento_SwaggerWebapi' => 1,
        'Magento_SwaggerWebapiAsync' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_TaxGraphQl' => 1,
        'Magento_TaxImportExport' => 1,
        'Magento_CustomerDownloadableGraphQl' => 1,
        'Magento_ThemeGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_AdminAdobeIms' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 0,
        'Magento_UrlRewrite' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_AdminAdobeImsTwoFactorAuth' => 1,
        'Magento_InventoryGraphQl' => 0,
        'Magento_PaypalGraphQl' => 1,
        'Magento_VaultGraphQl' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 0,
        'Magento_WebapiAsync' => 1,
        'Magento_Weee' => 0,
        'Magento_WeeeGraphQl' => 0,
        'Magento_CurrencySymbol' => 1,
        'Magento_Wishlist' => 1,
        'Magento_WishlistGraphQl' => 1,
        'Actiview_Honeypot' => 1,
        'Amasty_Base' => 1,
        'Amasty_CustomFormsLiteSubscriptionPackage' => 1,
        'Amasty_Customform' => 1,
        'Amasty_InvisibleCaptcha' => 1,
        'Biemans_BeforeNineAmCustomers' => 1,
        'Biemans_BlockedCustomers' => 1,
        'Biemans_Catalog' => 1,
        'Biemans_Configurator' => 1,
        'Smile_ElasticsuiteCore' => 1,
        'Hyva_Theme' => 1,
        'Biemans_CustomerDataAlertSubscription' => 1,
        'Biemans_CustomerDebtorNumber' => 1,
        'Biemans_CustomerGroupCurrency' => 1,
        'Biemans_CustomerGroupPriceList' => 1,
        'Biemans_CustomerGroupWebsite' => 1,
        'Biemans_CustomerInvoices' => 1,
        'Biemans_CustomerLastLoggedIn' => 1,
        'Biemans_CustomerMenu' => 1,
        'Biemans_CustomerNameOptional' => 1,
        'Biemans_CustomerTrackAndTraceEmail' => 1,
        'Biemans_Decimals' => 1,
        'Biemans_ShipmentMethods' => 1,
        'Biemans_ErrorAvOrderStatus' => 1,
        'Biemans_ExtendedCart' => 1,
        'Biemans_ExtendedCustomerAccount' => 1,
        'Smile_ElasticsuiteCatalog' => 1,
        'Magewirephp_Magewire' => 1,
        'MagePal_Core' => 1,
        'Mageplaza_Core' => 1,
        'Biemans_ExtendedPageBuilder' => 1,
        'Biemans_ExtendedProductAlert' => 1,
        'Biemans_ExtendedWishlist' => 1,
        'Biemans_GuestRestrictions' => 1,
        'Biemans_LimitedBackorders' => 1,
        'Biemans_MessagePopup' => 1,
        'Smile_ElasticsuiteCatalogRule' => 1,
        'Biemans_OldPrice' => 1,
        'Biemans_OrderHub' => 1,
        'Biemans_PageBuilderBannerAppearance' => 1,
        'Biemans_PageBuilderDoorwaysLayout' => 1,
        'Biemans_PalletShipment' => 1,
        'Biemans_ParcelShipment' => 1,
        'Biemans_PdfCatalog' => 1,
        'Biemans_PdfCatalogPublic' => 1,
        'Biemans_PickupCarrierShipment' => 1,
        'Biemans_PickupShipment' => 1,
        'Biemans_Prepress' => 1,
        'Biemans_PrepressExtensions' => 1,
        'Biemans_ProductDataExcel' => 1,
        'Biemans_ProductSpecifications' => 1,
        'Biemans_PushNotificationOutlet' => 1,
        'Biemans_Reservation' => 1,
        'Biemans_Samples' => 1,
        'Biemans_Sendcloud' => 1,
        'Biemans_DepotShipment' => 1,
        'Biemans_StockStatus' => 1,
        'Biemans_StoreForcedLanguage' => 1,
        'Snowdog_Menu' => 1,
        'Biemans_ToggleCoupon' => 1,
        'Hyva_Checkout' => 1,
        'Hyva_CompatModuleFallback' => 1,
        'Hyva_Email' => 1,
        'Hyva_ExtendedActiviewHoneypot' => 1,
        'Hyva_GraphqlTokens' => 1,
        'Hyva_GraphqlViewModel' => 1,
        'MagePal_GoogleTagManager' => 1,
        'Hyva_MagePalGoogleTagManager' => 1,
        'Hyva_OrderCancellationWebapi' => 1,
        'Smile_ElasticsuiteSwatches' => 1,
        'Biemans_ExtendedHyvaCheckout' => 1,
        'MagePal_GoogleAnalytics4' => 1,
        'Hyva_MagePalGoogleAnalytics4' => 1,
        'Biemans_ExtendedMagepalGTM' => 1,
        'Mageplaza_QuickOrder' => 1,
        'Biemans_ExtendedMageplazaQuickOrder' => 1,
        'Biemans_CustomerComment' => 1,
        'Magmodules_AlternateHreflang' => 1,
        'OlegKoval_RegenerateUrlRewrites' => 1,
        'Redkiwi_ApiLog' => 1,
        'Redkiwi_GoogleVerification' => 1,
        'Redkiwi_Popup' => 1,
        'Redkiwi_RichSnippets' => 1,
        'Redkiwi_TranslationEditor' => 1,
        'Smile_ElasticsuiteAdminNotification' => 1,
        'Smile_ElasticsuiteTracker' => 1,
        'Biemans_CustomCatalog' => 1,
        'Smile_ElasticsuiteCatalogGraphQl' => 1,
        'Smile_ElasticsuiteCatalogOptimizer' => 1,
        'Biemans_NewProducts' => 1,
        'Biemans_ExtendedElasticSearch' => 1,
        'Smile_ElasticsuiteThesaurus' => 1,
        'Hyva_SmileElasticsuite' => 1,
        'Smile_ElasticsuiteIndices' => 1,
        'Smile_ElasticsuiteAnalytics' => 1,
        'Smile_ElasticsuiteVirtualCategory' => 1,
        'Biemans_ThemeConfigurations' => 1,
        'Yireo_DisableCsp' => 1,
        'Yireo_NextGenImages' => 1,
        'Yireo_Webp2' => 1
    ],
    'system' => [
        'default' => [
            'catalog' => [
                'seo' => [
                    'product_url_suffix' => '',
                    'category_url_suffix' => ''
                ],
                'review' => [
                    'active' => '0'
                ],
                'frontend' => [
                    'show_add_to_compare_in_list' => '0',
                    'show_sidebar_in_list' => '0',
                    'show_add_to_compare_on_product_page' => '0'
                ],
                'layered_navigation' => [
                    'display_product_count' => '0'
                ]
            ],
            'currency' => [
                'options' => [
                    'allow' => 'EUR',
                    'base' => 'EUR',
                    'default' => 'EUR',
                    'customsymbol' => '{"EUR":"€","USD":"$","GBP":"£","DKK":"kr.","PLN":"zł"}'
                ],
                'import' => [
                    'enabled' => '0',
                    'error_email_identity' => 'general',
                    'error_email_template' => 'currency_import_error_email_template'
                ]
            ],
            'general' => [
                'region' => [
                    'display_all' => '0',
                    'state_required' => ''
                ]
            ],
            'persistent' => [
                'options' => [
                    'enabled' => '1',
                    'lifetime' => '31536000',
                    'remember_enabled' => '1',
                    'remember_default' => '1',
                    'logout_clear' => '1',
                    'shopping_cart' => '1'
                ]
            ],
            'sendfriend' => [
                'email' => [
                    'enabled' => '0'
                ]
            ],
            'admin' => [
                'dashboard' => [
                    'enable_charts' => '0'
                ]
            ],
            'customer' => [
                'account_share' => [
                    'scope' => '1'
                ],
                'captcha' => [
                    'enable' => '0'
                ],
                'password' => [
                    'reset_link_expiration_period' => '48'
                ]
            ],
            'smile_elasticsuite_misc_settings' => [
                'footer_settings' => [
                    'enable_es_link' => '0'
                ]
            ],
            'tax' => [
                'classes' => [
                    'shipping_tax_class' => '0',
                    'default_product_tax_class' => '0'
                ],
                'calculation' => [
                    'price_includes_tax' => '0',
                    'shipping_includes_tax' => '0',
                    'cross_border_trade_enabled' => '0'
                ],
                'display' => [
                    'type' => '0',
                    'shipping' => '0'
                ]
            ],
            'web' => [
                'url' => [
                    'use_store' => '0'
                ],
                'seo' => [
                    'use_rewrites' => '1'
                ]
            ],
            'design' => [
                'theme' => [
                    'theme_id' => 'frontend/Biemans/default'
                ]
            ],
            'cataloginventory' => [
                'options' => [
                    'show_out_of_stock' => '1'
                ],
                'item_options' => [
                    'backorders' => '0',
                    'min_qty' => '0'
                ]
            ],
            'oauth' => [
                'consumer' => [
                    'enable_integration_as_bearer' => '1'
                ]
            ],
            'checkout' => [
                'cart' => [
                    'delete_quote_after' => '400'
                ]
            ],
            'newsletter' => [
                'general' => [
                    'active' => '0'
                ]
            ],
            'hyva_themes_checkout' => [
                'general' => [
                    'checkout' => 'onepage'
                ],
                'component' => [
                    'order_comment' => [
                        'enable' => '1'
                    ],
                    'discount_code' => [
                        'enable' => '1'
                    ],
                    'shipping' => [
                        'address_list_view' => 'grid'
                    ],
                    'billing' => [
                        'shipping_as_billing' => '0'
                    ]
                ],
                'signin_registration' => [
                    'show_signin_registration_button' => '0'
                ]
            ],
            'googletagmanager' => [
                'general' => [
                    'active' => '1',
                    'account' => 'GTM-58Q3KLC',
                    'item_variant_layer' => '1',
                    'item_variant_format' => '1',
                    'category_layer' => '1',
                    'ua_purchase_tracking' => '0'
                ],
                'google_analytics4' => [
                    'active' => '1',
                    'refund' => '1',
                    'admin_order_tracking' => '1'
                ]
            ]
        ],
        'stores' => [
            'nl' => [
                'general' => [
                    'locale' => [
                        'code' => 'nl_NL'
                    ]
                ],
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}nl/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{secure_base_url}}nl/'
                    ]
                ]
            ],
            'en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ],
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}en/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{secure_base_url}}en/'
                    ]
                ]
            ],
            'de' => [
                'general' => [
                    'locale' => [
                        'code' => 'de_DE'
                    ]
                ],
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}de/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{secure_base_url}}de/'
                    ]
                ]
            ],
            'fr' => [
                'general' => [
                    'locale' => [
                        'code' => 'fr_FR'
                    ]
                ],
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}fr/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{secure_base_url}}fr/'
                    ]
                ]
            ],
            'biemans_pl_pl' => [
                'general' => [
                    'locale' => [
                        'code' => 'pl_PL'
                    ]
                ],
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}pl/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{secure_base_url}}pl/'
                    ]
                ]
            ],
            'biemans_pl_en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ],
                'web' => [
                    'unsecure' => [
                        'base_link_url' => '{{unsecure_base_url}}en/'
                    ],
                    'secure' => [
                        'base_link_url' => '{{secure_base_url}}en/'
                    ]
                ]
            ],
            'biemans_uk_en' => [
                'general' => [
                    'locale' => [
                        'code' => 'en_US'
                    ]
                ]
            ]
        ]
    ],
    'scopes' => [
        'websites' => [
            'admin' => [
                'website_id' => '0',
                'code' => 'admin',
                'name' => 'Admin',
                'sort_order' => '0',
                'default_group_id' => '0',
                'is_default' => '0'
            ],
            'base' => [
                'website_id' => '1',
                'code' => 'base',
                'name' => 'Main Website',
                'sort_order' => '0',
                'default_group_id' => '1',
                'is_default' => '1'
            ],
            'biemans_pl' => [
                'website_id' => '3',
                'code' => 'biemans_pl',
                'name' => 'Biemans_Polen',
                'sort_order' => '0',
                'default_group_id' => '2',
                'is_default' => '0'
            ],
            'biemans_uk' => [
                'website_id' => '4',
                'code' => 'biemans_uk',
                'name' => 'Biemans UK',
                'sort_order' => '0',
                'default_group_id' => '3',
                'is_default' => '0'
            ]
        ],
        'groups' => [
            [
                'group_id' => '0',
                'website_id' => '0',
                'name' => 'Default',
                'root_category_id' => '0',
                'default_store_id' => '0',
                'code' => 'default'
            ],
            [
                'group_id' => '1',
                'website_id' => '1',
                'name' => 'Main Website Store',
                'root_category_id' => '2',
                'default_store_id' => '1',
                'code' => 'main_website_store'
            ],
            [
                'group_id' => '2',
                'website_id' => '3',
                'name' => 'Biemans Polen',
                'root_category_id' => '2',
                'default_store_id' => '5',
                'code' => 'biemans_pl_store'
            ],
            [
                'group_id' => '3',
                'website_id' => '4',
                'name' => 'Biemans UK',
                'root_category_id' => '2',
                'default_store_id' => '6',
                'code' => 'biemans_uk_store'
            ]
        ],
        'stores' => [
            'admin' => [
                'store_id' => '0',
                'code' => 'admin',
                'website_id' => '0',
                'group_id' => '0',
                'name' => 'Admin',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'nl' => [
                'store_id' => '1',
                'code' => 'nl',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Nederland',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'en' => [
                'store_id' => '2',
                'code' => 'en',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'English',
                'sort_order' => '20',
                'is_active' => '1'
            ],
            'de' => [
                'store_id' => '3',
                'code' => 'de',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Deutsch',
                'sort_order' => '30',
                'is_active' => '1'
            ],
            'fr' => [
                'store_id' => '4',
                'code' => 'fr',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Francais',
                'sort_order' => '40',
                'is_active' => '1'
            ],
            'biemans_pl_en' => [
                'store_id' => '5',
                'code' => 'biemans_pl_en',
                'website_id' => '3',
                'group_id' => '2',
                'name' => 'Biemans Poland (Engels)',
                'sort_order' => '40',
                'is_active' => '1'
            ],
            'biemans_pl_pl' => [
                'store_id' => '7',
                'code' => 'biemans_pl_pl',
                'website_id' => '3',
                'group_id' => '2',
                'name' => 'Biemans Poland (Polen)',
                'sort_order' => '30',
                'is_active' => '1'
            ],
            'biemans_uk_en' => [
                'store_id' => '6',
                'code' => 'biemans_uk_en',
                'website_id' => '4',
                'group_id' => '3',
                'name' => 'Biemans UK',
                'sort_order' => '50',
                'is_active' => '1'
            ]
        ]
    ]
];
