<?php

declare(strict_types=1);

use Magento\CatalogSearch\Block\Result;
use Magento\Framework\Escaper;

/** These changes need to valid applying filters and configuration before search process is started. */

/** @var Result $block */
/** @var Escaper $escaper */
$productList = $block->getProductListHtml();
?>
<?php if ($block->getResultCount()) : ?>
    <div class="search results">
        <?php if ($messages = $block->getNoteMessages()) : ?>
        <div class="message notice">
            <div>
                <?php foreach ($messages as $message) : ?>
                    <?= /* @noEscape */ $message ?><br />
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        <?= /* @noEscape */ $productList ?>
    </div>
<?php else : ?>

<div class="container">
    <div class="message notice notice--closest border border-secondary text-center">
        <div>
            <?= $escaper->escapeHtml($block->getNoResultText() ? $block->getNoResultText() : __('Your search returned no results.')) /** @phpstan-ignore-line */ ?>
            <?= /* @noEscape */ $block->getAdditionalHtml() ?>
            <?php if ($messages = $block->getNoteMessages()) : ?>
                <?php foreach ($messages as $message) : ?>
                    <br /><?= /* @noEscape */ $message ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>
