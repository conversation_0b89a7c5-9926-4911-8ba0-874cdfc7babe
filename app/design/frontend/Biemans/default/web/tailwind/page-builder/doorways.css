[data-appearance="doorways-title"],
[data-appearance="doorways"] {

    @apply relative w-screen -translate-x-1/2 left-1/2;

    /* dark row */
    &[data-color-theme="black"] {
        [data-content-type="heading"],
        .doorway__title {
            @apply text-white;
        }
    }

    > div {
        @apply px-0 py-4 m-0 md:py-7 lg:py-10;
    }

    .doorways {
        @apply w-full mx-auto max-w-container px-container-padding;
    }

}

[data-appearance="doorways-title"] {

    > div {
        @apply shadow-y-outline-secondary-500;
    }

    /* title */
    [data-content-type="heading"] {
        @apply my-0 h1 lg:h2 grow lg:col-span-4;
    }

    .doorways {
        @apply flex flex-col gap-y-4;
    }

    /* buttons */
    [data-content-type="buttons"] {
        @apply flex gap-x-4 gap-y-2;

        [data-content-type='button-item'] {
            @apply m-0;
        }
    }

    /* on medium screens and up */
    @screen md {

        .doorways {
            @apply flex-row items-center;
        }

        [data-content-type="buttons"] {
            @apply ml-auto;
        }
    }

}

[data-appearance="doorways"] {

    /* doorways wrapper */
    .doorways {
        @apply flex lg:grid sm-max:flex-col lg:grid-cols-4 gap-y-6 md-max:flex-wrap md:justify-center;
    }

    /* doorway elements */
    .doorway {
        @apply relative flex justify-center lg:grow-0 lg:shrink;

        &:hover,
        &:focus-within {
            .pagebuilder-banner-image {
                @apply grayscale-0;
            }
        }

        /* inner */
        > div {
            @apply inline-flex flex-col md:w-full gap-y-4;
        }
        
        /* title */
        .doorway__title {
            @apply h2 lg:h4;
        }

        a.doorway__title {
            @apply after:absolute after:inset-0;
        }

        /* image */
        .pagebuilder-banner-image {
            @apply md-max:w-[343px] md-max:h-[388px] max-w-full aspect-[53/60] grayscale transition-filter;

            min-height: unset !important;
            clip-path: polygon(9% 0, 100% 0%, 91% 100%, 0% 100%);
        }
    }
}