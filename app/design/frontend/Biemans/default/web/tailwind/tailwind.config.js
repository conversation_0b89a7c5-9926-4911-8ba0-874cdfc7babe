const { spacing } = require('tailwindcss/defaultTheme');

const hyvaModules = require('@hyva-themes/hyva-modules');

module.exports = hyvaModules.mergeTailwindConfig({
    plugins: [require('@tailwindcss/forms'), require('@tailwindcss/typography')],
    corePlugins: {
        container: false
    },
    content: [
        "./../../../../../../code/Biemans/**/*.{html,js,phtml,php}",
        // this theme's phtml and layout XML files
        '../../**/*.phtml',
        '../../*/layout/*.xml',
        // parent theme in Vendor (if this is a child-theme)
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml'
        // app/code phtml files (if need tailwind classes from app/code modules)
        //'../../../../../../../app/code/**/*.phtml'
    ],
    safelist: [
        'bg-black-75'
    ],
    theme: {
        screens: {
            'xs-max': { 'max': '374px' },
            'sm': '375px',
            'sm-max': { 'max': '767px' },
            'md': '768px',
            'md-max': { 'max': '1023px' },
            'lg': '1024px',
            'lg-max': { 'max': '1279px' },
            'xl': '1280px',
            'xl-max': { 'max': '1439px' },
            '2xl': '1440px',
            '2xl-max': { 'max': '1599px' },
            '3xl': '1600px',
            'container-max': 'calc(100rem + 1px)',
            '640-max': { 'max': '639px' },
        },
        fontFamily: {
            'base': 'var(--font-base)',
            'heading': 'var(--font-heading)',
            'subheading': 'var(--font-subheading)',
            'icon': 'var(--font-icon)',
            'inherit': 'inherit'
        },
        fontSize: {
            'xxs-em': ['.6em', '1.25'],
            'xxs': ['.6rem', '1.25'],
            'xs-em': ['.75em', '1.25'],
            'xs': ['.75rem', '1.25'],
            'sm-em': ['.875em', '1.25'],
            'sm': ['.875rem', '1.25'],
            'base-em': ['1em', '1.5'],
            'base': ['1rem', '1.5'],
            'lg-em': ['1.125em', '1.75'],
            'lg': ['1.125rem', '1.75'],
            'xl-em': ['1.25em', '1.75'],
            'xl': ['1.25rem', '1.75'],
            '2xl-em': ['1.5em', '2'],
            '2xl': ['1.5rem', '2'],
            '3xl-em': ['1.875em', '2.25'],
            '3xl': ['1.875rem', '2.25'],
            '4xl-em': ['2.25em', '2.5'],
            '4xl': ['2.25rem', '2.5'],
            '5xl-em': ['3em', '1'],
            '5xl': ['3rem', '1'],
            '6xl-em': ['4em', '1'],
            '6xl': ['4rem', '1'],
            '7xl-em': ['5em', '1'],
            '7xl': ['5rem', '1'],
            '8xl-em': ['6em', '1'],
            '8xl': ['6rem', '1'],
            '9xl-em': ['8em', '1'],
            '9xl': ['8rem', '1'],
            'inherit': 'inherit'
        },
        backgroundImage: {
            bronze: 'var(--bg-bronze)',
            silver: 'var(--bg-silver)',
            gold: 'var(--bg-gold)',
        },
        extend: {
            backdropBlur: {
                'xs': '2px'
            },
            borderRadius: {
                '4xl': '2rem'
            },
            boxShadow: {
                'outline-secondary-500': '1px 0 0 0 var(--color-secondary-500), 0 1px 0 0 var(--color-secondary-500), 1px 1px 0 0 var(--color-secondary-500), 1px 0 0 0 var(--color-secondary-500) inset, 0 1px 0 0 var(--color-secondary-500) inset',
                'x-outline-secondary-500': '1px 0 0 0 var(--color-secondary-500), 0 0px 0 0 var(--color-secondary-500), 0px 0px 0 0 var(--color-secondary-500), 1px 0 0 0 var(--color-secondary-500) inset, 0 0px 0 0 var(--color-secondary-500) inset',
                'y-outline-secondary-500': '0px 0 0 0 var(--color-secondary-500), 0 0px 0 0 var(--color-secondary-500), 0px 1px 0 0 var(--color-secondary-500), 0px 0 0 0 var(--color-secondary-500) inset, 0 1px 0 0 var(--color-secondary-500) inset',
                'r-outline-secondary-500': '1px 0 0 var(--color-secondary-500), 0 0px 0 0 var(--color-secondary-500), 0px 0px 0 0 var(--color-secondary-500), 0px 0 0 0 var(--color-secondary-500) inset, 0 0px 0 0 var(--color-secondary-500) inset',
                'b-outline-secondary-500': '0px 0 0 var(--color-secondary-500), 0 0px 0 0 var(--color-secondary-500), 0px 1px 0 0 var(--color-secondary-500), 0px 0 0 0 var(--color-secondary-500) inset, 0 0px 0 0 var(--color-secondary-500) inset',
                'panel-top': '0 -2px 52px rgba(0,0,0,0.07)',
                'panel-bottom': '0 2px 52px rgba(0,0,0,0.07)'
            },
            colors: {
            primary: {
                DEFAULT: 'var(--color-primary)',
                100: 'var(--color-primary-100)',
                300: 'var(--color-primary-300)',
                500: 'var(--color-primary-500)',
                700: 'var(--color-primary-700)',
                900: 'var(--color-primary-900)'
            },
            secondary: {
                DEFAULT: 'var(--color-secondary)',
                200: 'var(--color-secondary-200)',
                300: 'var(--color-secondary-300)',
                400: 'var(--color-secondary-400)',
                500: 'var(--color-secondary-500)',
                700: 'var(--color-secondary-700)',
            },
            white: {
                DEFAULT: 'var(--color-white)'
            },
            black: {
                DEFAULT: 'var(--color-black)',
                75: 'rgba(var(--color-black-rgb), 0.75)',
            },
            grayscale: {
                DEFAULT: 'var(--color-grayscale-500)',
                50: 'var(--color-grayscale-50)',
                100: 'var(--color-grayscale-100)',
                300: 'var(--color-grayscale-300)',
                500: 'var(--color-grayscale-500)',
                700: 'var(--color-grayscale-700)',
                900: 'var(--color-grayscale-900)'
            },
            background: {
                DEFAULT: 'var(--color-white)',
                100: 'var(--color-background-100)',
                300: 'var(--color-background-300)',
                500: 'var(--color-background-500)',
            },
            success: {
                DEFAULT: 'var(--color-success)',
                500: 'var(--color-success-500)'
            },
            warning: {
                DEFAULT: 'var(--color-warning)',
            },
            error: {
                DEFAULT: 'var(--color-error)',
                500: 'var(--color-error-500)'
            },
            'in-stock': {
                DEFAULT: 'var(--color-in-stock)',
            },
            labels: {
                DEFAULT: 'var(--color-labels-red)',
                'red': 'var(--color-labels-red)',
                'yellow': 'var(--color-labels-yellow)',
            },
            'out-of-stock': {
                DEFAULT: 'var(--color-out-of-stock)',
            },
            bronze: {
                DEFAULT: 'var(--color-bronze)',
            },
            silver: {
                DEFAULT: 'var(--color-silver)',
            },
            gold: {
                DEFAULT: 'var(--color-gold)',
            },
                transparent: 'transparent',
                inherit: 'inherit',
                current: 'currentColor'
            },
            container: {
                center: true,
                padding: 'var(--container-padding)'
            },
            lineHeight: {
                'inherit': 'inherit'
            },
            maxWidth: {
                'container': 'var(--container-max-width)'
            },
            minWidth: {
                'button': '45px'
            },
            minHeight: {
                'button': '45px'
            },
            spacing: {
                'container-padding': 'var(--container-padding)',
                'header-height': 'var(--header-height)',
            },
            transitionProperty: {
                'filter': 'filter'
            },
            zIndex: {
                1: 1,
                2: 2,
                3: 3,
                4: 4,
                5: 5,
                6: 6,
                7: 7,
                8: 8,
                9: 9
            },
            typography: (theme) => ({
                DEFAULT: {
                    css: {
                        '--tw-prose-body': null,
                        '--tw-prose-headings': null,
                        '--tw-prose-lead': null,
                        '--tw-prose-links': null,
                        '--tw-prose-bold': null,
                        '--tw-prose-counters': null,
                        '--tw-prose-bullets': null,
                        '--tw-prose-hr': null,
                        '--tw-prose-quotes': null,
                        '--tw-prose-quote-borders': null,
                        '--tw-prose-captions': null,
                        '--tw-prose-code': null,
                        '--tw-prose-pre-code': null,
                        '--tw-prose-pre-bg': null,
                        '--tw-prose-th-borders': null,
                        '--tw-prose-td-borders': null,
                        '--tw-prose-invert-body': null,
                        '--tw-prose-invert-headings': null,
                        '--tw-prose-invert-lead': null,
                        '--tw-prose-invert-links': null,
                        '--tw-prose-invert-bold': null,
                        '--tw-prose-invert-counters': null,
                        '--tw-prose-invert-bullets': null,
                        '--tw-prose-invert-hr': null,
                        '--tw-prose-invert-quotes': null,
                        '--tw-prose-invert-quote-borders': null,
                        '--tw-prose-invert-captions': null,
                        '--tw-prose-invert-code': null,
                        '--tw-prose-invert-pre-code': null,
                        '--tw-prose-invert-pre-bg': null,
                        '--tw-prose-invert-th-borders': null,
                        '--tw-prose-invert-td-borders': null,
                        'h1, .h1': {
                            fontSize: '2em'
                        },
                        'h2, .h2': {
                            fontSize: '1.625em'
                        },
                        'h3, .h3': {
                            fontSize: '1.25em'
                        },
                        'h4, .h4': {
                            fontSize: '1.125em'
                        },
                        'h5, .h5, h6, .h6': {
                            fontSize: '1em'
                        },
                        p: {
                            fontSize: '1em'
                        },
                        a: {
                            textDecoration: 'underline',
                            transition: `color ${theme('transitionDuration.DEFAULT')} ${theme('transitionTimingFunction.DEFAULT')}`,
                            '&:hover, &:focus-visible': {
                                color: theme('colors.primary.DEFAULT')
                            },
                            code: {
                                backgroundColor: 'transparent'
                            }
                        },
                        table: {
                            fontSize: null,
                            th: {
                                fontWeight: theme('fontWeight.medium')
                            },
                            'th, td': {
                                padding: '1em'
                            },
                            'th, thead td, tfoot td': {
                                paddingTop: '.75em',
                                paddingBottom: '.75em',
                                backgroundColor: theme('colors.secondary.200')
                            },
                            thead: {
                                borderBottomColor: theme('colors.secondary.500')
                            },
                            tbody: {
                                tr: {
                                    borderBottomColor: theme('colors.secondary.500')
                                }
                            },
                            tfoot: {
                                borderTopColor: theme('colors.secondary.500')
                            },
                        }
                    },
                },
                lg: {
                    css: {
                        'h1, .h1': {
                            fontSize: '4em'
                        },
                        'h2, .h2': {
                            fontSize: '3.111111em'
                        },
                        'h3, .h3': {
                            fontSize: '2.222222em'
                        },
                        'h4, .h4': {
                            fontSize: '1.777778em'
                        },
                        'h5, .h5': {
                            fontSize: '1.777778em'
                        },
                        'h6, .h6': {
                            fontSize: '1.5em'
                        }
                    }
                }
            }),
        }
    }
});
