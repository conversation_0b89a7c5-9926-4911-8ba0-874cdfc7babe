/**
 * different styles can be found at https://tailwindcss-forms.vercel.app/
 **/
.form-datepicker,
.form-input,
.form-email,
.form-select,
.form-multiselect,
.form-textarea {
    @apply w-full bg-transparent px-5 py-3 border border-secondary rounded-3xl transition-colors;

    &::placeholder {
        @apply text-secondary-700;
    }

    &:focus {
        @apply border-secondary-700;
        box-shadow: none;
        outline: none;
    }

    &::invalid {
        @apply border-error;
    }
}

.form-select {
    @apply pr-8 cursor-pointer;
}

.form-radio {
    &:checked {
        @apply text-secondary-700;
    }
}

.form-checkbox,
.form-radio {
    &:focus:not(:focus-visible) {
        outline: none;
        box-shadow: none;
    }
    &:focus-visible {
        @apply outline-primary;
    }
    &:checked {
        @apply text-secondary-700;
    }
}

.form-file {
    @apply flex gap-4 items-start;
    @apply sm-max:flex-col;
    @apply md:items-center;

    /* hide the input file */
    input[type="file"] {
        @apply sr-only;
    }

    /* style the label as a button */
    > label {
        @apply inline-flex px-6 py-4 gap-x-2 items-center shrink-0 cursor-pointer bg-white rounded-full border border-secondary font-subheading text-sm leading-none;
    }

    /* interactive state */
    &:focus-within {
        > label {
            @apply outline outline-2 outline-primary-100;
        }
    }
}

label .required {
    @apply text-error-500;
}

@layer components {
    .no-input-spinner {
        -moz-appearance:textfield;
        
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    }
}