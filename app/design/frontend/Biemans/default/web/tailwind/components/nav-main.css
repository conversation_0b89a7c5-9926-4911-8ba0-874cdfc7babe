
#nav-icon {
    width: 18px;
    height: 14px;
    position: relative;
    transform: rotate(0deg);
    transition: .5s ease-in-out;
    cursor: pointer;

    /* bars */
    span {
        display: block;
        position: absolute;
        height: 2px;
        width: 100%;
        border-radius: 3px;
        opacity: 1;
        left: 0;
        transform: rotate(0deg);
        transition: .25s ease-in-out;
        @apply bg-white;
        
        &:nth-child(1) {
            top: 0px;
        }
        
        &:nth-child(2),
        &:nth-child(3) {
            top: 5.5px;
        }
        
        &:nth-child(4) {
            top: 11px;
        }
    }

    /* opened state */
    &.open span {
        &:nth-child(1) {
            top: 5px;
            width: 0%;
            left: 50%;
        }
        &:nth-child(2) {
            transform: rotate(45deg);
        }
        &:nth-child(3) {
            transform: rotate(-45deg);
        }
        &:nth-child(4) {
            top: 5px;
            width: 0%;
            left: 50%;
        }
    }
}