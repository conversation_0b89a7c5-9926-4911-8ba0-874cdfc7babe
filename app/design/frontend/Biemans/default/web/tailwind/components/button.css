@layer components {

    .btn {
        @apply inline-flex bg-grayscale-900 rounded-3xl px-5 py-3 text-white font-base font-medium leading-none items-center justify-center transition-colors no-underline;

        svg {
            @apply inline-flex;
        }

        span {
            vertical-align: middle;
        }

        /* interactive states */
        &:not([disabled]):not(.btn-disabled) {
            @screen lg {
                &:hover {
                    @apply bg-grayscale-700;
                }
            }
            
            &:focus-visible {
                @apply bg-grayscale-700;
            }

            &:active {
                @apply bg-grayscale-500;
            }
        }
    }

    /* primary button */
    .btn-primary {
        @apply bg-primary;

        /* interactive states */
        &:not([disabled]):not(.btn-disabled) {
            @screen lg {
                &:hover {
                    @apply bg-primary-700;
                }
            }

            &:focus-visible {
                @apply bg-primary-700;
            }
            
            &:active {
                @apply bg-primary-900;
            }
        }
    }

    /* secondary button */
    .btn-secondary {
        @apply bg-secondary-700 text-white;

        &:not([disabled]):not(.btn-disabled) {
            @screen lg {
                &:hover {
                    @apply border-grayscale-700 bg-grayscale-700 text-white;
                }
            }
            
            &:focus-visible {
                @apply border-grayscale-700 bg-grayscale-700 text-white;
            }
        }
    }
    
    /* white button */
    .btn-white {
        @apply bg-white text-black border-grayscale-300;

        &:not([disabled]):not(.btn-disabled) {
            @screen lg {
                &:hover {
                    @apply bg-grayscale-50;
                }
            }
            
            &:focus-visible {
                @apply bg-grayscale-50;
            }
        }
    }

    /* ghost button */
    .btn-ghost {
        @apply bg-transparent border border-secondary-500 text-black;
        
        /* interactive states */
        &:not([disabled]):not(.btn-disabled) {
            @screen lg {
                &:hover {
                    @apply border-grayscale-700 bg-grayscale-700 text-white;
                }
            }
            
            &:focus-visible {
                @apply border-grayscale-700 bg-grayscale-700 text-white;
            }

            &:active {
                @apply border-secondary-700 bg-secondary-700 text-white;
            }
        }
    }

    /* disabled button */
    .btn[disabled],
    .btn-disabled {
        @apply cursor-not-allowed;
    }

    .btn[disabled]:not(.btn-secondary),
    .btn-disabled:not(.btn-secondary) {
        @apply bg-grayscale-50 text-grayscale-500;
    }

    /* sizing */
    .btn-size-lg {
        @apply px-7 py-5 rounded-4xl;
    }

    .btn-size-sm {
        @apply px-3 py-1;
    }

    .btn-round {
        @apply rounded-full;
    }

    /* button with icons */
    .btn-with-icon {
        @apply relative;

        > [class*="icon"] {
            @apply absolute top-1/2 -translate-y-1/2 font-icon text-2xl-em font-normal leading-none transition-all;
        }

        /* icon right */
        &:not(.btn-with-icon--left) {
            @apply pr-11;

            > [class*="icon"] {
                @apply right-3;
            }
        }

        /* icon left */
        &--left {
            @apply pl-11;

            > [class*="icon"] {
                @apply left-3;
            }
        }
        
        /* animations */
        &--animate {
            @apply transition-all;

            /* move icon right */
            &-right {
                &:focus-visible,
                &:active {
                    > [class*="icon"] {
                        @apply translate-x-1.5;
                    }
                }
                
                @screen lg {
                    &:hover {
                        > [class*="icon"] {
                            @apply translate-x-1.5;
                        }
                    }
                }
            }

            /* move icon left */
            &-left {
                &:focus-visible,
                &:active {
                    > [class*="icon"] {
                        @apply -translate-x-1.5;
                    }
                }
                
                @screen lg {
                    &:hover {
                        > [class*="icon"] {
                            @apply -translate-x-1.5;
                        }
                    }
                }
            }
        }  
    }
}