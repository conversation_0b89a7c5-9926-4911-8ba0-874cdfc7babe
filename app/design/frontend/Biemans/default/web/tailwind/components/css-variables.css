html {

    /* font family */
    --font-base: 'Noto Sans', sans-serif;
    --font-heading: 'PT Sans Narrow', sans-serif;
    --font-subheading: 'Quicksand', sans-serif;
    --font-icon: 'icomoon';

    /* colors */
    --color-primary: var(--color-primary-500);
    --color-primary-100: #FF6D6A;
    --color-primary-300: #EA4644;
    --color-primary-500: #E42320;
    --color-primary-700: #B41614;
    --color-primary-900: #6F100F;

    --color-secondary: var(--color-secondary-500);
    --color-secondary-200: #EDECE4;
    --color-secondary-300: #cdcbbb;
    --color-secondary-400: #c7c5b3;
    --color-secondary-500: #C1BEAA;
    --color-secondary-700: #585540;

    --color-white: #fff;
    --color-black: #000;
    --color-black-rgb: 0,0,0;
    --color-grayscale-50: #E6E6E6;
    --color-grayscale-100: #B1B1B1;
    --color-grayscale-300: #888888;
    --color-grayscale-500: #5F5F5F;
    --color-grayscale-700: #1F1F1F;
    --color-grayscale-900: #000;

    --color-background-100: #EDECE4;
    --color-background-300: #C1BEAA;
    --color-background-500: #585540;

    --color-success: #D3DAD1;
    --color-success-500: #408888;
    --color-warning: #ECDFB0;
    --color-error: #EDC5C5;
    --color-error-500: #B41614;
    --color-in-stock: var(--color-success-500);
    --color-labels-red: #ec1c24;
    --color-labels-yellow: #fceb00;
    --color-out-of-stock: var(--color-error-500);

    --color-bronze: #D6855E;
    --color-silver: #9C9C9C;
    --color-gold: #B49222;

    /* backgrounds */
    --bg-bronze: linear-gradient(360deg, #88543B 0%, #D6855E 44.89%, #88543B 100%);
    --bg-silver: linear-gradient(360deg, #D3D3D3 4.58%, #9C9C9C 46.6%, #D3D3D3 98.18%);
    --bg-gold: linear-gradient(360deg, #D4BC65 15.99%, #B49222 57.39%, #ECDFB0 100%);;

    /* sizing */
    --container-padding: 1rem;
    --header-height: 9.125rem;
    --container-max-width: 100rem;

    @screen md {
        --header-height: 10rem;
    }
}
