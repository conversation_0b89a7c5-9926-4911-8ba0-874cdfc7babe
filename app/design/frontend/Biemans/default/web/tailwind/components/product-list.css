
.product-item {
    .price-container {
        @apply block;

        .price-label {
            @apply text-sm;
        }
    }

    .special-price .price-container .price-label {
        @apply sr-only;
    }

    .old-price .price-container {
        @apply text-grayscale-500;

        .price {
            @apply font-normal text-base;
        }
    }
}

.new-price {
    @apply no-underline font-normal;
}

.old-price .price-strikethrough {
    @apply text-primary;

    .price {
        @apply text-black;
    }
}
