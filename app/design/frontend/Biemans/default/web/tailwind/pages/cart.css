.checkout-cart-index {
    .columns {
        @apply p-0;

        @screen sm-max {
            @apply gap-0;

            /* product image */
            .photo.product-image-photo {
                @apply object-contain max-h-40;
            }
        }
    }

    /* item message error */
    .item.message.error,
    .item.message.notice {
        @apply text-white bg-out-of-stock;
    }

    /* personalization */
    .custom_text {
        @apply leading-tight;

        .row {
            @apply block pb-1 mb-1 border-b border-b-secondary-500;
        }
    }
}