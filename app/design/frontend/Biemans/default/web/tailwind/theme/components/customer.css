.account {
    
    .columns .sidebar {
        @screen sm-max {
            @apply order-none col-span-2;
        }
        @screen md {
            @apply row-span-full shadow-x-outline-secondary-500;
        }
    }
    
    .columns {
        @apply gap-x-0;
        @screen sm-max {
            @apply flex flex-col gap-y-8;
        }
    }

    &:not(.wishlist-index-index) {
        .column.main {
            @screen md {
                @apply pl-8;
            }
        }
    }

    .column.main {
        @screen md {
            @apply pt-4;
        }
    }
    
    .nav.items {
        @apply sm-max:mt-4;

        .nav.item {
            @apply py-1.5 font-medium;

            a {
                @apply transition-colors;
                
                &:focus-visible {
                    @apply text-secondary-700;
                }
            }

            strong {
                @apply text-secondary-700 underline;
            }
        }
    }

    /* wishlist page */
    &.wishlist-index-index {
        .column.main {
            @apply pt-0;
        }
    }
}

.actions-toolbar {
    @apply mt-6 pt-4 flex justify-between flex-row-reverse items-center
}


@layer components {
    .actions-toolbar .primary button {
        @apply btn btn-primary;
    }
}
