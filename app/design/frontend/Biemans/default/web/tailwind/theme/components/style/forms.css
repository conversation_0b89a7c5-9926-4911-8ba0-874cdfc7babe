form,
fieldset {
    .field {
        @apply mt-1
    }

    .field.field-reserved ul:last-of-type {
        @apply -mb-6 pb-1 /* The sum has to match the value set above for field.field-reserved */
    }

    .field.field-reserved ul {
        @apply text-sm
    }

    label {
        @apply block mb-2 font-medium leading-none cursor-pointer;
    }

    .field.choice {
        @apply flex items-center
    }

    .field.choice input {
        @apply mr-4
    }

    .field.choice label {
        @apply mb-0
    }

    .field.field-error .messages {
        @apply text-primary;
        max-width: fit-content;
    }

    legend {
        @apply text-xl mb-3
    }

    legend + br {
        @apply hidden
    }
}

fieldset ~ fieldset {
    @apply mt-8
}
