<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var Product $product */
if ($block->getItem()) {
    $product = $block->getItem();
} else {
    $product = $currentProduct->get();
}
?>

<?= $block->getChildHtml('', false) ?>

<button
    type="submit"
    form="product_addtocart_form_<?= $product->getId(); ?>"
    class="add-to-cart btn btn-primary"
>
    <span class="whitespace-nowrap">
        <?= /** @phpstan-ignore-line */ $block->getData('is_cart_configure') ?
        $escaper->escapeHtml(__('Update item')) :
        $escaper->escapeHtml(__('Add to cart')) ?>
    </span>
</button>
