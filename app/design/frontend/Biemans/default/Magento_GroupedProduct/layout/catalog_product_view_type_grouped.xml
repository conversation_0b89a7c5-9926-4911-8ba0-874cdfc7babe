<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product.info.options.grouped">
            <block class="Magento\Catalog\Block\Product\View" name="product.grouped.addtocart"
                   template="Magento_GroupedProduct::product/view/addtocart.phtml"/>
            <block name="biemans.add.configurations" ifconfig="biemans_configurator/general/enable"
                   template="Biemans_Configurator::catalog/product/add-configurations.phtml"/>
            <block name="product.grouped.addtowishlist" template="Magento_Catalog::product/view/addtowishlist.phtml"/>
            <block name="product.grouped.alert.stock" template="Magento_ProductAlert::product/stock.phtml" />
            <block name="product.grouped.login.note" template="Biemans_GuestRestrictions::product/login-note.phtml" />
            <block name="product.grouped.next.tier.price" template="Magento_Catalog::product/next-tier-price-message.phtml">
                <arguments>
                    <argument name="product_type" xsi:type="string">grouped</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
