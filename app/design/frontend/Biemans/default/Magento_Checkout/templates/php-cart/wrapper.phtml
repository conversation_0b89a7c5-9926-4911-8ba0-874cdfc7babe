<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Modal;
use Magento\Framework\Escaper;
use Magento\Checkout\ViewModel\Cart as CartViewModel;

/** @var ViewModelRegistry $viewModels */
/** @var CartViewModel $cartViewModel */
/** @var Modal $modalViewModel */
/** @var Escaper $escaper */

$cartViewModel = $viewModels->require(CartViewModel::class);
$modalViewModel = $viewModels->require(Modal::class);
?>

<div class="flex w-full md-max:flex-col">
    <div class="xl:min-w-[23rem] xl:p-8 bg-secondary-200 sm-max:sticky sm-max:bottom-0 z-1 sm-max:shadow-[0_-2px_52px_rgba(0,0,0,0.07)] md:shadow-outline-secondary-500">
        <div class="container py-container-padding md:sticky md:top-0">
            <h2 class="pb-2 mb-2 border-b border-secondary-500">
                <?= $escaper->escapeHtml(__('Cart total')) ?>
            </h2>
            <?= $block->getChildHtml('cart.summary') ?>

            <?php if ($cartViewModel->isClearShoppingCartEnabled()): ?>
                <div class="cart actions flex flex-col sm:flex-row gap-4 items-center my-4">
                    <script>
                        function initClearShoppingCartModal() {
                            return Object.assign(
                                hyva.modal(),
                                {
                                    postData: {
                                        action: '<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>',
                                        data: {update_cart_action: 'empty_cart'}
                                    }
                                }
                            );
                        }
                    </script>
                    <div x-data="initClearShoppingCartModal()" class="w-full">
                        <?= /** @noEscape */ /** @phpstan-ignore-line */ ($confirmation = $modalViewModel
                            ->confirm(__('Are you sure?'))
                            ->withDetails(__('Are you sure you want to remove all items from your shopping cart?'))
                        ) ?>
                        <button @click="<?= /** @noEscape */ $confirmation->getShowJs() ?>.then(result => result && hyva.postForm(postData))"
                            type="button" class="action clear btn w-full min-h-button" id="empty_cart_button">
                            <span><?= $escaper->escapeHtml(__('Clear Shopping Cart')) /** @phpstan-ignore-line */ ?></span>
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <div class="">
                <?= $block->getChildHtml('cart.discount') ?>
            </div>
        </div>
    </div>
    <div class="-order-1 md:grow">
        <?= $block->getChildHtml('form_before') ?>
        <?= $block->getChildHtml('cart-items') ?>
        <?= $block->getChildHtml('checkout_cart_widget') ?>
    </div>
</div>
