<?php

use Magento\Checkout\Block\Onepage\Link;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Link $block */
?>
<?php if ($block->isPossibleOnepageCheckout()): ?>
    <a @click.prevent.stop="$dispatch('toggle-authentication',
        {url: '<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($block->getCheckoutUrl())) ?>'});"
        href="<?= $escaper->escapeHtmlAttr($escaper->escapeUrl($block->getCheckoutUrl())) ?>"
        title="<?= $escaper->escapeHtmlAttr(__('Proceed to Checkout')) ?>"
        class="btn btn-primary w-full min-h-button mt-4"
        id="checkout-link-button"
    >
        <?= $escaper->escapeHtml(__('Proceed to Checkout')) /** @phpstan-ignore-line */ ?>
    </a>
<?php endif ?>
