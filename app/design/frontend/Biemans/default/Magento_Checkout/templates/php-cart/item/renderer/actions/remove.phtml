<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove;
use Magento\Framework\Escaper;

/** @var Remove $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);

?>
<button
   class="action action-delete btn btn-secondary aspect-square rounded-full p-0 min-w-[2.25rem] text-xl leading-none"
   x-data="{}"
   @click.prevent='hyva.postForm(<?= /* @noEscape */ $block->getDeletePostJson() ?>)'
>
    <span class="icon-close" aria-hidden="true"></span>
    <span class="sr-only"><?= $escaper->escapeHtml(__('Remove item')) /** @phpstan-ignore-line */ ?></span>
</button>
