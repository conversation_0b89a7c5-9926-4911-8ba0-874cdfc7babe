<?php

declare(strict_types=1);

// phpcs:disable Magento2.Files.LineLength.MaxExceeded

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductStockItem;
use Magento\Checkout\Block\Cart\Item\Renderer;
use Magento\Framework\Escaper;

/** @var Renderer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

$item = $block->getItem();
$product = $item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();

/** @var ProductStockItem $stockItemViewModel */
$stockItemViewModel = $viewModels->require(ProductStockItem::class);
$step = (int)($stockItemViewModel->getQtyIncrements($product) ?: 1);

$button_cls = 'w-7 aspect-square absolute top-1/2 -translate-y-1/2 rounded-full bg-secondary-200 text-grayscale-700 font-medium
    leading-none transition-colors hover:bg-secondary-700 focus-visible:bg-secondary-700 hover:text-white focus-visible:text-white';
?>
<div class="cart item shadow-outline-secondary-500">
    <?php if ($messages = $block->getMessages()): ?>
        <div>
            <?php foreach ($messages as $message): ?>
                <div class="cart item message w-full p-2 font-medium px-container-padding py-4 <?= $escaper->escapeHtmlAttr($message['type']) ?>">
                    <div><?= $escaper->escapeHtml($message['text']) /** @phpstan-ignore-line */ ?></div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <div class="flex items-start justify-between gap-3 px-3 py-3 640-max:flex-col">
        <div class="flex gap-x-4 sm-max:flex-wrap sm-max:gap-3">
            <div class="product photo product-item-photo max-w-[3.25rem]">
                <div class="flex justify-center p-1 bg-secondary-200">
                    <?php if ($block->hasProductUrl()): ?>
                        <a href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>"
                        tabindex="-1"
                        class="product-item-photo">
                    <?php else: ?>
                        <span class="product-item-photo">
                    <?php endif;?>
                    <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail') /** @phpstan-ignore-line */
                        ->setTemplate('Magento_Catalog::product/image.phtml')
                        ->toHtml() ?>
                    <?php if ($block->hasProductUrl()): ?>
                        </a>
                    <?php else: ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="flex flex-col gap-1 min-w-[8rem]">
                <?php if ($block->hasProductUrl()): ?>
                    <a class="flex flex-col gap-1" href="<?= $escaper->escapeUrl($block->getProductUrl()) ?>">
                        <small class="text-sm font-medium">
                            <?= $escaper->escapeHtml(__('SKU')) /** @phpstan-ignore-line */ ?>
                        </small>
                        <span class="flex items-center h-8"><?= $item->getSku() ?></span>
                    </a>
                <?php else: ?>
                    <div class="flex flex-col gap-1">
                        <small class="text-sm font-medium">
                            <?= $escaper->escapeHtml(__('SKU')) /** @phpstan-ignore-line */ ?>
                        </small>
                        <span class="flex items-center h-8"><?= $item->getSku() ?></span>
                    </div>
                <?php endif; ?>
                <?php if ($options = $block->getOptionList()): ?>
                    <div class="flex flex-col gap-2">
                        <div x-data="{open: false}">
                            <button type="button" @click="open=!open" class="flex flex-row items-center">
                                <small class="text-sm font-medium"><?= $escaper->escapeHtml(__('See Details')) /** @phpstan-ignore-line */ ?></small>
                                <span class="inline-block text-2xl leading-none transition-transform icon-chev-down" :class="open ? 'rotate-180' : ''" aria-hidden="true"></span>
                            </button>

                            <div x-show="open" x-cloak>
                                <dl class="w-full item-options">
                                    <?php foreach ($options as $option): ?>
                                        <?php $formatedOptionValue = $block->getFormatedOptionValue($option) ?>
                                        <dt class="my-2 text-sm font-medium"><?= $escaper->escapeHtml($option['label']) /** @phpstan-ignore-line */ ?>:</dt>
                                        <dd class="">
                                            <?php if (isset($formatedOptionValue['full_view'])): ?>
                                                    <?= $escaper->escapeHtml($formatedOptionValue['full_view'], ['span', 'a', 'br']) /** @phpstan-ignore-line */ ?>
                                                <?php else: ?>
                                                    <?= $escaper->escapeHtml($formatedOptionValue['value'], ['span', 'a', 'br']) /** @phpstan-ignore-line */ ?>
                                            <?php endif; ?>
                                        </dd>
                                    <?php endforeach; ?>
                                </dl>
                            </div>
                        </div>
                    </div>
                <?php endif;?>
            </div>
            <div class="flex gap-4">
                <div class="flex flex-col gap-1">
                    <small class="text-sm font-medium">
                        <?= $escaper->escapeHtml(__('Price')) /** @phpstan-ignore-line */ ?>
                    </small>
                    <span class="flex items-center h-8"><?= $block->getUnitPriceHtml($item) ?></span>
                </div>
                <div class="flex flex-col gap-1" x-data="{qty: <?= $block->getQty() ?>}">
                    <label class="!mb-0 text-sm" for="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty">
                        <?= $escaper->escapeHtml(__('Qty')) /** @phpstan-ignore-line */ ?>
                    </label>
                    <div class="qty-input-wrapper flex h-8 w-[7.25rem] justify-center relative border border-secondary-200 rounded-3xl transition-colors">
                        <button type="button"
                                x-on:click="qty = Math.max(<?= $step ?>, qty - <?= $step ?>);
                                    $refs.qtyInput.value = qty;
                                    $refs.qtyInput.dispatchEvent(new Event('change', { qty: qty }))"
                                class="<?= $button_cls ?> left-px"
                        >
                            <span class="sr-only"><?= $escaper->escapeHtml(__('Decrease by 1')) /** @phpstan-ignore-line */ ?></span>
                            <span>-</span>
                        </button>
                        <input
                            id="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty"
                            name="cart[<?= $escaper->escapeHtmlAttr($item->getId()) ?>][qty]"
                            value="<?= $block->getQty() ?>"
                            type="number"
                            size="4"
                            step="<?= $step ?>"
                            title="<?= $escaper->escapeHtmlAttr(__('Qty')) /** @phpstan-ignore-line */ ?>"
                            class="qty h-full max-w-[2.5rem] p-0 text-center bg-transparent border-none no-input-spinner"
                            required="required"
                            min="<?= $step ?>"
                            data-role="cart-item-qty"
                            x-ref="qtyInput"
                            x-model.number="qty"
                        />
                        <button type="button"
                                x-on:click="qty = Math.max(<?= $step ?>, qty + <?= $step ?>);
                                    $refs.qtyInput.value = qty;
                                    $refs.qtyInput.dispatchEvent(new Event('change', { qty: qty }))"
                                class="<?= $button_cls ?> right-px"
                        >
                            <span class="sr-only"><?= $escaper->escapeHtml(__('Increase by 1')) /** @phpstan-ignore-line */ ?></span>
                            <span>+</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex items-center justify-between 640-max:w-full gap-x-10">
            <div class="flex flex-col gap-1">
                <small class="text-sm font-medium">
                    <?= $escaper->escapeHtml(__('Subtotal')) /** @phpstan-ignore-line */ ?>
                </small>
                <span class="flex items-center h-8"><?= $block->getRowTotalHtml($item) ?></span>
            </div>
            <div class="flex items-center justify-end item-actions gap-2 min-w-[5rem]">
                <?= /* @noEscape */ $block->getActions($item) ?>
            </div>
        </div>
    </div>
    <?php if ($outletNote = $item->getData('outlet_notification')): ?>
        <div class="px-3 pb-3">
            <?php
            /**
             * DO NOT USE 'cart item message' CLASS HERE, AS CHECKOUT BUTTON WILL NOT WORK
             * @see app/design/frontend/Biemans/default/Magento_Customer/templates/account/authentication-popup.phtml :: redirectIfAuthenticated()
             */
            ?>
            <div class="product-label inline-block px-3 py-1.5 border-2 bg-labels-yellow border-labels-red text-labels-red rounded-3xl xl:text-lg !leading-none font-heading white-space-nowrap uppercase">
                <?=  __('Outlet') ?>
            </div>
        </div>
    <?php endif; ?>
</div>
