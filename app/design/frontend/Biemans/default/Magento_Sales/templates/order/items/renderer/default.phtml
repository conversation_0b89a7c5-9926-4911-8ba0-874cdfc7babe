<?php

declare(strict_types=1);

use Hyva\Theme\Model\LocaleFormatter;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer;

/** @var DefaultRenderer $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */

/** @var \Magento\Sales\Model\Order\Item $item */
$item = $block->getItem();
$cols = $block->getData('is_context_shipment') ? 3 : ($block->getData('is_context_creditmemo') ? 7 : 5);
?>
<td>
    <span class="font-semibold"><?= $escaper->escapeHtml($item->getName()) /** @phpstan-ignore-line */ ?></span>
    <div class="item-options mt-2">
        <div class="text-sm flex">
            <span><?= $escaper->escapeHtml(__('Sku')) /** @phpstan-ignore-line */ ?>:</span>
            <span class="text-sm ml-1"><?= /* @noEscape */ $block->prepareSku($block->getSku()) ?></span>
        </div>
        <?php if ($options = $block->getItemOptions()): ?>
            <?php foreach ($options as $option): ?>
                <div class="text-sm flex mt-1">
                    <span><?= $escaper->escapeHtml($option['label']) /** @phpstan-ignore-line */ ?>:</span>
                    <?php if (!$block->getPrintStatus()): ?>
                        <span class="ml-1">
                            <?= $escaper->escapeHtml($option['value'], ['a', 'span', 'br']) /** @phpstan-ignore-line */ ?>
                        </span>
                    <?php else: ?>
                        <span class="ml-1">
                            <?= /** @phpstan-ignore-line */ $escaper->escapeHtml(
                                (isset($option['print_value']) ? $option['print_value'] : $option['value'])
                            ) ?>
                        </span>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
        <?php /* downloadable */ ?>
        <?php if ($links = $block->getLinks()): ?>
            <div class="item options my-2">
                <p><?= $escaper->escapeHtml($block->getLinksTitle()) /** @phpstan-ignore-line */ ?></p>
                <?php foreach ($links->getPurchasedItems() as $link): ?>
                    <p class="ml-1"><?= $escaper->escapeHtml($link->getLinkTitle()) /** @phpstan-ignore-line */ ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <?php
            /** @var \Magento\Framework\View\Element\Template|null $addInfoBlock */
            $addInfoBlock = $block->getProductAdditionalInformationBlock();
        ?>
        <?php if ($addInfoBlock): ?>
            <?= $addInfoBlock->setItem($item)->toHtml() ?>
        <?php endif; ?>
        <?= $escaper->escapeHtml($item->getDescription()) /** @phpstan-ignore-line */ ?>
    </div>
</td>
<td data-label="<?= $escaper->escapeHtml(__('Price')) /** @phpstan-ignore-line */ ?>">
    <?php if (!$block->getData('is_context_shipment')): ?>
        <div>
            <?= $block->getItemPriceHtml() ?>
        </div>
    <?php endif; ?>
</td>
<td
    data-label="
        <?php if ($block->getData('is_context_invoice')): ?>
            <?= $escaper->escapeHtml(__('Qty Invoiced')) /** @phpstan-ignore-line */ ?>
        <?php elseif ($block->getData('is_context_shipment')): ?>
            <?= $escaper->escapeHtml(__('Qty Shipped')) /** @phpstan-ignore-line */ ?>
        <?php else: ?>
            <?= $escaper->escapeHtml(__('Qty')) /** @phpstan-ignore-line */ ?>
        <?php endif; ?>
    "
    class="lg:text-right"
>
    <div>
        <?php if ($block->getData('is_context_invoice') || $block->getData('is_context_creditmemo')): ?>
            <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($item->getQty()) ?></span>
        <?php endif; ?>
        <?php if ($block->getData('is_context_shipment')): ?>
            <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($item->getQty()) ?></span>
        <?php endif; ?>
        <?php if ($block->getData('is_context_order')): ?>
            <?php if ($item->getQtyOrdered() > 0): ?>
                <p class="!my-0">
                    <span class="title"><?= $escaper->escapeHtml(__('Ordered')) /** @phpstan-ignore-line */ ?>:</span>
                    <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($item->getQtyOrdered()) ?></span>
                </p>
            <?php endif; ?>
            <?php if ($item->getQtyShipped() > 0): ?>
                <p class="!my-0">
                    <span class="title"><?= $escaper->escapeHtml(__('Shipped')) /** @phpstan-ignore-line */ ?>:</span>
                    <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($item->getQtyShipped()) ?></span>
                </p>
            <?php endif; ?>
            <?php if ($item->getQtyCanceled() > 0): ?>
                <p class="!my-0">
                    <span class="title"><?= $escaper->escapeHtml(__('Canceled')) /** @phpstan-ignore-line */ ?>:</span>
                    <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($item->getQtyCanceled()) ?></span>
                </p>
            <?php endif; ?>
            <?php if ($item->getQtyRefunded() > 0): ?>
                <p class="!my-0">
                    <span class="title"><?= $escaper->escapeHtml(__('Refunded')) /** @phpstan-ignore-line */ ?>:</span>
                    <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($item->getQtyRefunded()) ?></span>
                </p>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</td>
<td data-label="<?= $escaper->escapeHtml(__('Subtotal')) /** @phpstan-ignore-line */ ?>" class="lg:text-right">
    <div>
        <?php if (!$block->getData('is_context_shipment')): ?>
            <div class="flex justify-between lg:block">
                <?= $block->getItemRowTotalHtml() ?>
            </div>
        <?php endif; ?>
        <?php if ($block->getData('is_context_creditmemo')): ?>
            <div class="flex justify-between lg:block">
                <p class="lg:hidden my-0 font-medium"><?= $escaper->escapeHtml(__('Discount Amount')) /** @phpstan-ignore-line */ ?></p>
                <?= /* @noEscape */ $block->getOrder()->formatPrice($item->getDiscountAmount()) ?>
            </div>
            <div class="flex justify-between lg:block">
                <p class="lg:hidden my-0 font-medium"><?= $escaper->escapeHtml(__('Row Total')) /** @phpstan-ignore-line */ ?></p>
                <?= $block->getItemRowTotalAfterDiscountHtml() ?>
            </div>
        <?php endif; ?>
    </div>
</td>
