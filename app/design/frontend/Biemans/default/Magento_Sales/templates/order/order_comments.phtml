<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\View;

/** @var View $block  */
/** @var Escaper $escaper */
?>

<?php $history = $block->getOrder()->getVisibleStatusHistory() ?>
<?php if (!empty($history)): ?>
    <div class="my-6 prose">
        <?php foreach ($history as $historyItem): ?>
            <div class="my-3 pl-3 py-1 border-l-2 border-primary" style="margin-left: 2px">
                <p class="text-grayscale-700"><?= $escaper->escapeHtml($historyItem->getComment(), ['b', 'br', 'strong', 'i', 'u', 'a']) /** @phpstan-ignore-line */ ?></p>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif;
