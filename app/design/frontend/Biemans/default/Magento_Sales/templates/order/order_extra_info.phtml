<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\View;

/** @var View $block  */
/** @var Escaper $escaper */

$order = $block->getOrder();
?>
<div class="my-6 prose">
    <h2 class="!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('About Your Order')) /** @phpstan-ignore-line */ ?></h2>

    <div class="my-3 pl-3 py-1" style="margin-left: 2px">
        <?php if ($orderReference = $order->getOrderReference()): ?>
            <p class="text-grayscale-700">
                <strong><?= $escaper->escapeHtml(__('Reference name')) /** @phpstan-ignore-line */ ?>:</strong>
                <span><?= $escaper->escapeHtml($orderReference); /** @phpstan-ignore-line */ ?></span>
            </p>
        <?php endif; ?>

        <?php if ($orderEmail = $order->getOrderEmail()): ?>
            <p class="text-grayscale-700">
                <strong><?= $escaper->escapeHtml(__('E-mail')) /** @phpstan-ignore-line */ ?>:</strong>
                <span><?= $escaper->escapeHtml($orderEmail); /** @phpstan-ignore-line */ ?></span>
            </p>
        <?php endif; ?>
    </div>
</div>
