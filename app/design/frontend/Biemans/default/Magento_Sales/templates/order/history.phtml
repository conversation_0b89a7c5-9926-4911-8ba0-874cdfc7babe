<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\History;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var History $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
/** @var \Magento\Sales\Model\ResourceModel\Order\Collection|false $orders */
$orders = $block->getOrders();
?>
<?= $block->getChildHtml('info') ?>
<?php if ($orders && $orders->count()): ?>
    <div class="prose lg:prose-lg md:max-w-none">
        <table>
            <thead>
                <tr>
                    <th>
                        <?= $escaper->escapeHtml(__('Order number')) /** @phpstan-ignore-line */ ?>
                    </th>
                    <th>
                        <?= $escaper->escapeHtml(__('Date')) /** @phpstan-ignore-line */ ?>
                    </th>
                    <th class="md-max:hidden">
                        <?= $escaper->escapeHtml(__('Ship To')) /** @phpstan-ignore-line */ ?>
                    </th>
                    <th class="md-max:hidden">
                        <?= $escaper->escapeHtml(__('Order Total')) /** @phpstan-ignore-line */ ?>
                    </th>
                    <th>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml(__('View')) /** @phpstan-ignore-line */ ?>
                            <?php if ($this->helper(\Magento\Sales\Helper\Reorder::class)->isAllow()): ?>
                                /<?= $escaper->escapeHtml(__('Reorder')) /** @phpstan-ignore-line */ ?>
                            <?php endif; ?>
                        </span>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php $i = 0; ?>
                <?php foreach ($orders as $order): ?>
                    <?php
                        $i++;
                        $order_link = $escaper->escapeUrl($block->getViewUrl($order));
                        /** @var string $order_date */
                        $order_date = $escaper->escapeHtml($block->formatDate($order->getCreatedAt()));
                    ?>

                    <tr class="relative isolate">
                        <td>
                            <span class="prose">
                                <a href="<?= $order_link ?>" class="after:absolute after:inset-0">
                                    <span class="sr-only"><?= $escaper->escapeHtmlAttr(__('View Order')) /** @phpstan-ignore-line */ ?> </span>
                                    <?= $escaper->escapeHtml($order->getRealOrderId()) /** @phpstan-ignore-line */ ?>
                                </a>
                            </span>
                        </td>
                        <td>
                            <time class="whitespace-nowrap" datetime="<?= $order_date ?>">
                                <?= $order_date ?>
                            </time>
                        </td>
                        <td class="md-max:hidden">
                            <?= $order->getBillingAddress() ? $escaper->escapeHtml($order->getBillingAddress()->getCompany()) : "&nbsp;" /** @phpstan-ignore-line */ ?>
                        </td>
                        <td class="md-max:hidden">
                            <?= /* @noEscape */$order->formatPrice($order->getGrandTotal()) ?>
                        </td>
                        <td>
                            <div class="inline-block text-sm underline">
                                <?= $heroicons->eyeHtml() ?>
                            </div>
                            <?php if ($this->helper(\Magento\Sales\Helper\Reorder::class)->canReorder($order->getEntityId())): ?>
                                <?php $formData = json_decode(
                                    $this->helper(\Magento\Framework\Data\Helper\PostHelper::class)->getPostData(
                                        $block->getReorderUrl($order)
                                    ),
                                    true
                                ) ?>
                                <form action="<?= /* @noEscape */ $formData['action'] ?>" method="post"
                                    class="inline-flex relative z-1 items-center">
                                    <?= $block->getBlockHtml('formkey'); ?>
                                    <input type="hidden" name="data"
                                        value="<?= $escaper->escapeHtmlAttr(json_encode($formData['data'])) /** @phpstan-ignore-line */ ?>"
                                    />
                                    <button
                                        type="submit"
                                        class="inline-block transition-colors hover:text-primary"
                                        title="<?= $this->escapeHtmlAttr(__('Re-order')) ?>"
                                        aria-label="<?= $this->escapeHtmlAttr(__('Re-order')) ?>"
                                    >
                                        <?= preg_replace(
                                            '/<title>.*?<\/title>/',
                                            '',
                                            $heroicons->refreshHtml()
                                        ) ?>
                                    </button>
                                </form>
                            <?php endif ?>
                        </td>
                    </tr>
                <?php endforeach; ?>

            </tbody>
        </table>
    </div>
    <?php if ($block->getPagerHtml()): ?>
        <div class="order-products-toolbar toolbar bottom"><?= $block->getPagerHtml() ?></div>
    <?php endif ?>
<?php else: ?>
    <div class="message info empty"><span><?= $escaper->escapeHtml($block->getEmptyOrdersMessage()) /** @phpstan-ignore-line */ ?></span></div>
<?php endif ?>
