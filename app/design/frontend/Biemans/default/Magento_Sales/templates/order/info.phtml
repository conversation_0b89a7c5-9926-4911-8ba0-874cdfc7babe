<?php

declare(strict_types=1);

/**
 * @var \Magento\Sales\Block\Order\Info $block
 * @var \Magento\Framework\Escaper $escaper
 */ ?>
<?php $order = $block->getOrder() ?>
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2 md:gap-4 prose max-w-none">
    <div class="w-full py-4">
        <h2 class="!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Billing Address')) /** @phpstan-ignore-line */ ?></h2>
        <div class="box-content">
            <address><?= /* @noEscape */ $block->getFormattedAddress($order->getBillingAddress()) /** @phpstan-ignore-line */ ?></address>
        </div>
    </div>
    <?php if (!$order->getIsVirtual()): ?>
        <div class="w-full py-4">
            <h2 class="!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Shipping Address')) /** @phpstan-ignore-line */ ?></h2>
            <div class="box-content">
                <address><?= /* @noEscape */ $block->getFormattedAddress($order->getShippingAddress()) ?></address>
            </div>
        </div>
        <div class="w-full py-4">
            <h2 class="!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Shipping Method')) /** @phpstan-ignore-line */ ?></h2>
            <div class="box-content">
                <?php if ($order->getShippingDescription()): ?>
                    <?= $escaper->escapeHtml($order->getShippingDescription()) /** @phpstan-ignore-line */ ?>
                <?php else: ?>
                    <?= $escaper->escapeHtml(__('No shipping information available')) /** @phpstan-ignore-line */ ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
