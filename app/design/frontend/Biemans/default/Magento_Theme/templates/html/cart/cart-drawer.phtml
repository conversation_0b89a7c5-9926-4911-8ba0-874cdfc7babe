<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);

$maxItemsToDisplay = $storeConfig->getStoreConfig('checkout/sidebar/max_items_display_count');

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<script>
    function initCartDrawer() {
        return {
            open: false,
            isLoading: false,
            cart: {},
            maxItemsToDisplay: <?= (int) $maxItemsToDisplay ?>,
            itemsCount: 0,
            totalCartAmount: 0,
            getData(data) {
                if (data.cart) {
                    this.cart = data.cart;
                    this.itemsCount = data.cart.items && data.cart.items.length || 0;
                    this.totalCartAmount = this.cart.summary_count;
                    this.setCartItems();
                }
                this.isLoading = false;
            },
            cartItems: [],
            getItemCountTitle() {
                return hyva.strf('(%0 <?= $escaper->escapeJs(__('of')) /** @phpstan-ignore-line */ ?> %1)', this.maxItemsToDisplay, this.itemsCount)
            },
            setCartItems() {
                this.cartItems = this.cart.items && this.cart.items.sort((a, b) => b.item_id - a.item_id) || [];

                if (this.maxItemsToDisplay > 0) {
                    this.cartItems = this.cartItems.slice(0, parseInt(this.maxItemsToDisplay, 10));
                }
            },
            deleteItemFromCart(itemId) {
                this.isLoading = true;

                const formKey = hyva.getFormKey();
                const postUrl = BASE_URL + 'checkout/sidebar/removeItem/';

                fetch(postUrl, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key=" + formKey + "&item_id=" + itemId,
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(response => {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        window.dispatchMessages && window.dispatchMessages([{
                            type: 'warning',
                            text: '<?= $escaper->escapeJs(__('Could not remove item from quote.')) /** @phpstan-ignore-line */ ?>'
                        }]);
                        this.isLoading = false;
                    }
                }).then(result => {
                    window.dispatchMessages && window.dispatchMessages([{
                        type: result.success ? 'success' : 'error',
                        text: result.success
                            ? '<?= $escaper->escapeJs(__('You removed the item.')) /** @phpstan-ignore-line */ ?>'
                            : result.error_message
                    }], result.success ? 5000 : 0)
                    window.dispatchEvent(new CustomEvent('reload-customer-section-data'));
                });
            }
        }
    }
</script>
<section id="cart-drawer"
         x-data="initCartDrawer()"
         @private-content-loaded.window="getData($event.detail.data)"
         @toggle-cart.window="open=true"
         @keydown.window.escape="open=false"
>
    <template x-if="cart && cart.summary_count">
        <div role="dialog"
             aria-labelledby="cart-drawer-title"
             aria-modal="true"
             @click.outside="open=false"
             class="fixed inset-y-0 right-0 z-30 flex max-w-full">
            <div class="cursor-pointer backdrop"
                 x-show="open"
                 x-transition:enter="ease-in-out duration-500"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-20"
                 x-transition:leave="ease-in-out duration-500"
                 x-transition:leave-start="opacity-20"
                 x-transition:leave-end="opacity-0"
                 @click="open=false"
            ></div>
            <div class="relative w-screen max-w-md shadow-2xl isolate"
                 x-show="open"
                 x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:enter-start="translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="translate-x-full"
            >
                <div class="flex flex-col h-full bg-white shadow-xl">
                    <?= $block->getChildHtml('cart-drawer.top'); ?>

                    <div class="flex items-center py-8 px-container-padding gap-x-4 z-1 shadow-panel-bottom">
                        <h2 id="cart-drawer-title" class="h1 md:h2 lg:h3">
                            <?= $escaper->escapeHtml(__('My Cart')) /** @phpstan-ignore-line */ ?>
                        </h2>
                        <div
                            x-show="open"
                            x-transition:enter="ease-in-out duration-500"
                            x-transition:enter-start="opacity-0"
                            x-transition:enter-end="opacity-100"
                            x-transition:leave="ease-in-out duration-500"
                            x-transition:leave-start="opacity-100"
                            x-transition:leave-end="opacity-0"
                            class="flex ml-auto"
                        >
                            <button @click="open = false;" class="min-w-button min-h-button">
                                <span class="sr-only"><?= $escaper->escapeHtml(__('Close panel')) /** @phpstan-ignore-line */ ?></span>
                                <span class="text-3xl leading-none icon-close" aria-hidden="true"></span>
                            </button>
                        </div>
                    </div>

                    <?= $block->getChildHtml('cart-drawer.items.before'); ?>

                    <div class="relative flex flex-col flex-grow py-8 overflow-y-auto bg-white isolate">
                        <template x-for="item in cartItems">
                            <div class="flex items-start pb-4 mb-4 border-b last:mb-0 last:border-b-0 border-secondary">
                                <div class="relative flex items-start w-full px-container-padding gap-x-10">
                                    <div class="w-20 h-20 px-2.5 py-3.5 bg-secondary-200">
                                        <img
                                            :src="item.product_image.src"
                                            :width="item.product_image.width"
                                            :height="item.product_image.height"
                                            alt=""
                                            loading="lazy"
                                        />
                                    </div>
                                    <div class="flex-grow">
                                        <h3 class="mb-1 h2 md:h3 lg:h4" x-html="item.product_name"></h3>
                                        <p class="text-lg leading-none text-grayscale-500" x-html="item.product_sku"></p>
                                        <template x-if="item.options.length">
                                            <div x-data="{open: false}">
                                                <button type="button" @click="open=!open" class="flex flex-row items-center mt-2" aria-controls="product-details">
                                                    <small class="text-sm font-medium"><?= $escaper->escapeHtml(__('See Details')) /** @phpstan-ignore-line */ ?></small>
                                                    <span class="inline-block text-2xl leading-none transition-transform icon-chev-down" :class="open ? 'rotate-180' : ''" aria-hidden="true"></span>
                                                </button>

                                                <div x-show="open" x-cloak id="product-details">
                                                    <div class="w-full item-options">
                                                        <template x-for="option in item.options">
                                                            <div>
                                                                <div class="my-2 text-sm font-medium" x-text="option.label + ':'"></div>
                                                                <div x-html="option.value"></div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <div class="flex flex-wrap pt-4">
                                            <div class="text-sm">
                                                <span class="font-medium"><?= $escaper->escapeHtml(__('Quantity')) /** @phpstan-ignore-line */ ?>: </span>
                                                <span x-html="item.qty"></span>
                                            </div>
                                            <button class="relative ml-auto text-xs underline transition-colors z-1 text-secondary-700 hover:text-black focus-visible:text-black"
                                                    @click="deleteItemFromCart(item.item_id)"
                                            >
                                                <?= $escaper->escapeHtml(__('Delete item')) /** @phpstan-ignore-line */ ?>
                                            </button>
                                        </div>
    
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <?= $block->getChildHtml('cart-drawer.totals.before'); ?>

                    <div class="relative bg-white p-container-padding shadow-panel-top">
                        <div class="flex flex-wrap items-center w-full gap-x-2">
                            <a @click.prevent.stop="$dispatch('toggle-authentication',
                                {url: '<?= $escaper->escapeUrl($block->getUrl('checkout')) /** @phpstan-ignore-line */ ?>'});"
                               href="<?= $escaper->escapeUrl($block->getUrl('checkout')) /** @phpstan-ignore-line */ ?>"
                               class="inline-flex btn btn-primary">
                                <?= $escaper->escapeHtml(__('Checkout')) /** @phpstan-ignore-line */ ?>
                            </a>
                            <span><?= $escaper->escapeHtml(__('or')) /** @phpstan-ignore-line */ ?></span>
                            <a href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart')) /** @phpstan-ignore-line */ ?>" class="font-medium underline">
                                <?= $escaper->escapeHtml(__('View and Edit Cart')) /** @phpstan-ignore-line */ ?>
                            </a>
                        </div>
                        <?= $block->getChildHtml('extra_actions') ?>
                    </div>

                    <?= $block->getChildHtml('cart-drawer.bottom'); ?>
                </div>
            </div>
            <?= $block->getChildHtml('loading') ?>
        </div>
    </template>
</section>
