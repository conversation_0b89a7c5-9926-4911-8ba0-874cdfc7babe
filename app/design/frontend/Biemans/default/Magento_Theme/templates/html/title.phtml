<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var Escaper $escaper */
/** @var Title $block */

$cssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : 'text-base';
$titleHtml = '';
if (trim((string)$block->getPageHeading())) {
    $titleHtml = '<span class="base" data-ui-id="page-title-wrapper" ' /** @phpstan-ignore-line */
        . $block->getAddBaseAttribute()
        . '>'
        . $escaper->escapeHtml($block->getPageHeading())
        . '</span>';
}
?>
<?php if ($titleHtml): ?>
<div class="page-title flex w-full flex-col md-max:px-container-padding md-max:py-4 md:flex-row justify-center align-center flex-wrap md-max:shadow-y-outline-secondary-500 <?= /** @noEscape */ $cssClass ?>">
    <h1 class="text-gray-900 page-title title-font sm-max:text-4xl sm-max:leading-none"
        <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>>
        <?= /* @noEscape */ $titleHtml ?>
    </h1>
    <?= $block->getChildHtml() ?>
</div>
<?php endif; ?>
