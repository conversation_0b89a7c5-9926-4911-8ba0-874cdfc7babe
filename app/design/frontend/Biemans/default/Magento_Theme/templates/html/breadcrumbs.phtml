<?php

declare(strict_types=1);

use Magento\Theme\Block\Html\Breadcrumbs;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Breadcrumbs $block */
/** @var array<mixed> $crumbs */
?>
<?php if ($crumbs && is_array($crumbs)): ?>
<nav class="py-4 breadcrumbs lg:py-6 lg-max:shadow-y-outline-secondary-500 px-container-padding" aria-labelledby="breadcrumbs-label">
    <span id="breadcrumbs-label" class="sr-only"><?= $escaper->escapeHtml(__('Breadcrumbs')) /** @phpstan-ignore-line */ ?></span>
    <div class="container xl:px-0">
        <ol class="flex flex-wrap lg-max:justify-center gap-y-1.5 lg-max:text-sm font-medium">
            <?php foreach ($crumbs as $crumbName => $crumbInfo): ?>
                <li class="flex <?= $escaper->escapeHtmlAttr($crumbName) ?>">
                    <?php if (!$crumbInfo['first']): ?>
                        <span aria-hidden="true" class="px-2">/</span>
                    <?php endif; ?>
                    <?php if ($crumbInfo['link']): ?>
                        <a
                            href="<?= $escaper->escapeUrl($crumbInfo['link']) ?>"
                            class="underline"
                        >
                            <?= $escaper->escapeHtml($crumbInfo['label']) /** @phpstan-ignore-line */ ?>
                        </a>
                    <?php elseif ($crumbInfo['last']): ?>
                        <span aria-current="page">
                            <?= $escaper->escapeHtml($crumbInfo['label']) /** @phpstan-ignore-line */ ?>
                        </span>
                    <?php else: ?>
                        <?= $escaper->escapeHtml($crumbInfo['label']) /** @phpstan-ignore-line */ ?>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif;
