<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var Hyva\Theme\ViewModel\StoreConfig $storeConfig */
$storeConfig = $viewModels->require(Hyva\Theme\ViewModel\StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(\Magento\Checkout\Block\Cart\Sidebar::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);
?>
<script>
    function initHeader () {
        return {
            searchOpen: false,
            cart: {},
            getData(data) {
                if (data.cart) { this.cart = data.cart }
            }
        }
    }
    function initCompareHeader() {
        return {
            compareProducts: null,
            itemCount: 0,
            receiveCompareData(data) {
                if (data['compare-products']) {
                    this.compareProducts = data['compare-products'];
                    this.itemCount = this.compareProducts.count;
                }
            }
        }
    }
    function setHeaderHeight(header) {
        document.documentElement.style.setProperty('--header-height', `${header.offsetHeight}px`);
    }
</script>
<div id="header"
     class="relative z-30 w-full py-3 lg:pb-0 shadow-b-outline-secondary-500"
     x-data="initHeader()"
     x-init="setHeaderHeight($el)"
     @keydown.window.escape="searchOpen = false;"
     @private-content-loaded.window="getData(event.detail.data)"
     @resize.window.debounce="setHeaderHeight($el)"
>
    <div class="container flex flex-wrap items-center gap-x-2">

        <!--Logo-->
        <div class="w-32 lg:w-44">
            <?= $block->getChildHtml('logo'); ?>
        </div>

        <!--Desktop search-->
        <div class="flex-1 mx-24 md-max:hidden max-w-max-w-screen-sm search-bar">
            <?= $block->getChildHtml('header-search'); ?>
        </div>

        <!--Language switcher-->
        <div class="relative language-switcher z-1 md-max:hidden">
            <?= $block->getChildHtml('store-language-switcher') ?>
        </div>

        <!--Customer Icon & Dropdown-->
        <div class="ml-auto customer-menu">
            <?= $block->getChildHtml('customer') ?>
        </div>

        <!--Main Navigation-->
        <?= $block->getChildHtml('topmenu') ?>

        <!--Mobile search-->
        <div class="relative w-full pt-3 mt-3 lg:hidden before:absolute before:w-screen before:h-px before:top-0 before:left-1/2 before:-translate-x-1/2 before:bg-secondary-500">
            <?= $block->getChildHtml('header-search'); ?>
        </div>

    </div>

    <!--Cart Drawer-->
    <?= $block->getChildHtml('cart-drawer'); ?>

    <!--Authentication Pop-Up-->
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>
