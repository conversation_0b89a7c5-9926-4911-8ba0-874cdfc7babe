<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

?>
<script>
    function initMessages() {
        "use strict";
        return {
            messages: window.mageMessages || [],
            isEmpty() {
                return this.messages.reduce(
                    function (isEmpty, message) {
                        return isEmpty && message === undefined
                    }, true
                )
            },
            removeMessage(messageIndex) {
                this.messages[messageIndex] = undefined;
            },
            addMessages(messages, hideAfter) {
                if (hideAfter == undefined) {
                    hideAfter = 8000;
                }
                messages.map((message) => {
                    // Capitalize message sentence
                    if (message && message.text) {
                        let messageText = message.text.trim(),
                            capitalLetter = messageText[0].toUpperCase();
                        message.text = capitalLetter + messageText.slice(1);
                    }
                    //add longer timeout if we have warning messages.
                    if(message && message.type === 'warning'){
                        hideAfter = 30000;
                    }
                    this.messages = this.messages.concat(message);
                    if (hideAfter) {
                        this.setHideTimeOut(this.messages.length -1, hideAfter);
                    }
                });
            },
            setHideTimeOut(messageIndex, hideAfter) {
                setTimeout((messageIndex) => {
                    this.removeMessage(messageIndex);
                }, hideAfter, messageIndex);
            },
            eventListeners: {
                ['@messages-loaded.window']() {
                    this.addMessages(event.detail.messages, event.detail.hideAfter)
                },
                ['@private-content-loaded.window'](event) {
                    const data = event.detail.data;
                    if (
                        data.messages &&
                        data.messages.messages &&
                        data.messages.messages.length
                    ) {
                        this.addMessages(data.messages.messages);
                    }
                },
                ['@clear-messages.window']() {
                    this.messages = [];
                }
            }
        }
    }
</script>
<section id="messages"
         x-data="initMessages()"
         x-bind="eventListeners"
>
    <template x-if="!isEmpty()">
        <div class="w-full">
            <div role="alert" class="messages">
                <template x-for="(message, index) in messages" :key="index">
                    <div>
                        <template x-if="message">
                            <div class="message border-t-1 border-y border-secondary" :class="{ 'notice bg-warning' : message.type === 'notice' || message.type === 'info' || message.type === 'warning',  'error bg-error' : message.type === 'error', 'success bg-success' : message.type === 'success'}"
                                 :ui-id="'message-' + message.type"
                            >
                                <div class="container flex items-center justify-between w-full p-2">
                                    <span x-html="message.text"></span>
                                    <a href="#" class="close cursor-pointer" title="close"
                                       @click.prevent="removeMessage(index)">
                                        <?= $heroicons->xHtml('text-black', 18, 18); ?>
                                    </a>
                                </div>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </template>
</section>
