<?php

declare(strict_types=1);

use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

?>

<?php if ($block->getMenu()): ?>
    <?php
    $menuClass = $block->getMenu()->getCssClass();
    $parentNode = $block->getParentNode();
    $level = $block->getLevel();
    $viewAllAttributes = [];

    $wrapperAttributesDesktop = [
        'class' => [
            $menuClass . '__inner-list',
            $menuClass . '__inner-list--level' . $level,
            'absolute bg-secondary-200 border border-secondary-500 py-12 z-50 hidden min-w-[20rem]',
            $level == 1 ? '-translate-x-14' : '',
            $level == 2 ? '' : '',
            $level >= 2 ? 'left-full -top-px min-h-[calc(100%+2px)] border-l-0 before:absolute before:inset-y-12 before:left-0 before:w-px before:bg-secondary-500' : '',
            $level == 3 ? '' : ''
        ],
        'data-menu' => 'menu-' . $parentNode->getNodeId(),
    ];
    $wrapperAttributesMobile = [
        'class' => [
            $menuClass . '__inner-list',
            $menuClass . '__inner-list--level' . $level,
            'absolute z-50 lg:hidden top-0 left-0 right-0 bottom-0 bg-white overflow-y-auto',
        ],
        'data-menu' => 'menu-' . $parentNode->getNodeId(),
    ];
    ?>
    <ul x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95"
        x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95"
        :class="{ 'hidden' : hoverPanelActiveId<?= $parentNode->getLevel(); ?> !== '<?= /* @noEscape */ (string) $parentNode->getNodeId() ?>',
                'block' : hoverPanelActiveId<?= $parentNode->getLevel(); ?> === '<?= /* @noEscape */ (string) $parentNode->getNodeId() ?>'}"
        <?= $block->buildAttrFromArray($wrapperAttributesDesktop) ?>
    >

        <?php foreach ($block->getSubmenuNodes() as $node): ?>
            <?php
            $childrenLevel = $node->getLevel() + 1;
            $children = $block->getNodes($childrenLevel, $node);
            $node->setIsParent((bool)$children);

            $nodeAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--level' . $level,
                    'px-12'
                ]
            ];

            if ($children) {
                $nodeAttributes['class'][] = $menuClass . '__inner-item--parent';
            }

            if ($node->getClasses()) {
                $nodeAttributes['class'][] = $node->getClasses();
            }
            ?>

            <li <?= $block->buildAttrFromArray($nodeAttributes) ?>>
                <div x-on:mouseover="hoverPanelActiveId<?= $node->getLevel(); ?> = '<?= /* @noEscape */ (string) $node->getId() ?>'">
                    <?= $block->renderMenuNode($node) ?>
                </div>
                <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
            </li>
        <?php endforeach ?>
        <?php if ($block->isViewAllLinkAllowed($parentNode->getType())): ?>
            <?php
            $viewAllAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--all',
                    $menuClass . '__inner-item--level' . $level
                ]
            ];
            ?>

        <?php endif ?>
    </ul>


    <!-- Mobile submenus -->
    <ul x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700 bg-white"
        x-transition:enter-start="translate-x-full"
        x-transition:enter-end="translate-x-0"
        x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700 bg-white"
        x-transition:leave-start="translate-x-0"
        x-transition:leave-end="translate-x-full"
        <?= $level == 1 ? 'x-show="openMobileMenu" :class="{\'translate-x-0\': openMobileMenu, \'translate-x-full\': !openMobileMenu}"' : '' ?>
        <?= $level == 2 ? 'x-show="openMobileSubmenu" :class="{\'translate-x-0\': openMobileSubmenu, \'translate-x-full\': !openMobileSubmenu}"' : '' ?>
        <?= $level == 3 ? 'x-show="openMobileSubSubmenu" :class="{\'translate-x-0\': openMobileSubSubmenu, \'translate-x-full\': !openMobileSubSubmenu}" ' : '' ?>
        <?= $block->buildAttrFromArray($wrapperAttributesMobile) ?>
    >

        <li class="lg:hidden" <?= $block->buildAttrFromArray($viewAllAttributes) ?>>
            <button
                class="flex items-center gap-x-2 w-full h-14 px-container-padding font-medium !text-black <?= $level == 1 ? 'bg-secondary-500' : 'bg-secondary-200' ?>"
                <?= $block->getLevel() == 1 ? '@click="openMobileMenu = false"' : '' ?>
                <?= $block->getLevel() == 2 ? '@click="openMobileSubmenu = false"' : '' ?>
                <?= $block->getLevel() == 3 ? '@click="openMobileSubSubmenu = false"' : '' ?>
            >
                <span class="icon-chev-left text-2xl leading-none"></span>
                <?= $parentNode->getTitle() ?>
            </button>
        </li>
        <?php if ($block->isViewAllLinkAllowed($parentNode->getType())): ?>
            <?php
            $viewAllAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--all',
                    $menuClass . '__inner-item--level' . $level,
                    $menuClass . 'border-b border-b-secondary-200'
                ]
            ];
            ?>

            <li <?= $block->buildAttrFromArray($viewAllAttributes) ?>>
                <?= $block->renderViewAllLink($parentNode) ?>
            </li>
        <?php endif ?>
        <?php foreach ($block->getSubmenuNodes() as $node): ?>
            <?php
            $childrenLevel = $node->getLevel() + 1;
            $children = $block->getNodes($childrenLevel, $node);
            $node->setIsParent((bool)$children);

            $nodeAttributes = [
                'class' => [
                    $menuClass . '__inner-item',
                    $menuClass . '__inner-item--level' . $level,
                    $menuClass . 'border-b border-b-secondary-200'
                ]
            ];

            if ($children) {
                $nodeAttributes['class'][] = $menuClass . '__inner-item--parent';
            }

            if ($node->getClasses()) {
                $nodeAttributes['class'][] = $node->getClasses();
            }
            ?>

            <li
                <?= $block->buildAttrFromArray($nodeAttributes) ?>
                x-data="initDesktopMenu()"
            >
                <?= $block->renderMenuNode($node) ?>
                <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
            </li>
        <?php endforeach ?>
    </ul>
    <!-- End of mobile submenus -->

<?php endif; ?>
