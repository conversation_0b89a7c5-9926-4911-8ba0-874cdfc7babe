<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CustomerRegistration;
use Hyva\Theme\ViewModel\Customer;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var CustomerRegistration $blockRegistration */
$blockRegistration = $viewModels->require(CustomerRegistration::class);
$customer = $viewModels->require(Customer::class);
$heroicons = $viewModels->require(HeroiconsOutline::class);

?>

<?php if ($block->getMenu()): ?>
    <?php $menuClass = $block->getMenu()->getCssClass() ?>
    <script>
        function initDesktopMenu() {
            return {
                openDesktopMenu: false,
                slideMenu: false,
                openSubmenu: false,
                openSubSubmenu: false,
                openMobileMenu: false,
                openMobileSubmenu: false,
                openMobileSubSubmenu: false,
                hoverPanelActiveId: null,
                hoverPanelActiveId0: null,
                hoverPanelActiveId1: null,
                hoverPanelActiveId2: null,
                hoverPanelActiveId3: null,

                focusBehaviour(container) {
                    let mouseOverEvent = new Event('mouseover');
                    container.querySelectorAll('li.__item a, li.__inner-item a').forEach((node) => {
                        node.addEventListener('focus', (event) => {
                            event.target.parentElement.dispatchEvent(mouseOverEvent);
                        });
                    });
                }
            }
        }
    </script>
    <script>
        function initMobileMenu() {
            return {
                mobileMenuOpen: false,

                // makes sure page is scrolled to top and page can not be scrolled while mobile menu is active
                toggleBodyForMenu() {
                    if (this.mobileMenuOpen) {
                        document.documentElement.classList.add('overflow-hidden');
                        document.documentElement.scrollTop = 0;
                    } else {
                        document.documentElement.classList.remove('overflow-hidden');
                    }
                }
            }
        }
    </script>

    <!-- Desktop menu -->
    <nav class="<?= $menuClass ?> main-nav main-nav--desktop hidden lg:block w-full relative mt-4 before:absolute before:-z-1 before:w-screen before:h-px before:top-0 before:left-1/2 before:-translate-x-1/2 before:bg-secondary-500"
         x-data="initDesktopMenu()"
         x-on:mouseleave="hoverPanelActiveId0 = 0"
         x-init="focusBehaviour($el)"
    >
        <ul class="<?= $menuClass ?>__list flex flex-col lg:flex-row gap-x-12">
            <?php foreach ($block->getNodes() as $node): ?>
                <?php
                $childrenLevel = $node->getLevel() + 1;
                $children = $block->getNodes($childrenLevel, $node);
                $node->setIsParent((bool)$children);
                $parentClass = $children ? ' ' . $menuClass . '__item--parent' : '';
                $additionalClasses = $node->getClasses() ? ' ' . $node->getClasses() : '';
                $itemClasses = $parentClass . $additionalClasses . ' group font-medium';
                ?>
                <li class="<?= $menuClass ?>__item <?= $itemClasses ?>">
                    <div x-on:mouseover="hoverPanelActiveId<?= $node->getLevel(); ?> = '<?= /* @noEscape */ (string) $node->getId() ?>'">
                        <?= $block->renderMenuNode($node) ?>
                    </div>
                    <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
                </li>
            <?php endforeach ?>
        </ul>
    </nav>

    <!-- Mobile menu -->
    <div class="main-nav main-nav--mobile navigation lg:hidden" x-data="initMobileMenu()">

        <div class="flex items-baseline justify-between menu-icon">
            <div class="flex justify-end w-full">
                <button
                    @click="mobileMenuOpen = ! mobileMenuOpen; toggleBodyForMenu()"
                    class="mobile-menu-toggle btn btn-round h-11"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Toggle menu')) ?>"
                    :aria-expanded="mobileMenuOpen"
                    aria-controls="mobile-main-menu"
                >
                    <div id="nav-icon" :class="{'open': mobileMenuOpen}">
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>
            </div>
        </div>

        <div class="backdrop !top-header-height" x-show="mobileMenuOpen"
             x-transition:enter="ease-in-out duration-500"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in-out duration-500"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"></div>

        <nav
            id="mobile-main-menu"
            class="<?= $menuClass ?> transform transition opacity-0 bg-blue w-full h-[calc(100%-var(--header-height))] fixed left-0 top-header-height z-50 bg-white"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Main menu')) ?>"
            x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
            x-transition:enter-start="-translate-x-full"
            x-transition:enter-end="translate-x-0"
            x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
            x-transition:leave-start="translate-x-0"
            x-transition:leave-end="-translate-x-full"
            x-show="mobileMenuOpen"
            :class="{'opacity-100': mobileMenuOpen, 'opacity-0': !mobileMenuOpen}"
        >
            <ul class="<?= $menuClass ?>__list overflow-auto flex flex-col lg:flex-row text-black h-full transform transition duration-500 sm:duration-700"
                id="main-ul"
            >
                <?php foreach ($block->getNodes() as $node): ?>
                    <?php
                    $childrenLevel = $node->getLevel() + 1;
                    $children = $block->getNodes($childrenLevel, $node);
                    $node->setIsParent((bool)$children);
                    $parentClass = $children ? ' ' . $menuClass . '__item--parent' : '';
                    $additionalClasses = $node->getClasses() ? ' ' . $node->getClasses() : '';
                    $itemClasses = $parentClass . $additionalClasses;
                    ?>
                    <li <?= $children ? 'x-data="initDesktopMenu()"' : '' ?>
                        class="<?= $menuClass ?>__item <?= $itemClasses ?> border-b border-b-secondary-200">
                        <?= $block->renderMenuNode($node) ?>
                        <?= $block->renderSubmenu($children, $node, $childrenLevel) ?>
                    </li>
                <?php endforeach ?>

                <div class="flex min-h-[3.5rem] gap-2 px-container-padding py-2 border-b border-b-secondary-200">
                    <!-- Account links -->
                    <?= $block->getChildHtml('mobile-customer-menu') ?>
                    
                    <!--Language switcher-->
                    <div>
                        <?= $block->getChildHtml('mobile-menu-store-language-switcher') ?>
                    </div>
                </div>


            </ul>
            <div class="<?= $menuClass ?>__mobile-bg"></div>
        </nav>
    </div>
    <!-- End of mobile menu -->

<?php endif; ?>
