<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var ViewModelRegistry $viewModels */

$nodeId = $block->getId();
$class = $block->getMenuClass();
$pageUrl = $block->getPageUrl($nodeId);
$pageUrl = $pageUrl ?: '#';
$dataAttribute = '';

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

if ($block->getIsViewAllLink()) {
    $title = __('View All');
    $classLink = $class . '__inner-link';
} else {
    $classLink = $block->getIsRoot() ? $class . '__link' : $class . '__inner-link';
    $title = $block->getTitle();

    if ($nodeId) {
        $dataAttribute = ' data-menu="menu-' . $nodeId . '"';
    }

    if ($block->isCurrentPage($nodeId)) {
        $classLink .= ' current';
    }
}

?>

<?php 
    // top level item
    if ($block->getLevel() == 0): 
?>
    <a href="<?= $pageUrl ?>" class="items-center hidden lg:flex w-full min-h-[3.5rem] md-max:px-4 py-4 block lg:relative lg:after:absolute lg:after:w-full lg:after:h-0.5 lg:after:bg-primary lg:after:bottom-0 lg:after:left-0 lg:after:opacity-0 lg:after:transition-opacity lg:group-hover:after:opacity-100 lg:group-focus-within:after:opacity-100 <?= $classLink ?>" <?= $dataAttribute ?>>
        <?= $title ?>
        <?php if ($block->getLevel() == 0 && $block->getIsParent() && !$block->getIsViewAllLink()): ?>
            <span class="icon-chev-down inline-block ml-2 text-2xl leading-none"></span>
        <?php endif; ?>
    </a>
<?php endif; ?>

<?php 
    // nested menu item
    if ($block->getLevel() >= 1): 
?>
    <a href="<?= $pageUrl ?>" class="items-center hidden lg:inline-flex gap-x-2 min-h-[3.5rem] -ml-2 py-2 px-4 transition-colors rounded-4xl hover:bg-secondary-500 focus-visible:bg-secondary-500 <?php if ($block->getLevel() >= 1 && $block->getIsParent() && !$block->getIsViewAllLink()): ?>w-full justify-between<?php endif; ?> <?= $classLink ?>" <?= $dataAttribute ?>>
        <?= $title ?>
        <?php if ($block->getLevel() >= 1 && $block->getIsParent() && !$block->getIsViewAllLink()): ?>
            <span class="icon-chev-right inline-block text-2xl leading-none"></span>
        <?php endif; ?>
    </a>
<?php endif; ?>

<?php if ($block->getIsParent() && !$block->getIsViewAllLink()): ?>
    <!-- Mobile expanding button -->
    <button class="lg:hidden flex items-center w-full h-14 px-container-padding font-medium"
        <?= $block->getLevel() == 0 ? '@click="openMobileMenu = !openMobileMenu"' : '' ?>
        <?= $block->getLevel() == 1 ? '@click="openMobileSubmenu = !openMobileSubmenu"' : '' ?>
        <?= $block->getLevel() == 2 ? '@click="openMobileSubSubmenu = !openMobileSubSubmenu"' : '' ?>
    >
        <span class="<?= $classLink ?>" <?= $dataAttribute ?>>
            <?= $title ?>
        </span>
        <span class="icon-chev-right ml-auto text-2xl leading-none"></span>
    </button>
    <!-- End of mobile expanding button -->
<?php else: ?>
    <a href="<?= $pageUrl ?>"
        class="lg:hidden flex items-center w-full h-14 px-container-padding font-medium <?= $classLink ?>"
        <?= $dataAttribute ?>
    >
        <?= $title ?>
    </a>
<?php endif; ?>
