<?php

declare(strict_types=1);

/** @var Template $block */

use Magento\Framework\View\Element\Template;

$navHtmlLeft = $block->getChildHtml('hyva.checkout.navigation.left');
$navHtmlRight = $block->getChildHtml('hyva.checkout.navigation.right');
?>
<nav x-data="initCheckoutNavigation()" class="nav-main text-center flex flex-col-reverse md:flex-row gap-y-2 md:gap-y-0 md:gap-x-0 md:mb-0">
    <?php if (! empty($navHtmlLeft)): ?>
    <div class="flex flex-col-reverse md:flex-row gap-y-2 md:gap-x-2 w-full md:items-center">
        <?= /* @noEscape */ $navHtmlLeft ?>
    </div>
    <?php endif ?>

    <div class="flex flex-col-reverse md:flex-row gap-y-2 md:gap-x-2 w-full justify-end md:items-center">
        <?= /* @noEscape */ $navHtmlRight ?>
    </div>
</nav>
