<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

/** @var Template $block */
/** @var EntityFieldInterface $field */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var MagewireFormComponent $magewire */

use Hyva\Checkout\Model\Form\EntityFieldInterface;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magewirephp\Magewire\Component\Form as MagewireFormComponent;

$field = $block->getData('field');
$form = $block->getData('form_name');
?>
<div class="block text-sm font-medium text-gray-700 <?= $field->isRequired() ? 'required' : 'not-required' ?>">
    <?php if (! $field->hasAncestor() && $field->getName()): ?>
        <label for="<?= $escaper->escapeHtmlAttr($field->getName()) ?>">
            <span>
                <?= $escaper->escapeHtml(__($field->getLabel())) /** @phpstan-ignore-line */ ?>
            </span>
            <?php if ($field->isRequired()): ?>
                <span class="required" aria-hidden="true">*</span>
            <?php endif; ?>
        </label>
    <?php endif ?>

    <?= /* @noEscape */ $field->getRenderer()->renderBefore($field, $form) ?>

    <div class="flex items-center gap-4">
        <select class="<?= $escaper->escapeHtmlAttr($field->getClass(['block w-full form-input'])) ?>"
                <?php if ($field->hasAttributes()): ?>
                    <?= /* @noEscape */ $field->renderAttributes() ?>
                <?php endif ?>
        >
            <?php foreach ($field->getOptions() as $option): ?>
                <?php if (is_object($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option->getValue()) /** @phpstan-ignore-line */ ?>">
                        <?= $escaper->escapeHtml(__($option->getLabel())) /** @phpstan-ignore-line */ ?>
                    </option>
                <?php elseif (is_array($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option['value']) ?>">
                        <?= $escaper->escapeHtml(__($option['label'])) /** @phpstan-ignore-line */ ?>
                    </option>
                <?php elseif (is_string($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option) ?>">
                        <?= $escaper->escapeHtml(__($option)) /** @phpstan-ignore-line */ ?>
                    </option>
                <?php endif ?>
            <?php endforeach ?>
        </select>

        <?php if ($field->hasTooltip()): ?>
            <?= /* @noEscape */ $field->getRenderer()->renderTooltip($field) ?>
        <?php endif ?>
    </div>

    <?php if ($field->hasRelatives()): ?>
        <?php foreach ($field->getRelatives() as $child): ?>
            <?= /* @noEscape */ $child->render($form, $field) ?>
        <?php endforeach ?>
    <?php endif ?>

    <?= /* @noEscape */ $field->getRenderer()->renderAfter($field, $form) ?>
</div>
