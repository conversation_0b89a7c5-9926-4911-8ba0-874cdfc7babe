<?php

declare(strict_types=1);

/** @var Template $block */
/** @var Escaper $escaper */

use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
?>
<?php if ($block->hasData('title')): ?>
    <header class="section-title mb-3 lg:mb-4 pb-3 lg:pb-4 border-b border-secondary-200">
        <h2 class="flex gap-x-5 lg:h4">
            <span   
                aria-hidden="true"
                class="
                    number hidden font-base font-light text-xxs-em leading-none 
                    before:w-[1.5em] before:aspect-square before:items-center before:justify-center before:rounded-full before:bg-secondary-200
                "
            >
            </span>
            <?= $escaper->escapeHtml($block->getData('title')) /** @phpstan-ignore-line */ ?>
        </h2>
    </header>
<?php endif;
