<?php

declare(strict_types=1);

use Hyva\Checkout\Magewire\Main as MagewireComponent;
use Hyva\Checkout\ViewModel\Main as ViewModel;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var Template $block */
/** @var MagewireComponent $magewire */
/** @var ViewModel $viewModel */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
$viewModel = $viewModels->require(ViewModel::class);
?>
<?php /* Change the Frontend API main.id when you change the id attribute value. */ ?>
<div id="hyva-checkout-main"
     class="
        <?= $escaper->escapeHtmlAttr($viewModel->getStepClassesAsString()) ?> 
        !grid grid-cols-1 
        md:grid-cols-2 md:grid-rows-[repeat(2,_auto)_1fr] gap-6 lg:gap-x-9 xl:gap-x-12
        lg:grid-cols-3 lg:grid-rows-[auto_1fr]"
     role="main"
>
    <?php /* Change the Frontend API main.container when you change the id attribute value. */ ?>
    <?= $block->getChildHtml('hyva.checkout.container') ?>

    <?php /* Render if it is still is a child of "hyva.checkout.main", usually it will be moved into a column. */ ?>
    <?= $block->getChildHtml('hyva.checkout.navigation') ?>

    <?php /** 'hyva-checkout-container' ID is needed for checkout initialization script */ ?>
    <section id="hyva-checkout-container" style="display: none"></section>

    <?= $block->getChildHtml('hyva.shipping.date.picker') ?>
</div>
