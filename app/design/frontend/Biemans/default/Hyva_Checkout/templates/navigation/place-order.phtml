<?php

declare(strict_types=1);

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Navigation $viewModel */
$viewModel = $viewModels->require(Navigation::class);
$next = $viewModel->getConfig()->getStepAfter();
?>
<?php if ($next === null): ?>
    <button
        type="button"
        class="btn btn-primary min-h-button mt-4 grow"
        x-spread="buttonPlaceOrder()"
        x-bind="buttonPlaceOrder()"
    >
        <?= $escaper->escapeHtml(__('Place Order')) /** @phpstan-ignore-line */ ?>
    </button>
<?php endif ?>
