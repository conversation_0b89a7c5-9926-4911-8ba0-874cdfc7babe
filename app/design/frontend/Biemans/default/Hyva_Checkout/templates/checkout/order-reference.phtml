<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
?>
<div class="pt-4">
    <div x-data="{
            content: $wire.reference ?? ''
         }"
    >
        <div class="flex justify-between">
            <label class="block font-medium mb-1" for="order-reference">
                <?= $escaper->escapeHtml(__('Reference name')) /** @phpstan-ignore-line */ ?>
                <span class="required" aria-hidden="true">*</span>
            </label>
        </div>

        <input id="order-reference"
               wire:model.delay.750ms="reference"
               x-ref="reference"
               x-model="content"
               maxlength="50"
               type="text"
               class="form-input bg-white"
        />
    </div>
</div>
