<?php

declare(strict_types=1);

use Biemans\DepotShipment\ViewModel\DepotOptions;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Checkout\Magewire\Checkout\Shipping\MethodList as Magewire;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Quote\Api\Data\ShippingMethodInterface;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ShippingMethodInterface $method */
/** @var Magewire $magewire */
/** @var Escaper $escaper */

/** @var DepotOptions $depotOptionsViewModel */
$depotOptionsViewModel = $viewModels->require(DepotOptions::class);
/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);

$options = $depotOptionsViewModel->getDepotOptions();

$currentDepot = $magewire->depot ?? '';
?>
<?php if (count($options)): ?>
    <div class="border-t border-secondary-200 pt-4">
        <div
            x-data="{
                content: $wire.depot ?? ''
            }"
            class="inline-flex relative flex-col gap-y-4"
        >
            <?php foreach ($options as $key => $option): ?>
                <div 
                    class="flex gap-x-4"
                    wire:model="depot"
                    x-ref="depot"
                    x-model="content"
                >
                    <input
                        id="depot-<?= $escaper->escapeHtmlAttr($key) ?>"
                        class="form-radio"
                        maxlength="50"
                        type="radio"
                        name="depot_option"
                        value="<?= $escaper->escapeHtmlAttr($option) ?>"
                        <?= ($currentDepot === $option) ? 'checked="checked"' : ''; ?>
                    />
                    <label class="block cursor-pointer"
                           for="depot-<?= $escaper->escapeHtmlAttr($key) ?>"
                    >
                        <?= $escaper->escapeHtml($option) /** @phpstan-ignore-line */ ?>
                    </label>
                </div>
            <?php endforeach; ?>

            <div
                class="!hidden absolute -inset-1 backdrop-blur-xs after:bg-white after:opacity-80"
                wire:loading
                wire:target="depot"
                wire:loading.class.remove="!hidden"
            >
                <div class="flex h-full items-center justify-center">
                    <svg
                        class="animate-spin h-6 w-6"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                    >
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="text-primary" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span aria-role="alert" class="sr-only">
                        <?= $escaper->escapeHtml(__('saving')) /** @phpstan-ignore-line */ ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
<?php endif;
