<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Customer\Api\Data\AddressInterface;
use Magento\Customer\Api\Data\AddressSearchResultsInterface;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\Magewire\Checkout\AddressView\ShippingDetails\AddressList as Magewire;
use Hyva\Checkout\ViewModel\Checkout\AddressView\AbstractAddressList;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Magewire $magewire */
/** @var AddressSearchResultsInterface $shippingAddressList */
/** @var AddressInterface $address */
/** @var Escaper $escaper */
/** @var AbstractAddressList $addressList */
/** @var HeroiconsOutline $iconsViewModel */
$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$addressList = $block->getData('address_list');
$addressListItems = $addressList->getAddressListItems();
?>
<?php if ($addressListItems): ?>
    <div class="address-grid flex flex-col gap-4"
         data-view="grid"
         x-data="toggleAddressList($el)"
         x-init="init()"
    >
        <?php foreach ($addressListItems as $address): ?>

            <div class="checkout-address relative">
                <input
                    type="radio"
                    wire:model="activeAddressEntity"
                    value="<?=  /* @noEscape */ $address->getId() ?? 0 ?>"
                    name="<?= $escaper->escapeHtmlAttr('address_' . $addressList->getTypeNamespace()) ?>"
                    id="<?= $escaper->escapeHtmlAttr($addressList->renderEntityName($address)) ?>"
                    class="checkout-address__radio form-radio absolute top-3 left-3 w-5 h-5 peer"
                />
                <details class="checkout-address__address pl-12 border border-secondary-200"
                         <?= ((int)$address->getId() === (int) $magewire->activeAddressEntity) ? 'open' : ''; ?>
                >
                    <summary class="flex relative isolate items-center cursor-pointer list-none">
                        <label
                            for="<?= $escaper->escapeHtmlAttr($addressList->renderEntityName($address)) ?>"
                            class="font-text-lg leading-none cursor-pointer after:absolute after:inset-0"
                        >
                            <?= $address->getCompany() ?: $address->getFirstname() . ' ' . $address->getLastname(); ?>,
                            <?= $address->getCity(); ?>
                        </label>
                        <span class="checkout-address__address__toggle-indicator icon-chev-down inline-flex min-w-button
                                    min-h-button ml-auto relative z-1 items-center justify-center text-2xl leading-none
                                    transition-transform"
                              aria-hidden="true"
                        ></span>
                    </summary>
                    <div class="pr-4 pb-4">
                        <address class="h-full">
                            <?= /* @noEscape */ $addressList->renderAddress($address) ?>
                        </address>

                        <?php if (!is_numeric($address->getId())): ?>
                            <div class="toolbar block flex gap-x-4">
                                <button
                                    wire:click="$emitTo('<?= /* @noEscape */ $addressList->getModalAddressFormBlockName() ?>', 'edit')"
                                    class="rounded-full w-9 h-9 border-0 flex-shrink-0"
                                >
                                    <span class="w-full h-full rounded-full inline-flex items-center justify-center">
                                        <?= $iconsViewModel->pencilAltHtml('w-5 h-5') ?>
                                    </span>
                                </button>
                            </div>
                        <?php endif ?>
                    </div>
                </details>
            </div>

        <?php endforeach ?>

    </div>

    <script defer="defer">
        function toggleAddressList(listContainer) {
            return {
                init: function () {
                    let self = this;

                    listContainer.querySelectorAll('details').forEach((details) => {
                        details.addEventListener('toggle', self.toggleHandler, true);
                    });
                },

                toggleHandler: function (event) {
                    let details = event.target,
                        parent = details.closest('.checkout-address');

                    // Only run if accordion is open and run on accordions inside our selector
                    if (!details.hasAttribute('open') || !parent) return;

                    // Get all open accordions inside parent
                    parent.parentNode.querySelectorAll('details[open]').forEach((accordion) => {
                        if (accordion === details) return;
                        accordion.removeAttribute('open');
                    });
                }
            };
        }
    </script>
<?php endif;
