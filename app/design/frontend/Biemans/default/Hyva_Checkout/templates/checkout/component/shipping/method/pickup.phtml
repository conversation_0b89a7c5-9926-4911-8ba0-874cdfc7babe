<?php

declare(strict_types=1);

use <PERSON>iemans\PickupShipment\ViewModel\Pickup as PickupViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Checkout\Magewire\Checkout\Shipping\MethodList as Magewire;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Quote\Api\Data\ShippingMethodInterface;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ShippingMethodInterface $method */
/** @var Magewire $magewire */
/** @var Escaper $escaper */

/** @var HeroIconsOutline $heroIcons */
$heroIcons = $viewModels->require(HeroiconsOutline::class);
/** @var FormatterViewModel $formatterViewModel */
$formatterViewModel = $viewModels->require(FormatterViewModel::class);
/** @var PickupViewModel $pickupViewModel */
$pickupViewModel = $viewModels->require(PickupViewModel::class);
?>

<div x-data="{
            value: $wire.value ?? ''
            }"
>
    <div class="flex flex-col"
            x-data="initDatePicker(value)"
            x-init="[initDate(), recalcDaysGrid(), $watch('value', value => {$wire.updatingValue(value); $wire.set('value', value); })]"
    >
        <label for="pickup-date" class="block my-1 w-full text-secondary-700 cursor-pointer">
            <?= $escaper->escapeHtml(__('Select a date')) /** @phpstan-ignore-line */ ?>
        </label>
        <div class="inline-block relative control customer-dob">
            <input id="pickup-date"
                    x-ref="value"
                    x-model="value"
                    @keydown="onInputKeydown($event)"
                    @paste="$event.preventDefault()"
                    @click="showDatepicker = !showDatepicker"
                    autocomplete="off"
                    type="text"
                    class="form-datepicker cursor-pointer"
            />

            <div class="absolute top-1/2 right-4 -translate-y-1/2 z-10 text-secondary-700 cursor-pointer"
                    @click="showDatepicker = true"
            >
                <span class="icon-calendar" aria-hidden="true"></span>
            </div>

            <div
                class="
                    absolute top-full left-0 z-1 p-3 mt-2 bg-white border border-secondary-200
                    sm-max:max-w-[calc(100vw-calc(var(--container-padding)*2))] sm-max:-left-8
                    md:right-0
                    lg:grid lg:grid-cols-[repeat(2,auto)] lg:grid-rows-[repeat(2,auto)] lg:gap-x-6 lg:gap-y-3 lg:-inset-x-24
                    xl:-inset-x-16
                "
                x-cloak=""
                x-transition
                x-show="showDatepicker"
                x-on:click.outside.away="showDatepicker = false"
            >
                <div class="flex flex-wrap items-center justify-between mb-2 lg:col-span-full">
                    <div>
                        <select name="datepicker_month"
                                x-model="month"
                                class="form-select !w-auto !py-2"
                                x-on:change="goToMonth(month)"
                        >
                            <template x-for="(monthName, index) in labels.monthNames">
                                <option
                                    :value="index"
                                    :selected="index == month"
                                    x-text="monthName"
                                ></option>
                            </template>
                        </select>
                        <select name="datepicker_year"
                                x-model="year"
                                class="form-select !w-auto !py-2"
                                x-on:change="goToMonth(month)"
                        >
                            <template x-for="option in yearRange">
                                <option :value="option" x-text="option"></option>
                            </template>
                        </select>
                    </div>
                    <div>
                        <button
                                type="button"
                                class="inline-flex p-1"
                                :class="{'cursor-not-allowed opacity-25': isFirstMonth()}"
                                :disabled="isFirstMonth()"
                                :title="labels.prevText"
                                @click="goToMonth(month - 1)">
                            <span class="icon-chev-left text-2xl leading-none" aria-hidden="true"></span>
                            <span class="sr-only" x-text="labels.prevText"></span>
                        </button>
                        <button
                                type="button"
                                class="inline-flex p-1"
                                :class="{'cursor-not-allowed opacity-25': isLastMonth() }"
                                :disabled="isLastMonth()"
                                :title="labels.nextText"
                                @click="goToMonth(month + 1)">
                            <span class="icon-chev-right text-2xl leading-none" aria-hidden="true"></span>
                            <span class="sr-only" x-text="labels.nextText"></span>
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-7 -mx-1">
                    <template x-for="(day, index) in labels.dayNamesShort" :key="index">
                        <div class="">
                            <div x-text="day" class="mx-auto font-medium text-center text-xs w-8 flex items-center justify-center uppercase"></div>
                        </div>
                    </template>
                    <template x-for="(date, dateIndex) in dayNumbers" :key="dateIndex">
                        <div class="flex items-center justify-center aspect-square" :style="getDayStyle(date)">
                            <div
                                @click="if (canSelectDate(date)) { setDate(date) }"
                                x-text="date"
                                class="w-8 h-8 flex items-center justify-center text-center text-sm transition-colors rounded-sm border border-transparent"
                                :class="{
                                    'cursor-pointer hover:bg-secondary-500 hover:text-white hover:!border-secondary-700 focus-visible:bg-secondary-500 focus-visible:text-white focus-visible:!border-secondary-700' : (canSelectDate(date) && !isSelected(date)),
                                    'hover:cursor-not-allowed focus-visible:cursor-not-allowed' : !canSelectDate(date),
                                    'text-grayscale-300' : (!canSelectDate(date) && !isToday(date)),
                                    'bg-secondary-200 text-secondary-700 underline' : isToday(date),
                                    'bg-secondary-700 text-white !border-secondary-700': isSelected(date),
                                    '!text-white': isToday(date) && isSelected(date)
                                }"
                            ></div>
                        </div>
                    </template>
                </div>

                <div class="flex flex-col lg:col-start-2">
                    <div class="mt-4 lg:mt-0 mb-2 font-medium">
                        <span class="sr-only"><?= $escaper->escapeHtml(__('Select time')); /** @phpstan-ignore-line */ ?> </span>
                        <span aria-hidden="true" class="text-xs uppercase"><?= $escaper->escapeHtml(__('Time')); /** @phpstan-ignore-line */ ?></span>
                    </div>
                    <div class="flex flex-wrap mb-2 md-max:gap-y-1 lg:grid lg:grid-cols-3 lg:gap-x-2 lg:gap-y-1">
                        <template x-for="(time, timeIndex) in timeOptions" :key="timeIndex">
                            <button
                                @click="setTime(time)"
                                type="button"
                                x-text="time"
                                class="md-max:px-2 py-1 text-center text-sm transition-colors flex items-center justify-center border border-transparent cursor-pointer rounded-sm hover:bg-secondary-500 hover:text-white hover:!border-secondary-700 focus-visible:bg-secondary-500 focus-visible:text-white focus-visible:!border-secondary-700"
                                :class="{'bg-secondary-700 text-white !border-secondary-700': isSelectedTime(time)}"
                            ></button>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div>
        <span class="text-green-500 text-sm hidden"
                wire:loading
                wire:target="value"
                wire:loading.class.remove="hidden"
        >
            ...<?= $escaper->escapeHtml(__('saving')) /** @phpstan-ignore-line */ ?>
        </span>
    </div>
</div>
