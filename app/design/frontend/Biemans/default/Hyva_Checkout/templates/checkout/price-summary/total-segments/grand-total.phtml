<?php

declare(strict_types=1);

/** @var Template $block */
/** @var FormatterViewModel $formatterViewModel */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Checkout\ViewModel\Checkout\Formatter as FormatterViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Cart\Items;

$formatterViewModel = $viewModels->require(FormatterViewModel::class);
$segment = $block->getSegment();

/** @var Items $cartItemsViewModel */
$cartItemsViewModel = $viewModels->require(Items::class);

$outletNotification = '';
foreach ($cartItemsViewModel->getCartItems() as $cartItem) {
    $outletNotification = $cartItem->getData('outlet_notification');
    if ($outletNotification) {
        break;
    }
}
?>
<div class="flex gap-4 justify-between items-center md:gap-0">
    <span class="label font-bold text-xl">
        <?= $escaper->escapeHtml(__(($segment['title'] ?? 'Order Total'))) /** @phpstan-ignore-line */ ?>
    </span>
    <span class="value text-right font-bold">
        <?= /* @noEscape */ $formatterViewModel->currency($segment['value'] ?? 0) /** @phpstan-ignore-line */ ?>
    </span>
</div>

<?php if ($outletNotification): ?>
<div class="flex items-center mt-8 py-2 px-4 bg-error">
    <span class="text-3xl leading-none icon-information mr-2" aria-hidden="true"></span>
    <p class="text-base font-bold"><?= $escaper->escapeHtml($outletNotification) ?></p>
</div>
<?php endif; ?>