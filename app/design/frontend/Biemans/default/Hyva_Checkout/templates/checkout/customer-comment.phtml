<?php

declare(strict_types=1);

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
?>
<div class="pt-4">
    <div x-data='{
            content: $wire.comment ?? "",
            limit: $el.dataset.limit,
            rowsLimit: 5,
            currentRows: 0,
            get remaining() {
                let rows = this.content.split(/\r|\r\n|\n/);
                this.currentRows = rows.length;

                if (this.currentRows > this.rowsLimit) {
                    this.content = rows.slice(0, this.rowsLimit).join("\n");

                    return 0;
                }

                return this.limit - this.content.length;
            }
         }'
         x-init="$watch('limit')"
         data-limit="200"
    >
        <div class="flex justify-between">
            <label class="block font-medium mb-1" for="order-comment">
                <?= $escaper->escapeHtml(__('Order Comment')) /** @phpstan-ignore-line */ ?>
            </label>
        </div>

        <textarea
            id="order-comment"
            wire:model.delay.750ms="comment"
            x-ref="content"
            x-bind:maxlength="limit"
            x-model="content"
            type="text"
            rows="5"
            class="form-textarea bg-white resize-y md:min-h-[6rem]"
        ></textarea>

        <div class="w-full flex flex-row-reverse text-sm mt-1" x-ref="remaining" x-cloak>
            <span x-text="(remaining + '/' + limit)"
                  x-show="remaining < 75 && remaining !== 0"
                  x-bind:class="{
                      25: 'text-green-700',
                      50: 'text-purple-700',
                      75: 'text-yellow-600',
                      100: 'text-red-600'
                  }[(100 - (Math.round(remaining / 25) * 25))]"
            ></span>

            <div x-ref="remaining">
                <p class="text-red-700" x-show="remaining === 0">
                    <?= $escaper->escapeHtml(__('You\'ve reached the maximum amount of characters')) /** @phpstan-ignore-line */ ?>
                </p>
                <p class="text-red-700" x-show="currentRows >= rowsLimit">
                    <?= $escaper->escapeHtml(__('You\'ve reached the maximum number of lines')) /** @phpstan-ignore-line */ ?>
                </p>
            </div>
        </div>
    </div>
</div>
