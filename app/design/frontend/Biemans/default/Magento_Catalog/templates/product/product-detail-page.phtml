<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Catalog\Block\Product\View;

/** @var View $block */

$product = $block->getProduct();
?>
<section class="md:shadow-y-outline-secondary-500">
    <div class="flex md:container sm-max:pt-container-padding md:pr-0 md:flex-row flex-col items-center">
        <div class="flex sm-max:flex-wrap order-first w-full">
            <?= $block->getChildHtml('product.media') ?>
            <?= $block->getChildHtml('product.info') ?>
        </div>
    </div>
</section>
<section>
    <?= $block->getChildHtml('product_options_wrapper_bottom') ?>
</section>

<?= $block->getChildHtml('biemans.product.detailed.specifications'); ?>
<?= $block->getChildHtml('product.info.details'); ?>

<section>
    <?= $block->getChildHtml('related') ?>
    <?= $block->getChildHtml('upsell') ?>
    <?= $block->getChildHtml('review_list') ?>
    <?= $block->getChildHtml('review_form') ?>
</section>
