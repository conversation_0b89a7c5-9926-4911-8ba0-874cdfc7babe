<?php

declare(strict_types=1);

/** @var Details $block */
/** @var Escaper $escaper */

use Magento\Catalog\Block\Product\View\Details;
use Magento\Framework\Escaper;

/** @var \Magento\Framework\View\Element\Template $titleRenderer */
$titleRenderer = $block->getChildBlock('product.section.title.renderer');
$defaultTitleTemplate = $titleRenderer->getTemplate();
?>
<div>
<?php
foreach ($block->getGroupSortedChildNames('detailed_info', '') as $sectionName) {
    /** @var \Magento\Framework\View\Element\Template $sectionBlock */
    $sectionBlock  = $block->getLayout()->getBlock($sectionName);
    $sectionHtml   = (string) $sectionBlock->toHtml();
    $titleTemplate = $sectionBlock->getData('title_template') ?? $defaultTitleTemplate;

    if (empty(trim($sectionHtml))) {
        continue;
    }
    ?>
    <section id="<?= $escaper->escapeHtmlAttr($sectionBlock->getNameInLayout()) ?>">
        <?=
            $titleRenderer->setTemplate($titleTemplate) /** @phpstan-ignore-line */
                          ->assign('sectionBlock', $sectionBlock)
                          ->toHtml()
        ?>
        <div class="card w-full">
            <?= /** @noEscape  */ $sectionHtml ?>
        </div>
    </section>
<?php } ?>
</div>
