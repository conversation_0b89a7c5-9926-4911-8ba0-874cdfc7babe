<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<div class="toolbar-sorter sorter flex items-center">
    <span class="sr-only sorter-label">
        <?= $escaper->escapeHtml(__('Sort By')) /** @phpstan-ignore-line */ ?>
    </span>
    <select
        data-role="sorter"
        class="form-select sorter-options h-8 sm:h-10 py-0 text-base-em"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Sort By')) ?>"
        @change="changeUrl(
            'product_list_order',
            $event.currentTarget.options[$event.currentTarget.selectedIndex].value,
            options.orderDefault
        )"
    >
        <?php foreach ($block->getAvailableOrders() as $orderCode => $orderLabel):?>
            <option value="<?= $escaper->escapeHtmlAttr($orderCode) ?>"
                <?php if ($block->isOrderCurrent($orderCode)): ?>
                    selected="selected"
                <?php endif; ?>
                >
                <?= $escaper->escapeHtml(__($orderLabel)) /** @phpstan-ignore-line */ ?>
            </option>
        <?php endforeach; ?>
    </select>
    <?php if ($block->getCurrentDirection() == 'desc'): ?>
        <a
            href="#"
            class="action sorter-action sort-desc inline-flex min-h-button min-w-button items-center justify-center"
            @click.prevent="changeUrl('product_list_dir', 'asc', options.directionDefault)"
        >
            <span class="sr-only"><?= $escaper->escapeHtmlAttr(__('Set Ascending Direction')) ?></span>
            <span class="icon-arrow-down text-xl leading-none" aria-hidden="true"></span>
        </a>
    <?php else: ?>
        <a
            href="#"
            class="action sorter-action sort-asc inline-flex min-h-button min-w-button items-center justify-center"
            @click.prevent="changeUrl('product_list_dir', 'desc', options.directionDefault)"
        >
            <span class="sr-only"><?= $escaper->escapeHtmlAttr(__('Set Descending Direction')) ?></span>
            <span class="icon-arrow-up text-xl leading-none" aria-hidden="true"></span>
        </a>
    <?php endif; ?>
</div>
