<?php

declare(strict_types=1);

use Biemans\ThemeConfigurations\ViewModel\ProductGridItem;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentCategory;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Block\Product\ListProduct;
use Magento\Framework\Escaper;

/** @var ListProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductGridItem $productGridItemViewModel */
$productGridItemViewModel = $viewModels->require(ProductGridItem::class);
/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);
/** @var CurrentCategory $currentCategoryViewModel */
$currentCategoryViewModel = $viewModels->require(CurrentCategory::class);

$hideRatingSummary = (bool) $block->getData('hide_rating_summary');
$hideDetails = (bool) $block->getData('hideDetails');
$eagerLoadImagesCount = (int) ($block->getData('eager_load_images_count') ?? 3);

$productCollection = $block->getLoadedProductCollection();

?>
<?php if (!$productCollection->count()): ?>
    <div
        class="w-full p-4 message info empty md:my-14 md:p-10 md:shadow-outline-secondary-500"
    >
        <p>
            <?= $escaper->escapeHtml(__('We can\'t find products matching the selection.')) /** @phpstan-ignore-line */ ?>
        </p>
    </div>
<?php else: ?>
    <section>
        <?= $block->getToolbarHtml() ?>
        <?= $block->getAdditionalHtml() ?>
        <?php
        if ($block->getMode() == 'grid') {
            $viewMode         = 'grid';
            $imageDisplayArea = 'category_page_grid';
            $showDescription  = false;
            $templateType     = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
        } else {
            $viewMode         = 'list';
            $imageDisplayArea = 'category_page_list';
            $showDescription  = true;
            $templateType     = \Magento\Catalog\Block\Product\ReviewRendererInterface::FULL_VIEW;
        }
        /**
         * Position for actions regarding image size changing in vde if needed
         */
        $pos = $block->getPositioned();
        ?>
        <?php if ($viewMode === 'grid'): // Grid mode ?>
            <div class="products wrapper sm-max:overflow-x-hidden mode-grid products-grid">
                <div class="sm-max:-translate-x-px sm-max:w-[calc(100%+2px)] grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    <?php
                    /** @var \Magento\Catalog\Model\Product $product */
                    foreach (array_values($productCollection->getItems()) as $i => $product) {
                        if ($i < $eagerLoadImagesCount) {
                            $product->setData('image_custom_attributes', ['loading' => 'eager', 'fetchpriority' => 'high']);
                        }
                        echo $productGridItemViewModel->getItemHtml(
                            $product,
                            $block,
                            $viewMode,
                            $templateType,
                            $imageDisplayArea,
                            $showDescription
                        );
                    } ?>
                </div>
                <?= $block->getChildBlock('toolbar')->setIsBottom(true)->toHtml() /** @phpstan-ignore-line */ ?>
            </div>
        <?php else: // List mode ?>
            <div class="products wrapper sm-max:overflow-x-hidden mode-list products-list">
                <div class="grid w-full grid-cols-1 sm-max:-translate-x-px">
                    <?php
                    /** @var \Magento\Catalog\Model\Product $product */
                    foreach (array_values($productCollection->getItems()) as $i => $product) {
                        if ($i < $eagerLoadImagesCount) {
                            $product->setData('image_custom_attributes', ['loading' => 'eager', 'fetchpriority' => 'high']);
                        }
                        echo $productListItemViewModel->getItemHtml(
                            $product,
                            $block,
                            $viewMode,
                            $templateType,
                            $imageDisplayArea,
                            $showDescription
                        );
                    } ?>
                </div>
                <?= $block->getChildBlock('toolbar')->setIsBottom(true)->toHtml() /** @phpstan-ignore-line */ ?>
            </div>
        <?php endif; ?>
    </section>
<?php endif; ?>
