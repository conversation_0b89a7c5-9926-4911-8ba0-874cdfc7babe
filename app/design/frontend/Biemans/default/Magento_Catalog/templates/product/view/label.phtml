<?php

declare(strict_types=1);

use Biemans\Catalog\Model\Product\Attribute\Source\Labels;
use Biemans\Catalog\Setup\Patch\Data\AddLabelsProductAttribute;
use <PERSON><PERSON>mans\Catalog\ViewModel\CollectionLabel;
use <PERSON><PERSON><PERSON>\Catalog\ViewModel\IsNewProduct;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var \Magento\Catalog\Model\Product $product */
$product = $block->getProduct();

/** @var IsNewProduct $isNewProductViewModel */
$isNewProductViewModel = $viewModels->require(IsNewProduct::class);
/** @var CollectionLabel $collectionLabelViewModel */
$collectionLabelViewModel = $viewModels->require(CollectionLabel::class);

$isNewProduct = $isNewProductViewModel->isProductNew($product);

$labels = [];
if ($product->getData(AddLabelsProductAttribute::ATTRIBUTE_CODE)) {
    $labels = array_map(
        'trim',
        explode(',', $product->getData(AddLabelsProductAttribute::ATTRIBUTE_CODE))
    );
}

if ($isNewProduct && !in_array(Labels::OPTION_NEW, $labels)) {
    $labels[] = Labels::OPTION_NEW;
}
//out of collection and is new cannot be same time.
$hasOutCollectionLabel = $collectionLabelViewModel->hasOutCollectionLabel($product);
if(!in_array(Labels::OPTION_NEW, $labels) && $hasOutCollectionLabel){
    $labels[] = $collectionLabelViewModel->getOutCollectionLabel();
}
?>

<?php $bottomRightLabels = 0 ?>
<?php foreach ($labels as $label) : ?>
    <?php if (in_array($label, [Labels::OPTION_SUSTAINABLE_PACKAGES, Labels::OPTION_SUSTAINABLE_PRODUCT])) : ?>
        <?php $bottomRightLabels++ ?>
        <div class="absolute bottom-4 <?= ($bottomRightLabels === 1) ? 'right-8 lg:right-10 xl:right-14' : 'right-[4.5rem] lg:right-20 xl:right-24' ?>">
            <div class="product-label !leading-none white-space-nowrap">
                <img src="<?= $escaper->escapeUrl($block->getViewFileUrl("images/{$label}.png")) ?>" alt="<?= $escaper->escapeHtmlAttr($label); ?>" loading="lazy" height="20" class="w-8 h-8" />
            </div>
        </div>
    <?php else : ?>
        <div class="absolute top-6 lg:top-8 xl:top-12 left-6 lg:left-8 xl:left-12">
            <div class="product-label inline-block px-3 py-1.5 border-2 <?=
            ($label === Labels::OPTION_OUTLET) ? 'bg-labels-yellow border-labels-red text-labels-red' :
                (($label === CollectionLabel::OUT_COLLECTION_LABEL_TEXT) ? 'bg-in-stock' : 'border-labels-red text-black bg-white');
            ?> rounded-3xl xl:text-lg !leading-none font-heading white-space-nowrap uppercase">
                <?= $escaper->escapeHtml(__(($label === Labels::OPTION_NEW) ? 'new product' : $label));
                /** @phpstan-ignore-line */ ?>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach;
