<?php

declare(strict_types=1);

use Hyva\Theme\Model\LocaleFormatter;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */
?>
<p class="toolbar-amount sm-max:hidden mr-auto text-base leading-none" id="toolbar-amount">
    <?= /** @phpstan-ignore-line */ $escaper->escapeHtml(
        __(
            '%1 products',
            '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
        ),
        ['span']
    ) ?>
</p>
