<?php

declare(strict_types=1);

use <PERSON>iemans\Configurator\ViewModel\Configurator;
use Biemans\ProductSpecifications\ViewModel\DetailedProductSpecifications;
use <PERSON><PERSON>mans\StoreForcedLanguage\ViewModel\StoreEmulator;
use Biemans\ThemeConfigurations\ViewModel\Customer;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var StoreEmulator $storeEmulator */
$storeEmulator = $viewModels->require(StoreEmulator::class);

/** @var DetailedProductSpecifications $specificationsViewModel */
$specificationsViewModel = $viewModels->require(DetailedProductSpecifications::class);
$attributes = $specificationsViewModel->getProductSpecificationAttributes();
$associatedProductsSpecifications = $specificationsViewModel->getAssociatedProductSpecifications();

/** @var Configurator $configuratorViewModel */
$configuratorViewModel = $viewModels->require(Configurator::class);
$specificationsViewModel->removeEmptyColumn($attributes,$associatedProductsSpecifications);
/** @var Customer $customerViewModel */
$customerViewModel = $viewModels->require(Customer::class);

if (!count($attributes) || !count($associatedProductsSpecifications)) {
    return '';
}
?>

<div class="sm-max:w-screen sm-max:relative sm-max:left-1/2 sm-max:-translate-x-1/2 bg-secondary-200 shadow-y-outline-secondary-500">
    <div class="container !max-w-6xl py-container-padding lg:py-8 xl:py-12">
        <h2 id="product-specs" class="mb-6">
            <?= $escaper->escapeHtml(__('Product specifications')); /** @phpstan-ignore-line */ ?>
        </h2>
        <table class="table-responsive w-full leading-relaxed" aria-labeledny="product-specs">
            <thead>
                <tr class="border-b border-b-secondary-500">
                    <?php foreach ($attributes as $attributeCode => $attributeLabel): ?>
                        <th scope="col" class="pr-1 py-1">
                            <?= $escaper->escapeHtml($attributeLabel) /** @phpstan-ignore-line */ ?>
                        </th>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($associatedProductsSpecifications as $associatedProductSpecifications): ?>
                    <tr class="border-b border-b-secondary-500">
                        <?php foreach ($attributes as $attributeCode => $attributeLabel): ?>
                            <td data-label="<?= $escaper->escapeHtmlAttr($attributeLabel); ?>" class="pr-1 py-1">
                                <?php if (
                                    $associatedProductSpecifications
                                    && !empty($attributeValue = $associatedProductSpecifications[$attributeCode])
                                ): ?>
                                    <?php if (is_array($attributeValue)): ?>
                                        <div class="flex gap-x-2 items-center">
                                            <img
                                                src="<?= $escaper->escapeUrl($attributeValue['image'] ?? '');?>"
                                                alt=""
                                                witdh="48"
                                                height="48"
                                                loading="lazy"
                                                class="w-8"
                                            >
                                            <span><?= $escaper->escapeHtml($attributeValue['label'] ?? '') /** @phpstan-ignore-line */ ?></span>
                                        </div>
                                    <?php elseif ($attributeCode === 'personalisation_service'): ?>
                                        <?php $storeEmulator->stopEmulation(); ?>
                                        <?php if (
                                            ($personalizationProduct = $specificationsViewModel->getProductBySku($attributeValue))
                                            && ($personalizationGroupedProduct = $specificationsViewModel->getPersonalizationProduct($attributeValue))
                                        ): ?>
                                            <?php
                                                /** @var \Magento\Catalog\Model\Product $personalizationProduct */
                                                /** @var \Magento\Catalog\Model\Product $personalizationGroupedProduct */

                                                $configuratorViewModel->setProduct($personalizationProduct);
                                            ?>
                                            <?php if (!$configuratorViewModel->hasOptions() || !$customerViewModel->isCustomerLoggedIn()): ?>
                                                <span>
                                                    <?= $specificationsViewModel->getPersonalisationSize($personalizationProduct); ?>
                                                </span>
                                            <?php else: ?>
                                                <a href="<?= $personalizationGroupedProduct->getProductUrl(); ?>">
                                                    <?= $escaper->escapeHtml($attributeValue) /** @phpstan-ignore-line */ ?>
                                                </a>
                                            <?php endif; ?>
                                            <?php $storeEmulator->startEmulation(); ?>
                                        <?php else: ?>
                                            <?= $escaper->escapeHtml($attributeValue) /** @phpstan-ignore-line */ ?>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?= $escaper->escapeHtml($attributeValue) /** @phpstan-ignore-line */ ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
