<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Block\Product\View\Gallery;
use Magento\Catalog\Helper\Image;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Gallery $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$images = $block->getGalleryImages()->getItems();
$mainImage = current(array_filter($images, [$block, 'isMainImage']));

if (!empty($images) && empty($mainImage)) {
    $mainImage = reset($images);
}

/** @var Image $helper */
$helper = $block->getData('imageHelper');
$mainImageData = $mainImage ?
    $mainImage->getData('medium_image_url') :
    $helper->getDefaultPlaceholderUrl('image');

$smallWidth = $block->getImageAttribute('product_page_image_small', 'width', '90');
$smallHeight = $block->getImageAttribute('product_page_image_small', 'height', '90');
$mediumWidth = $block->getImageAttribute('product_page_image_medium', 'width', '700');
$mediumHeight = $block->getImageAttribute('product_page_image_medium', 'height', '700');

$productName = $block->getProduct()->getName();
?>

<div
    id="gallery"
    class="w-full md:w-4/12 md:h-auto md:py-4 xl:py-8 md:pr-4 xl:pr-8 md:shadow-r-outline-secondary-500"
    x-data="initGallery()"
    x-init="initActive(); $nextTick(() => calcPageSize())"
    x-bind="eventListeners"
>
    <div class="relative">
        <div
            class="relative flex flex-col gap-y-2"
            :class="{'w-full h-full fixed flex-row top-0 left-0 bg-white z-50': fullscreen}"
        >
            <div class="absolute top-0 right-0 pt-4 pr-4">
                <button
                    @click="fullscreen = false; $nextTick(() => calcPageSize())"
                    type="button"
                    class="hidden text-gray-400 transition duration-150 ease-in-out hover:text-gray-500 focus:outline-none focus:text-gray-500"
                    :class="{ 'hidden': !fullscreen, 'block': fullscreen }"
                    aria-label="Close"
                >
                    <?= $heroicons->xHtml(); ?>
                </button>
            </div>
            <div class="relative self-center w-full aspect-[343/368] bg-secondary-200
                        px-16 py-20
                        md:px-8 md:py-14
                        xl:px-16 xl:py-20"
                x-transition:enter="ease-out duration-500"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
            >
                <?= $block->getChildBlock('biemans.product.label')->setProduct($block->getProduct())->toHtml(); /** @phpstan-ignore-line */ ?>
                <div class="relative">
                    <?php
                    /**
                     * The first image is a placeholder that determines the
                     * aspect ratio for the gallery. It will be hidden as
                     * soon as JS is loaded, but will keep reserving the
                     * necessary space in the layout for the other (absolute positioned)
                     * images. Hence, `opacity-0` instead of `x-show` or `hidden`
                     */
                    ?>
                    <img
                        alt="main product photo"
                        title="<?= $escaper->escapeHtmlAttr($productName) ?>"
                        class="object-contain object-center w-full h-auto max-h-screen-75"
                        :class="'opacity-0'"
                        src="<?= /* @noEscape */ $mainImageData ?>"
                        width="<?= /* @noEscape */ $mediumWidth ?>"
                        height="<?= /* @noEscape */ $mediumHeight ?>"
                        itemprop="image"
                        @click="<?php if (count($images) > 1): ?>await $nextTick(); calcPageSize();<?php endif; ?>"
                    />
                    <template x-for="(image, index) in images" :key="index">
                        <img
                            :alt="image.caption || '<?= $escaper->escapeJs($productName) ?>'"
                            @click="<?php if (count($images) > 1): ?>await $nextTick(); calcPageSize();<?php endif; ?>"
                            class="absolute inset-0 object-contain object-center w-full m-auto max-h-screen-75"
                            :class="{ 'cursor-pointer': !fullscreen }"
                            width="<?= /* @noEscape */ $mediumWidth ?>"
                            height="<?= /* @noEscape */ $mediumHeight ?>"
                            :loading="active!==index ? 'lazy' : 'eager'"
                            :src="fullscreen ? image.full : image.img"
                            x-transition.opacity.duration.500ms x-show="active===index"
                        >
                    </template>
                    <div
                        class="absolute inset-0 hidden w-full h-full bg-white nonmobile"
                        :class="{ 'hidden': activeVideoType !== 'youtube' }"
                        x-transition.opacity.duration.500ms x-show="
                            images[active].type === 'video' && activeVideoType === 'youtube'
                        "
                    >
                        <div id="youtube-player" class="w-full h-full"></div>
                    </div>
                    <div
                        class="absolute inset-0 hidden w-full h-full bg-white"
                        :class="{ 'hidden': activeVideoType !== 'vimeo' }"
                        x-transition.opacity.duration.500ms x-show="
                            images[active].type === 'video' && activeVideoType === 'vimeo'
                        "
                    >
                        <div id="vimeo-player" class="w-full h-full"></div>
                    </div>
                </div>
                <template x-for="(image, index) in images" :key="index">
                    <a
                        :href="image.full_original"
                        x-show="active===index"
                        download
                        class="btn btn-with-icon btn-with-icon--left btn-white py-2.5 pl-9 font-subheading font-normal text-sm absolute z-1 right-4 bottom-4"
                    >
                        <span class="icon-download" aria-hidden="true"></span>
                        Download
                    </a>
                </template>
            </div>
            <div @resize.window.debounce="calcPageSize(); await $nextTick(); calcActive();">
                <div
                    id="thumbs"
                    class="flex items-center gap-x-2"
                    :class="{ 'fixed justify-center bottom-0 left-0 right-0 mx-6': fullscreen }"
                    style="min-height: 100px;"
                    x-show="images.length > 1"
                    x-cloak
                >
                    <button
                        aria-label="<?= $escaper->escapeHtmlAttr(__('Previous')) ?>"
                        tabindex="-1"
                        class="flex-none mr-4 text-black rounded-full outline-none focus:outline-none"
                        :class="{ 'opacity-25 pointer-events-none' : activeSlide === 0, 'hidden' : !isSlider }"
                        @click="scrollPrevious"
                    ><?= $heroicons->chevronLeftHtml() ?></button>
                    <div
                        class="flex w-full gap-2 overflow-auto js_thumbs_slides thumbs-wrapper flex-nowrap js_slides snap"
                        x-ref="jsThumbSlides"
                        @scroll.debounce="calcPageSize(); calcActive()"
                    >
                        <template x-for="(image, index) in images" :key="index">
                            <button
                                @click.prevent="setActive(index);"
                                class="p-3 js_thumbs_slide basis-1/3 grow-0 shrink aspect-square bg-secondary-200"
                            >
                                <img
                                    :src="image.thumb"
                                    :alt="image.caption || '<?= $escaper->escapeJs($productName) ?>'"
                                    class="object-cover w-full h-full"
                                    width="<?= /* @noEscape */ $smallWidth ?>"
                                    height="<?= /* @noEscape */ $smallHeight ?>"
                                >
                            </button>
                        </template>
                    </div>
                    <button
                        aria-label="<?= $escaper->escapeHtmlAttr(__('Next')) ?>"
                        tabindex="-1"
                        class="flex-none ml-4 text-black rounded-full outline-none focus:outline-none"
                        :class="{ 'opacity-25 pointer-events-none' : activeSlide >= itemCount-pageSize, 'hidden' : !isSlider }"
                        @click="scrollNext"
                    ><?= $heroicons->chevronRightHtml() ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function initGallery () {
        return {
            "active": 0,
            "videoData": {},
            "activeVideoType": false,
            "autoplayVideo": false,
            "loopVideo": true,
            "relatedVideos": false,
            "vimeoPlayer": null,
            "fullscreen": false,
            "isSlider": false,
            "initialImages": <?= /* @noEscape */ $block->getGalleryImagesJson() ?>,
            "images": <?= /* @noEscape */ $block->getGalleryImagesJson() ?>,
            "appendOnReceiveImages": <?=
                $block->getVar('gallery_switch_strategy', 'Magento_ConfigurableProduct') === 'append' ? 'true' : 'false'
            ?>,
            "activeSlide": 0,
            "itemCount": 0,
            "pageSize": 4,
            "pageFillers": 0,
            receiveImages(images) {
                if (this.appendOnReceiveImages) {
                    const initialUrls = this.initialImages.map(image => image.full);
                    const newImages = images.filter(image => ! initialUrls.includes(image.full));
                    this.images = [].concat(this.initialImages, newImages);
                    this.setActive(newImages.length ? this.initialImages.length : 0);
                } else {
                    this.images = images;
                    this.setActive(0);
                }
            },
            resetGallery() {
                this.images = this.initialImages;
                this.setActive(0);
            },
            initActive() {
                let active = this.images.findIndex(function(image) {
                    return image.isMain === true
                });
                if (active === -1) {
                    active = 0;
                }
                this.setActive(active);
            },
            setActive(index) {
                this.active = index;
                if (window.youtubePlayer) {
                    window.youtubePlayer.stopVideo();
                }
                if (this.vimeoPlayer) {
                    this.vimeoPlayer.contentWindow.postMessage(JSON.stringify({"method": "pause"}), "*");
                }
                if (this.images[index].type === 'video') {
                    this.activateVideo();
                }
            },
            activateVideo() {
                const videoData = this.getVideoData();

                if (!videoData) { return }

                this.activeVideoType = videoData.type;

                if (videoData.type === "youtube") {
                    if (!window.youtubePlayer) {
                        this.initYoutubeAPI(videoData);
                    } else {
                        window.youtubePlayer.loadVideoById(videoData.id);
                    }

                } else if (videoData.type === "vimeo") {
                    this.initVimeoVideo(videoData);
                }
            },
            getVideoData() {
                const videoUrl = this.images[this.active] && this.images[this.active].videoUrl;

                if (!videoUrl) { return }

                let id,
                    type,
                    youtubeRegex,
                    vimeoRegex,
                    useYoutubeNoCookie = false;

                if (videoUrl.match(/youtube\.com|youtu\.be|youtube-nocookie.com/)) {
                    id = videoUrl.replace(/^\/(embed\/|v\/)?/, '').replace(/\/.*/, '');
                    type = 'youtube';

                    youtubeRegex = /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
                    id = videoUrl.match(youtubeRegex)[1];

                    if (videoUrl.match(/youtube-nocookie.com/)) {
                        useYoutubeNoCookie = true;
                    }
                } else if (videoUrl.match(/vimeo\.com/)) {
                    type = 'vimeo';
                    vimeoRegex = new RegExp(['https?:\\/\\/(?:www\\.|player\\.)?vimeo.com\\/(?:channels\\/(?:\\w+\\/)',
                        '?|groups\\/([^\\/]*)\\/videos\\/|album\\/(\\d+)\\/video\\/|video\\/|)(\\d+)(?:$|\\/|\\?)'
                    ].join(''));
                    id = videoUrl.match(vimeoRegex)[3];
                }

                return id ? {
                    id: id, type: type, useYoutubeNoCookie: useYoutubeNoCookie
                } : false;
            },
            initYoutubeAPI(videoData) {
                if (document.getElementById('loadYoutubeAPI')) {
                    return;
                }
                const params = {};
                const loadYoutubeAPI = document.createElement('script');
                loadYoutubeAPI.src = 'https://www.youtube.com/iframe_api';
                loadYoutubeAPI.id = 'loadYoutubeAPI';
                const firstScriptTag = document.getElementsByTagName('script')[0];
                firstScriptTag.parentNode.insertBefore(loadYoutubeAPI, firstScriptTag);

                const host = (videoData.useYoutubeNoCookie) ?
                    'https://www.youtube-nocookie.com' :
                    'https://www.youtube.com';

                if (this.autoplayVideo) {
                    params.autoplay = this.autoplayVideo;
                }
                if (!this.relatedVideos) {
                    params.rel = 0;
                }
                const fireYoutubeAPI = document.createElement('script');
                fireYoutubeAPI.innerHTML = `function onYouTubeIframeAPIReady() {
                    window.youtubePlayer = new YT.Player('youtube-player', {
                        host: '${host}',
                        videoId: '${videoData.id}',
                        playerVars: ${JSON.stringify(params)},
                    });
                }`;
                firstScriptTag.parentNode.insertBefore(fireYoutubeAPI, firstScriptTag);
            },
            initVimeoVideo(videoData) {
                let
                    additionalParams = '',
                    src;

                const timestamp = new Date().getTime();
                const vimeoContainer = document.getElementById("vimeo-player");
                const videoId = videoData.id;

                if (!vimeoContainer || !videoId) return;

                if (this.autoplayVideo) {
                    additionalParams += '&autoplay=1';
                }

                if (this.loopVideo) {
                    additionalParams += '&loop=1';
                }
                src = 'https://player.vimeo.com/video/' +
                    videoId + '?api=1&player_id=vimeo' +
                    videoId +
                    timestamp +
                    additionalParams;
                vimeoContainer.innerHTML =
                    `<iframe id="${'vimeo' + videoId + timestamp}"
                        src="${src}"
                        width="640" height="360"
                        webkitallowfullscreen
                        mozallowfullscreen
                        allowfullscreen
                        referrerPolicy="origin"
                        allow="autoplay"
                        class="object-center w-full h-full object-fit"
                     />`;

                this.vimeoPlayer = vimeoContainer.childNodes[0];
            },
            getSlider() {
                return this.$refs.jsThumbSlides;
            },
            calcPageSize() {
                const slider = this.getSlider();
                if (slider) {
                    const slideEl = slider.querySelector('.js_thumbs_slide'),
                        marginRight = parseInt(window.getComputedStyle(slideEl).marginRight);

                    this.itemCount = slider.querySelectorAll('.js_thumbs_slide').length;
                    this.pageSize = Math.round(slider.clientWidth / (slideEl.clientWidth + marginRight));
                    this.pageFillers = (
                        this.pageSize * Math.ceil(this.itemCount / this.pageSize)
                    ) - this.itemCount;

                    this.isSlider = ((slider.clientWidth - (this.itemCount * (slideEl.clientWidth + marginRight))) < 0);
                }
            },
            calcActive() {
                const slider = this.getSlider();
                if (slider) {
                    const sliderItems = this.itemCount + this.pageFillers;
                    const calculatedActiveSlide = slider.scrollLeft / (slider.scrollWidth / sliderItems);
                    this.activeSlide = Math.round(calculatedActiveSlide / this.pageSize) * this.pageSize;
                }
            },
            scrollPrevious() {
                this.scrollTo(this.activeSlide - this.pageSize);
            },
            scrollNext() {
                this.scrollTo(this.activeSlide + this.pageSize);
            },
            scrollTo(idx) {
                const slider = this.getSlider();
                if (slider) {
                    const slideWidth = slider.scrollWidth / (this.itemCount + this.pageFillers);
                    slider.scrollLeft = Math.floor(slideWidth) * idx;
                    this.activeSlide = idx;
                }
            },
            eventListeners: {
                ['@keydown.window.escape']() {
                    this.fullscreen = false
                },
                ['@update-gallery.window'](event) {
                    this.receiveImages(event.detail);
                },
                ['@reset-gallery.window'](event) {
                    this.resetGallery();
                }
            }
        }
     }
</script>
