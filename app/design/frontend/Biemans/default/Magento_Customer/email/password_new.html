<!--@subject {{trans "Reset your %store_name password" store_name=$store.frontend_name}} @-->
<!--@vars {
"var store.frontend_name":"Store Name",
"var this.getUrl($store, 'customer/account/')":"Customer Account URL",
"var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])":"Password Reset URL",
"var customer.name":"Customer Name"
} @-->
{{template config_path="design/email/header_template"}}

<p class="greeting">{{trans "Dear client" }}, <br><br></p>
<p>{{trans "A request has recently been submitted to change the password for your account with this email address"}}.</p>
<p>{{trans "If you have requested this change, you can set a new password via the button below"}}.</p>

<table class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tr>
        <td>
            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center">
                <tr>
                    <td align="center">
                        <a href="{{var this.getUrl($store,'customer/account/createPassword',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])}}" target="_blank">{{trans "Set a new password"}}</a>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<p>{{trans "If you have not submitted this request, you can ignore this email and your password will remain unchanged"}}.</p>

{{template config_path="design/email/footer_template"}}
