<?php

use <PERSON><PERSON>mans\CustomerDebtorNumber\Setup\Patch\Data\AddDebtorNumberCustomerAttribute;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<section aria-labelledby="account-info" class="pb-6 mb-6 prose border-b lg:prose-lg md:max-w-none border-b-secondary-200">
    <h2 id="account-info" class="!mt-0"><?= $escaper->escapeHtml(__('Contact Information')) /** @phpstan-ignore-line */ ?></h2>
    <p class="!mb-0"><?= $escaper->escapeHtml($block->getCustomer()?->getCustomAttribute(AddDebtorNumberCustomerAttribute::ATTRIBUTE_CODE)?->getValue()) /** @phpstan-ignore-line */ ?></p>
    <p class="!my-0"><?= $escaper->escapeHtml($block->getName()) /** @phpstan-ignore-line */ ?></p>
    <address><?= $escaper->escapeHtml($block->getCustomer()->getEmail()) /** @phpstan-ignore-line */ ?></address>
    <?= $block->getChildHtml('customer.account.dashboard.info.extra'); ?>
    <p class="text-secondary-700">
        <a href="<?= $escaper->escapeUrl($block->getUrl('customer/account/edit')) ?>">
            <small>
                <span><?= $escaper->escapeHtml(__('Edit')) /** @phpstan-ignore-line */ ?></span>
                <span class="icon-arrow-right" aria-hidden="true"></span>
            </small>
        </a> <br>
        <a href="<?= $escaper->escapeUrl($block->getChangePasswordUrl()) ?>">
            <small>
                <?= $escaper->escapeHtml(__('Change Password')) /** @phpstan-ignore-line */ ?>
                <span class="icon-arrow-right" aria-hidden="true"></span>
            </small>
        </a>
    </p>
</section>
