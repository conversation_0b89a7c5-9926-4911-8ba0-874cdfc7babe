<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Address\Book;
use Magento\Framework\Escaper;

/** @var Book $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<section aria-labelledby="address" class="prose lg:prose-lg md:max-w-none pb-6">
    <h2 id="address" class="sr-only">
        <?= $escaper->escapeHtml(__('Default Addresses')) /** @phpstan-ignore-line */ ?>
    </h2>
    <div class="block-content md:flex md:flex-wrap md:gap-x-16">
        <?php if ($customerDefaultBillingAddress = $block->getDefaultBilling()): ?>
            <div class="box box-address-billing card">
                <h3 class="!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Default Billing Address')) /** @phpstan-ignore-line */ ?></h3>
                <div class="box-content">
                    <address>
                        <?= $block->getAddressHtml($block->getAddressById($customerDefaultBillingAddress)) ?>
                    </address>
                </div>
            </div>
        <?php else: ?>
            <div class="box box-billing-address card">
                <h3 class="!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Default Billing Address')) /** @phpstan-ignore-line */ ?></h3>
                <div class="box-content">
                    <p><?= $escaper->escapeHtml(__('You have no default billing address in your address book.')) /** @phpstan-ignore-line */ ?></p>
                </div>
            </div>
        <?php endif ?>

        <?php if ($customerDefaultBillingAddress = $block->getDefaultShipping()): ?>
            <div class="box box-address-shipping card">
                <h3 class="md:!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Default Shipping Address')) /** @phpstan-ignore-line */ ?></h3>
                <div class="box-content">
                    <address>
                        <?= $block->getAddressHtml($block->getAddressById($customerDefaultBillingAddress)) ?>
                    </address>
                </div>
            </div>
        <?php else: ?>
            <div class="box box-shipping-address card">
                <h3 class="md:!mt-0 !mb-4 pb-4 border-b border-secondary-200"><?= $escaper->escapeHtml(__('Default Shipping Address')) /** @phpstan-ignore-line */ ?></h3>
                <div class="box-content">
                    <p>
                        <?= $escaper->escapeHtml(__('You have no default shipping address in your address book.')) /** @phpstan-ignore-line */ ?>
                    </p>
                </div>
            </div>
        <?php endif ?>
    </div>
</section>
