<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Address\Grid;
use Magento\Framework\Escaper;

/** @var Grid $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<section aria-labelledby="address-list" class="block block-addresses-list prose lg:prose-lg md:max-w-none pb-6">
    <h2 id="address-list" class="block-title h3 !mb-4 pb-4 border-b border-secondary-200">
        <?= $escaper->escapeHtml(__('Address Book')) /** @phpstan-ignore-line */ ?>
    </h2>
    <div class="block-content" x-data="initAddresses()">
        <?php if ($additionalAddresses = $block->getAdditionalAddresses()): ?>
            <table>
                <thead>
                    <tr>
                        <th>
                            <?= $escaper->escapeHtml(__('Company')) /** @phpstan-ignore-line */ ?>
                        </th>
                        <th class="md-max:hidden">
                            <?= $escaper->escapeHtml(__('Name')) /** @phpstan-ignore-line */ ?>
                        </th>
                        <th class="sm-max:hidden">
                            <?= $escaper->escapeHtml(__('Street Address')) /** @phpstan-ignore-line */ ?>
                        </th>
                        <th>
                            <?= $escaper->escapeHtml(__('City')) /** @phpstan-ignore-line */ ?>
                        </th>
                        <th class="md-max:hidden">
                            <?= $escaper->escapeHtml(__('Country')) /** @phpstan-ignore-line */ ?>
                        </th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                <?php $i = 0; ?>
                    <?php foreach ($additionalAddresses as $address): ?>
                        <?php $i++ ?>
                        <tr>
                            <td>
                                <?= $escaper->escapeHtml($address->getCompany()) /** @phpstan-ignore-line */ ?>
                            </td>
                            <td class="md-max:hidden">
                                <?php if ($address->getLastname() || $address->getFirstname()): ?>
                                    <?php if ($address->getLastname()): ?>
                                        <?= $escaper->escapeHtml($address->getLastname()) /** @phpstan-ignore-line */ ?>,
                                    <?php endif; ?>
                                    <?php if ($address->getFirstname()): ?>
                                        <?= $escaper->escapeHtml($address->getFirstname()) /** @phpstan-ignore-line */ ?>
                                    <?php endif; ?>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td class="sm-max:hidden">
                                <?= $escaper->escapeHtml($block->getStreetAddress($address)) /** @phpstan-ignore-line */ ?>
                            </td>
                            <td>
                                <?= $escaper->escapeHtml($address->getCity()) /** @phpstan-ignore-line */ ?>
                            </td>
                            <td class="md-max:hidden">
                                <?= $escaper->escapeHtml($block->getCountryByCode($address->getCountryId())) /** @phpstan-ignore-line */ ?>
                            </td>
                            <td>
                                <a class="action edit inline-block text-sm underline text-secondary-darker"
                                    title="<?= $escaper->escapeHtmlAttr(__('Edit')) ?>"
                                    href="<?= $escaper->escapeUrl($block->getUrl(
                                        'customer/address/edit',
                                        ['id' => $address->getId()]
                                    )) ?>">
                                    <?= $heroicons->pencilAltHtml(); ?>
                                    <span></span>
                                </a>
                                <a class="action delete ml-2 inline-block text-sm underline text-secondary-darker"
                                title="<?= $escaper->escapeHtmlAttr(__('Delete')) ?>"
                                @click.prevent="deleteAddressById(<?= $escaper->escapeJs($address->getId()) /** @phpstan-ignore-line */ ?>)" href="#">
                                    <?= $heroicons->trashHtml(); ?>
                                </a>
                            </td>
                        </tr>

                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if ($block->getChildHtml('pager')): ?>
                <div class="customer-addresses-toolbar toolbar bottom">
                    <?= $block->getChildHtml('pager') ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <p class="empty">
                <?= $escaper->escapeHtml(__('You have no other address entries in your address book.')) /** @phpstan-ignore-line */ ?>
            </p>
        <?php endif ?>

        <script>
            function initAddresses() {
                return {
                    deleteAddressById(id) {
                        if (window.confirm(
                            '<?= $escaper->escapeJs(__('Are you sure you want to delete this address?')) ?>'
                        )) {
                            hyva.postForm(
                                {
                                    "action": '<?= $escaper->escapeJs($block->getDeleteUrl()) ?>',
                                    "data": {
                                        "id": id
                                    }
                                }
                            );
                        }
                    }
                }
            }
        </script>
    </div>
    <div class="actions-toolbar gap-x-6 prose">
        <div class="primary flex grow not-prose">
            <a href="<?= $escaper->escapeUrl($block->getUrl('customer/address/new')) ?>" class="btn btn-primary grow">
                <?= $escaper->escapeHtml(__('Add New Address')) /** @phpstan-ignore-line */ ?>
            </a>
        </div>
        <div class="secondary">
            <a class="action back text-secondary-700" href="<?= $escaper->escapeUrl($block->getUrl('customer/account')) ?>">
                <?= $escaper->escapeHtml(__('Back to overview')) /** @phpstan-ignore-line */ ?>
            </a>
        </div>
    </div>
</div>

