<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ReCaptcha;
use Magento\Customer\Block\Form\Edit as CustomerEdit;
use Magento\Customer\Block\Widget\Name;
use Magento\Framework\Escaper;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var CustomerEdit $block */
/** @var Escaper $escaper */
/** @var ReCaptcha|null $recaptcha */
/** @var HeroiconsSolid $heroicons */
/** @var ViewModelRegistry $viewModels */

// Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency
// on the Magento_ReCaptchaCustomer module
$recaptcha = $block->getData('viewModelRecaptcha');
$heroIcons = $viewModels->require(HeroiconsSolid::class);
$minimumPasswordLength = $block->getMinimumPasswordLength();
$passwordMinCharacterSets = $block->getRequiredCharacterClassesNumber();

?>
<div class="prose lg:prose-lg pb-6">

    <form class="form form-edit-account"
          action="<?= $escaper->escapeUrl($block->getUrl('customer/account/editPost')) ?>"
          method="post" id="form-validate"
          enctype="multipart/form-data"
          x-data="Object.assign(hyva.formValidation($el), initForm())"
          @submit.prevent="submitForm"
          autocomplete="off">
        <fieldset class="fieldset info">
            <?= $block->getBlockHtml('formkey') ?>
            <legend class="legend h2"><span><?= $escaper->escapeHtml(__('Account Information')) /** @phpstan-ignore-line */ ?></span></legend>
            <br>
            <?= $block->getLayout()->createBlock(Name::class)->setObject($block->getCustomer())->toHtml() /** @phpstan-ignore-line */ ?>
            <?php 
                /** @var \Magento\Customer\Block\Widget\Dob $dob */
                $dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class);
            
                /** @var \Magento\Customer\Block\Widget\Taxvat $taxvat */
                $taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class);
            
                /** @var \Magento\Customer\Block\Widget\Gender $gender */
                $gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class);
            ?>
            <?php if ($dob->isEnabled()): ?>
                <?= $dob->setDate($block->getCustomer()->getDob())->toHtml() ?>
            <?php endif ?>
            <?php if ($taxvat->isEnabled()): ?>
                <?= $taxvat->setTaxvat($block->getCustomer()->getTaxvat())->toHtml() ?>
            <?php endif ?>
            <?php if ($gender->isEnabled()): ?>
                <?= $gender->setGender($block->getCustomer()->getGender())->toHtml() ?>
            <?php endif ?>
    
            <div class="field choice">
                <input type="checkbox" name="change_password" id="change-password" value="1"
                       title="<?= $escaper->escapeHtmlAttr(__('Change Password')) ?>"
                       x-on:change="showPasswordFields = !showPasswordFields"
                    <?php if ($block->getChangePassword()): ?> checked="checked"<?php endif; ?>
                       class="checkbox"/>
                <label class="label" for="change-password">
                    <span><?= $escaper->escapeHtml(__('Change Password')) /** @phpstan-ignore-line */ ?></span>
                </label>
            </div>
    
            <template x-if="showPasswordFields">
                <div class="field field-reserved password current required !mt-8">
                    <label class="label" for="current-password">
                        <span><?= $escaper->escapeHtml(__('Current Password')) /** @phpstan-ignore-line */ ?></span>
                    </label>
                    <div class="control flex items-center">
                        <input
                            type="password" class="form-input"
                            name="current_password" id="current-password"
                            data-input="current-password"
                            required
                            autocomplete="off"
                        >
                    </div>
                </div>
            </template>
    
            <template x-if="showPasswordFields">
                <div>
                    <div class="field field-reserved">
                        <label class="label" for="password"><span>
                                <?= $escaper->escapeHtml(__('New Password')) /** @phpstan-ignore-line */ ?>
                        </span></label>
                        <div class="control flex flex-col gap-y-4">
                            <input 
                                type="password"
                                class="form-input w-full"
                                name="password"
                                id="password"
                                required
                                data-validate='{"password-strength": {"minCharacterSets": <?= (int) $passwordMinCharacterSets ?>}}'
                                @input="onChange"
                                minlength="<?= $escaper->escapeHtmlAttr($minimumPasswordLength) ?>" autocomplete="off"
                            >
                            <div
                                id="password-strength-meter-container"
                                data-role="password-strength-meter"
                                aria-live="polite"
                                class="w-full"
                            >
                                <div id="password-strength-meter" class="password-strength-meter">
                                    <?= $escaper->escapeHtml(__('Password Strength')) /** @phpstan-ignore-line */ ?>:
                                    <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                        <?= $escaper->escapeHtml(__('No Password')) /** @phpstan-ignore-line */ ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <div class="field field-reserved">
                        <label class="label" for="password-confirmation">
                            <span><?= $escaper->escapeHtml(__('Confirm New Password')) /** @phpstan-ignore-line */ ?></span>
                        </label>
                        <div class="control flex flex-wrap">
                            <input 
                                type="password"
                                class="form-input"
                                name="password_confirmation" 
                                id="password-confirmation"
                                data-validate='{"equalTo": "password"}'
                                @input="onChange"
                                autocomplete="off"
                            >
                        </div>
                    </div>
                </div>
            </template>
        </fieldset>
        <?= $block->getChildHtml('form_additional_info') ?>
        <?= $recaptcha ? $recaptcha->getInputHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>
        <div class="actions-toolbar gap-x-6">
            <div class="primary flex grow not-prose">
                <button type="submit" class="action save primary grow">
                    <?= $escaper->escapeHtml(__('Save details')) /** @phpstan-ignore-line */ ?>
                </button>
            </div>
            <div class="secondary">
                <a class="action back text-secondary-700" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                    <?= $escaper->escapeHtml(__('Back to overview')) /** @phpstan-ignore-line */ ?>
                </a>
            </div>
        </div>
    </form>
    <div class="w-full">
        <?= $recaptcha ? $recaptcha->getLegalNoticeHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>
    </div>
</div>
<script>
    function initForm() {
        return {
            errors: 0,
            hasCaptchaToken: 0,
            displayErrorMessage: false,
            errorMessages: [],
            showPasswordNew: false,
            showPasswordConfirm: false,
            showPasswordCurrent: false,
            showPasswordFields: <?= $block->getChangePassword() ? $escaper->escapeJs('true') : $escaper->escapeJs('false') ?>,
            setErrorMessages(messages) {
                this.errorMessages = [messages]
                this.displayErrorMessage = this.errorMessages.length
            },
            submitForm() {
                this.validate()
                    .then(() => {
                        // Do not rename $form, the variable is expected to be declared in the recaptcha output
                        const $form = document.querySelector('#form-validate');
                        <?= $recaptcha ? $recaptcha->getValidationJsHtml(ReCaptcha::RECAPTCHA_FORM_ID_CUSTOMER_EDIT) : '' ?>

                        if (this.errors === 0) {
                            $form.submit();
                        }
                    })
                    .catch((invalid) => {
                        if (invalid.length > 0) {
                            invalid[0].focus();
                        }
                    })
            }
        }
    }
</script>
