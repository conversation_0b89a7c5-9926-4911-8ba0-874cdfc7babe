<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\CustomerRegistration;
use Magento\Customer\Block\Account\Customer;
use Magento\Customer\Model\Account\Redirect;
use Magento\Framework\Escaper;

/** @var Customer $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var CustomerRegistration $customerRegistration */
$customerRegistration = $viewModels->require(CustomerRegistration::class);
?>

<?php if ($block->customerLoggedIn()): ?>
    <div class="relative inline-flex gap-x-2">
        <a
            class="btn btn-primary btn-with-icon btn-with-icon--left !pl-10 h-11"
            href="<?= $escaper->escapeUrl($block->getUrl('customer/account')) ?>"
        >
            <?= $escaper->escapeHtml(__('My Account')); /** @phpstan-ignore-line */ ?> <span class="icon-medal !left-3"></span>
        </a>

        <a class="btn btn-round h-11 aspect-square p-0 items-center justify-center" href="<?= $escaper->escapeUrl($block->getUrl('favorites')) ?>">
            <span class="icon-favorite text-2xl-em"></span>
            <span class="sr-only"><?= $escaper->escapeHtml(__('My Wish List')); /** @phpstan-ignore-line */ ?></span>
        </a>
    </div>
<?php endif;
