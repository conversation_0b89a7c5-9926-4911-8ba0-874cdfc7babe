<?php

declare(strict_types=1);

use <PERSON>iemans\CustomerGroupPriceList\ViewModel\PriceList;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var PriceList $priceList */
$priceList = $viewModels->require(PriceList::class);

$customer = $block->getCustomer();
$groupId = (int)$customer->getGroupId();
$pdfPriceList = $priceList->getPdfPriceList($groupId);
$excelPriceList = $priceList->getExcelPriceList($groupId);

if (!$pdfPriceList && !$excelPriceList) {
    return '';
}
?>
<div class="flex mb-4 px-5 py-6 gap-4 bg-secondary-200 flex-wrap items-center">
    <span class="mr-auto">
        <?= $escaper->escapeHtml(__('Download your personal pricelist')) ?>
    </span>

    <div class="flex flex-wrap gap-4">
        <?php if ($pdfPriceList): ?>
            <a
                href="
                    <?= $escaper->escapeUrl($block->getUrl('biemans-price-list/pricelist/download', [
                        'id' => $groupId,
                        'type' => 'pdf'
                    ])); ?>
                "
                class="btn btn-ghost btn-with-icon border-current"
            >
                <?= $escaper->escapeHtml(__('Download PDF')) ?>
                <?= $heroicons->downloadHtml('w-4 h-4 !transition-none icon') ?>
            </a>
        <?php endif; ?>
    
        <?php if ($excelPriceList): ?>
            <a
                href="<?= $escaper->escapeUrl($block->getUrl('biemans-price-list/pricelist/download', [
                        'id' => $groupId,
                        'type' => 'excel'
                    ])); ?>
                "
                class="btn btn-ghost btn-with-icon border-current"
            >
                <?= $escaper->escapeHtml(__('Download Excel')) ?>
                <?= $heroicons->downloadHtml('w-4 h-4 !transition-none icon') ?>
            </a>
        <?php endif; ?>
    </div>
</div>
