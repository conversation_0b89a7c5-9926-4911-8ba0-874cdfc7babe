<?php

use Hyva\Theme\Model\LocaleFormatter;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Pager;

/** @var Pager $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$paginationUrlAnchor = $block->hasData('pagination_url_anchor')
    ? '#' . $escaper->escapeHtmlAttr((string) $block->getData('pagination_url_anchor'))
    : '';

$pagerItemClass = "";
$pagerItemBtnClass = $pagerItemClass . " btn btn-ghost w-8 h-8 p-0 rounded-full";

$itemClass = "item";

?>
<?php if ($block->getCollection()->getSize()): ?>

    <?php if ($block->getUseContainer()): ?>
        <div class="toolbar toolbar-products md:px-8 sm-max:pt-1.5 sm-max:pb-1 flex xs-max:flex-wrap items-center toolbar-products--bottom h-14 px-container-padding flex lg:grid lg:grid-cols-[1fr_auto_1fr] gap-4 items-center mt-auto">
    <?php endif ?>

    <?php if ($block->getShowAmounts()): ?>
        <p class="toolbar-amount text-base leading-none">
            <?php if ($block->getLastPageNum() > 1): ?>
                <?= /** @phpstan-ignore-line */ $escaper->escapeHtml(
                    __(
                        'Items %1-%2 of %3',
                        '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getFirstNum()) . '</span>',
                        '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getLastNum()) . '</span>',
                        '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
                    ),
                    ['span']
                ) ?>
            <?php elseif ($block->getTotalNum() == 1): ?>
                <?= /** @phpstan-ignore-line */ $escaper->escapeHtml(
                    __(
                        '%1 Item',
                        '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
                    ),
                    ['span']
                ) ?>
            <?php else: ?>
                <?= /** @phpstan-ignore-line */ $escaper->escapeHtml(
                    __(
                        '%1 Items',
                        '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
                    ),
                    ['span']
                ) ?>
            <?php endif; ?>
        </p>
    <?php endif ?>

    <div class="flex justify-center md-max:ml-auto">
        <?php if ($block->getLastPageNum() > 1): ?>
            <nav class="inline-flex items-center pages" aria-labelledby="pagination-label">
                <span id="pagination-label" class="sr-only"><?= $escaper->escapeHtml(__('Pagination')) /** @phpstan-ignore-line */ ?></span>
                <ol class="pages-items items inline-flex gap-2" data-controls="<?= $block->isFirstPage() ? 'first' : '' ?><?= $block->isLastPage() ? 'last' : '' ?>" data-pages="<?= $block->getLastPageNum() ?>">
                    <?php if (!$block->isFirstPage()): ?>
                        <li class="<?= $itemClass ?> pages-item-previous">
                            <?php $text = $block->getAnchorTextForPrevious() ? $block->getAnchorTextForPrevious() : ''; ?>
                            <a
                                href="<?= $escaper->escapeUrl($block->getPreviousPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="
                                    <?= $escaper->escapeHtmlAttr($text ? 'link ' : 'action ') ?>
                                    <?= /* @noEscape */ $pagerItemBtnClass ?>
                                "
                            >
                                <?php if ($text): ?>
                                    <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) /** @phpstan-ignore-line */ ?></span>
                                    <span><?= $escaper->escapeHtml($text) /** @phpstan-ignore-line */ ?></span>
                                <?php else: ?>
                                    <span>
                                        <span class="sr-only"><?= $escaper->escapeHtml(__('Previous')) /** @phpstan-ignore-line */ ?></span>
                                        <span class="icon-chev-left text-2xl leading-none" aria-hidden="true"></span>
                                    </span>
                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($block->canShowFirst()): ?>
                        <li
                            class="<?= $itemClass ?> -ml-px"
                        >
                            <a
                                href="<?= $escaper->escapeUrl($block->getFirstPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="page first <?= /* @noEscape */ $pagerItemBtnClass ?>"
                            >
                                <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) /** @phpstan-ignore-line */ ?></span>
                                <span><?= $escaper->escapeHtml($localeFormatter->formatNumber(1)) /** @phpstan-ignore-line */ ?></span>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($block->canShowPreviousJump()): ?>
                        <li class="<?= $itemClass ?> -ml-px">
                            <a
                                href="<?= $escaper->escapeUrl($block->getPreviousJumpUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="page previous jump <?= /* @noEscape */ $pagerItemBtnClass ?>"
                                aria-label="<?= $escaper->escapeHtmlAttr(__(
                                    'Skip to page %1',
                                    $localeFormatter->formatNumber($block->getPreviousJumpPage())
                                )) ?>"
                            >
                            <span aria-label="<?= $escaper->escapeHtml(__('Jump backward')) /** @phpstan-ignore-line */ ?>">...</span>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php foreach ($block->getFramePages() as $page): ?>
                        <?php if ($block->isPageCurrent($page)): ?>
                            <li
                                class="<?= $itemClass ?>"
                                aria-label="<?= $escaper->escapeHtml(__('Page') . ' ' . $page) /** @phpstan-ignore-line */ ?>"
                                data-pager="current"
                            >
                                <span
                                    class="page <?= /* @noEscape */ $pagerItemBtnClass ?> cursor-not-allowed bg-grayscale-700 border-grayscale-700 text-white"
                                    aria-current="page"
                                >
                                    <span class="sr-only label">
                                        <?= /** @phpstan-ignore-line */ $escaper->escapeHtml(
                                            __('You\'re currently reading page')
                                        ) ?>
                                    </span>
                                    <span><?= $escaper->escapeHtml($localeFormatter->formatNumber($page)) /** @phpstan-ignore-line */ ?></span>
                                </span>
                            </li>
                        <?php else: ?>
                            <li class="<?= $itemClass ?>">
                                <a
                                    href="<?= $escaper->escapeUrl($block->getPageUrl($page)) . /* @noEscape */ $paginationUrlAnchor ?>"
                                    class="page <?= /* @noEscape */ $pagerItemBtnClass ?>"
                                >
                                    <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) /** @phpstan-ignore-line */ ?></span>
                                    <span><?= $escaper->escapeHtml($localeFormatter->formatNumber($page)) /** @phpstan-ignore-line */ ?></span>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>

                    <?php if ($block->canShowNextJump()): ?>
                        <li class="<?= $itemClass ?> -ml-px">
                            <a
                                href="<?= $escaper->escapeUrl($block->getNextJumpUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="page next jump <?= /* @noEscape */ $pagerItemBtnClass ?>"
                                aria-label="<?= $escaper->escapeHtmlAttr(__(
                                    'Skip to page %1',
                                    $localeFormatter->formatNumber($block->getNextJumpPage())
                                )) ?>"
                            >...</a>
                        </li>
                    <?php endif; ?>

                    <?php if ($block->canShowLast()): ?>
                        <li class="<?= $itemClass ?> -ml-px text-gray-500">
                            <a
                                href="<?= $escaper->escapeUrl($block->getLastPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="page last <?= /* @noEscape */ $pagerItemBtnClass ?>"
                            >
                                <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) /** @phpstan-ignore-line */ ?></span>
                                <span><?= $escaper->escapeHtml($localeFormatter->formatNumber($block->getLastPageNum())) /** @phpstan-ignore-line */ ?></span>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if (!$block->isLastPage()): ?>
                        <li class="<?= $itemClass ?> pages-item-next -ml-px">
                            <?php $text = $block->getAnchorTextForNext() ? $block->getAnchorTextForNext() : ''; ?>
                            <a
                                href="<?= $escaper->escapeUrl($block->getNextPageUrl()) . /* @noEscape */ $paginationUrlAnchor ?>"
                                class="
                                    next
                                    <?= $text ? 'link ' : 'action ' ?> 
                                    <?= /* @noEscape */ $pagerItemBtnClass ?>
                                "
                            >
                                <?php if ($text): ?>
                                    <span class="sr-only label"><?= $escaper->escapeHtml(__('Page')) /** @phpstan-ignore-line */ ?></span>
                                    <span><?= $escaper->escapeHtml($text) /** @phpstan-ignore-line */ ?></span>
                                <?php else: ?>
                                    <span>
                                        <span class="sr-only"><?= $escaper->escapeHtml(__('Next')) /** @phpstan-ignore-line */ ?></span>
                                        <span class="icon-chev-right text-2xl leading-none" aria-hidden="true"></span>
                                    </span>
                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ol>
            </nav>
        <?php endif; ?>
    </div>

    <?php if ($block->getUseContainer()): ?>
        </div>
    <?php endif ?>

<?php endif ?>
