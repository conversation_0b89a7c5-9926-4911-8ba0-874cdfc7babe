<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <remove src="Mageplaza_QuickOrder::css/source/quickorder.css" />
        <remove src="Mageplaza_Core::css/font-awesome.min.css" />
    </head>
    <body>
        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Home</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Home</item>
                    <item name="label" xsi:type="string">Home</item>
                    <item name="link" xsi:type="string">/</item>
                </argument>
            </action>
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Quick order</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Quick order</item>
                    <item name="label" xsi:type="string">Quick order</item>
                </argument>
            </action>
        </referenceBlock>

        <referenceBlock name="mp.quickorder.design" remove="true" />
    </body>
</page>
