<?php

declare(strict_types=1);

use Magento\Catalog\Helper\Data;
use Magento\Framework\Escaper;
use Smile\ElasticsuiteCatalog\Block\Navigation\FilterRenderer;

/** @var FilterRenderer $block */
/** @var Escaper $escaper */
/** @var array $filterItems */
/** @var Data $catalogHelper */

$catalogHelper = $this->helper('\Magento\Catalog\Helper\Data');
?>
<ol class="items">
    <?php foreach ($filterItems as $filterItem): ?>
        <li class="item my-1">
            <?php if ($filterItem->getCount() > 0): ?>
                <a href="<?= $escaper->escapeUrl($filterItem->getUrl()) ?>">
                    <?php if ($this->isMultipleSelectEnabled()) : ?>
                        <input type="checkbox" onclick="this.parentNode.click();" <?php if($filterItem->getIsSelected()) : ?>checked<?php endif; ?> />
                    <?php endif; ?>
                    <?= $filterItem->getLabel() ?>
                    <?php if ($catalogHelper->shouldDisplayProductCountOnLayer()): ?>
                        (<span class="count"><?= $filterItem->getCount()?></span>)
                    <?php endif; ?>
                </a>
            <?php else:?>
                <?= $filterItem->getLabel() ?>
                <?php if ($catalogHelper->shouldDisplayProductCountOnLayer()): ?>
                    (<span class="count"><?= $filterItem->getCount()?></span>)
                <?php endif; ?>
            <?php endif; ?>
        </li>
    <?php endforeach ?>
</ol>
