<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\LayeredNavigation\Block\Navigation;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Navigation $block */
/** @var Escaper $escaper */

/** @var \Magento\Catalog\Model\Category|null $category */
$category = $block->getLayer()->getCurrentCategory();
$categoryName = '';
$canShow = $block->canShowBlock();

/** @var \Magento\Catalog\Helper\Data $catalogHelper */
$catalogHelper = $this->helper(\Magento\Catalog\Helper\Data::class);

if (
    !empty($category)
    && !empty($category->getEntityId())
) {
    $categoryName = $category->getName();

    $categoryLayout = $category->getData('page_layout');

    $canShow = !in_array($categoryLayout, ['1column', 'cms-full-width', 'category-full-width', 'product-full-width']);
}

$activeFiltersName = [];
foreach ($block->getLayer()->getState()->getFilters() as $activeFilter) {
    $activeFiltersName[] = $activeFilter->getName();
}
?>
<?php if ($canShow) : ?>
    <div
        class="w-full"
        x-data="initLayeredNavigation()"
        x-init="checkIsMobileResolution()"
        @toogle-filters.window="isMobile = !isMobile"
    >
        <div id="products-filter" class="block-content filter-content" x-show="isMobile && blockOpen || !isMobile">

            <div class="flex justify-end py-2 md:hidden px-container-padding">
                <button
                    @click="isMobile = !isMobile"
                    class="btn btn-ghost gap-x-2"
                    aria-controls="products-filter"
                >
                    <span><?= $escaper->escapeHtml(__('Close')) /** @phpstan-ignore-line */ ?></span>
                    <span class="text-xl leading-none icon-close" aria-hidden="true"></span>
                </button>
            </div>

            <?php $activeFilters = json_decode($block->getActiveFilters()) ?>

            <?php
            /** @var \Smile\ElasticsuiteCatalog\Model\Layer\Filter\Attribute $filter */
            foreach ($block->getFilters() as $filter):
                if (
                    $filter->getData('attribute_model')
                    && ($filter->getAttributeModel()->getAttributeCode()
                        === \Biemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE)
                ) {
                    continue;
                }
            ?>

                <?php if ($catalogHelper->getCategory() && ($filter->getName() == __('Category'))): ?>
                    <?= /* @noEscape */ $block->getChildBlock('renderer')->render($filter) /** @phpstan-ignore-line */ ?>
                <?php else: ?>

                    <?php if ($filter->getItemsCount() || in_array($filter->getName(), $activeFiltersName)): ?>
                        <div class="filter-option card" x-data="{ open: <?= ($filter->getName() == __('Category')) ? 'true' : ((in_array($filter->getName(), $activeFiltersName)) ? 'true' : 'false') ?> }">
                            <button
                                class="flex items-center justify-between w-full px-4 leading-none text-left cursor-pointer filter-options-title group h-14 xl:px-8 shadow-outline-secondary-500"
                                @click="open = !open"
                            >
                                <span class="font-bold uppercase transition-colors title font-subheading group-hover:text-primary group-focus-visible:text-primary" :class="open ? 'text-primary' : ''">
                                    <?= $escaper->escapeHtml(__('Filter on')) ?> <?= $escaper->escapeHtml(__($filter->getName())) /** @phpstan-ignore-line */ ?>
                                </span>
                                <span class="leading-none text-2xl-em" :class="open ? 'icon-remove' : 'icon-add'" aria-hidden="true"></span>
                            </button>
                            <div class="hidden px-4 py-4 filter-options-content xl:px-8 xl:py-8 shadow-outline-secondary-500"
                                 :class="{ 'hidden': !open , 'block': 'open' }"
                            >
                                <?php if ($filter->getName() == __('Category')): ?>
                                    <?= $block->getChildHtml('category.state') ?>
                                <?php endif; ?>

                                <?= /* @noEscape */
                                $block->getChildBlock('renderer')->render($filter) /** @phpstan-ignore-line */ ?>
                            </div>
                        </div>
                    <?php endif; ?>

                <?php endif; ?>

            <?php endforeach; ?>
        </div>
    </div>
    <script>
        function initLayeredNavigation() {
            return {
                isMobile: false,
                blockOpen: false,
                checkIsMobileResolution: function () {
                    this.isMobile = window.outerWidth < 768
                }
            }
        }
    </script>
<?php endif; ?>
