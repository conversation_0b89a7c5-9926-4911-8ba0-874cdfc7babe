<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Search\Helper\Data as SearchHelper;

/**
 * Template for quick search mini form.
 * Overridden to manage template injection for the rendering of autocomplete results.
 *
 * @var \Smile\ElasticsuiteCore\Block\Search\Form\Autocomplete $block
 * @var SearchHelper $helper
 * @var Escaper $escaper
 * @var \Hyva\Theme\Model\ViewModelRegistry $viewModels
 * @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons
 */

$helper        = $this->helper(SearchHelper::class);
$suggestionUrl = $helper->getResultUrl() . '?' . $helper->getQueryParamName() . '=';
$heroicons     = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
$templates     = json_decode($block->getJsonSuggestRenderers(), true);

?>

<script>
    function initMiniSearchComponent() {
        "use strict";

        return {
            show: false,
            formSelector: "#search_mini_form",
            url: "<?= /* @escapeNotVerified */ $block->getUrl('search/ajax/suggest') ?>",
            destinationSelector: "#search_autocomplete",
            templates: <?= /* @noEscape */ $block->getJsonSuggestRenderers() ?>,
            priceFormat: <?= /* @noEscape */ $block->getJsonPriceFormat() /** @phpstan-ignore-line */ ?>,
            minSearchLength: <?= /* @escapeNotVerified */ $helper->getMinQueryLength() ?>,
            searchResultsByType: {},
            currentRequest: null,
            searchLink: "",
            isLoading: false,

            /**
             * Get search results.
             */
            getSearchResults: function (value) {
                if (value.length < parseInt(this.minSearchLength, 10)) {
                    this.searchResultsByType = [];

                    return false;
                }

                let url = this.url + '?' + new URLSearchParams({
                    q: value,
                    _: Date.now()
                }).toString();

                if (this.currentRequest !== null) {
                    this.currentRequest.abort();
                }
                this.currentRequest = new AbortController();
                this.searchLink = "<?= $escaper->escapeUrl($block->getUrl('catalogsearch/result')) ?>?q=" + value;
                this.isLoading = true;

                fetch(url, {
                    method: 'GET',
                    signal: this.currentRequest.signal,
                }).then((response) => {
                    if (response.ok) {
                        return response.json();
                    }
                }).then((data)  => {
                    this.show = data.length > 0;

                    this.searchResultsByType = data.reduce((acc, result) => {
                        let column = (result.type === 'product') ? 'main' : 'sidebar';
                        // Init columns, first sidebar and then main
                        if (! acc['sidebar']) acc['sidebar'] = [];
                        if (! acc['main']) acc['main'] = [];

                        if (! acc[column][result.type]) acc[column][result.type] = [];
                        acc[column][result.type].push(result);
                        return acc;
                    }, {});

                    this.isLoading = false;
                }).catch((error) => {
                });
            },
        }
    }
</script>
<div id="search-content" x-show="true">
    <div x-data="initMiniSearchComponent()">
        <template x-if="isLoading">
            <?= $block->fetchView((string) $block->getTemplateFile('Hyva_Theme::ui/loading.phtml')) ?>
        </template>
        <form class="form minisearch" action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get">
            <label>
                <span class="sr-only" ><?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?></label>
                <div class="relative">
                    <span class="icon-search absolute top-1/2 left-3 -translate-y-1/2 text-2xl text-black pointer-events-none"></span>
                    <input
                        x-on:input.debounce="getSearchResults($event.target.value)"
                        x-ref="searchInput"
                        type="search"
                        class="form-input bg-transparent pl-11"
                        autocapitalize="off" autocomplete="off" autocorrect="off"
                        name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                        value="<?= $escaper->escapeHtmlAttr($helper->getEscapedQueryText()) ?>"
                        placeholder="<?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?>"
                        maxlength="<?= $escaper->escapeHtmlAttr((string)$helper->getMaxQueryLength()) ?>"
                    />
                </div>
            </label>
            <div id="search_autocomplete" class="search-autocomplete relative z-50 w-full" x-show="show && !isLoading" style="display:none;">
                <div class="absolute top-2 lg:top-3 bg-white w-full flex sm-max:flex-col lg:w-screen lg:max-w-4xl xl:max-w-6xl lg:left-1/2 lg:-translate-x-1/2">
                    <template x-for="(searchResultsByColumn, index) in searchResultsByType" :key="index">
                        <div :class="{'': (index === 'main'), 'lg:min-w-[15rem] xl:min-w-[22rem] md:basis-80 lg:basis-auto px-4 py-2 lg:p-9 lg:grow shadow-outline-secondary-500': (index === 'sidebar')}">
                            <template x-for="searchResultByType in Object.values(searchResultsByColumn)">
                                <div :class="{'grid grid-cols-2 lg:grid-cols-3': (index === 'main'), 'flex flex-col': (index === 'sidebar')}">
                                    <template x-if="index !== 'main' && searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].title && templates[searchResultByType[0].type].titleRenderer === undefined">
                                        <div class="h2 lg:h4 mt-4 md-max:mb-2 font-bold" x-text="templates[searchResultByType[0].type].title"></div>
                                    </template>
                                    <template x-if="index !== 'main' && searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].titleRenderer !== undefined">
                                        <div class="h2 lg:h4 mt-4 md-max:mb-2 font-bold" x-text="window[templates[searchResultByType[0].type].titleRenderer](searchResultByType)"></div>
                                    </template>

                                    <template x-for="searchResult in searchResultByType">
                                        <div class="inline-flex" :class="{'shadow-outline-secondary-500': (index === 'main'), '': (index === 'sidebar')}">
                                            <?php foreach(json_decode($block->getJsonSuggestRenderers(), true) as $renderer): ?>
                                                <?= /** @phpstan-ignore-next-line */
                                                $block->getLayout()
                                                    ->createBlock(\Magento\Framework\View\Element\Template::class)
                                                    ->setTemplate($renderer['template'])
                                                    ->setData('suggestion_url', $suggestionUrl)
                                                    ->toHtml()
                                                ?>
                                            <?php endforeach; ?>
                                        </div>
                                    </template>

                                    <template x-if="index === 'main'">
                                        <div class="col-span-2 lg:col-span-3 shadow-outline-secondary-500">
                                            <a :href="searchLink" class="btn btn-primary btn-size-lg rounded-none w-full justify-center">
                                                <?= $escaper->escapeHtml(__('See products in')); /** @phpstan-ignore-line */ ?> <?= $escaper->escapeHtml(__('All categories')); /** @phpstan-ignore-line */ ?>
                                            </a>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
            <?= $block->getChildHtml() ?>
        </form>
    </div>
</div>
