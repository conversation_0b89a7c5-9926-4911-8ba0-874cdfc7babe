<?php

declare(strict_types=1);

use Biemans\Decimals\ViewModel\Precision;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Currency;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Currency $currencyViewModel */
$currencyViewModel = $viewModels->require(Currency::class);
/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$jsLocale = str_replace('_', '-', $storeConfig->getStoreConfig('general/locale/code'));
/** @var Precision $precisionViewModel */
$precisionViewModel = $viewModels->require(Precision::class);
?>
<script>
    'use strict';
    (function( hyva, undefined ) {

        // Format prices with 3 decimals
        hyva.formatPrice = (value, showSign) => {
            let formatter = new Intl.NumberFormat(
                '<?= $escaper->escapeJs($jsLocale) ?>',
                {
                    style: 'currency',
                    currency: '<?= $escaper->escapeHtml($currencyViewModel->getCurrentCurrencyCode()) /** @phpstan-ignore-line */ ?>',
                    signDisplay: showSign ? "always" : "auto",
                    minimumFractionDigits: <?= $precisionViewModel->getPrecision(); ?>
                }
            );
            return (typeof Intl.NumberFormat.prototype.formatToParts === 'function') ?
                formatter.formatToParts(value).map(({type, value}) => {
                    switch (type) {
                        case 'currency':
                            return '<?= $currencyViewModel->getCurrentCurrencySymbol() ?: ""; ?>' || value;
                        case 'minusSign':
                            return '- ';
                        case 'plusSign':
                            return '+ ';
                        default :
                            return value;
                    }
                }).reduce((string, part) => string + part) :
                formatter.format(value);
        }

    }( window.hyva = window.hyva || {} ));
</script>
