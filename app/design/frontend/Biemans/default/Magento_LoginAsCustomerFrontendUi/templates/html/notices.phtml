<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\LoginAsCustomerFrontendUi\ViewModel\Configuration;

/**
 * @var Template $block
 * @var Escaper $escaper
 * @var ViewModelRegistry $viewModels
 */
$viewFileUrl = $block->getViewFileUrl('Magento_LoginAsCustomerFrontendUi::images/magento-icon.svg');

/** @var Configuration $configurationViewModel */
$configurationViewModel = $viewModels->require(Configuration::class);
if (!$configurationViewModel->isEnabled()) {
    return;
}
?>
<div class="fixed bottom-0 left-0 z-10 w-full bg-grayscale-700 login-as-customer"
     x-data="initLoginAsCustomer()"
     @private-content-loaded.window="receiveCustomerData(event.detail.data)"
>
    <div class="container px-1 py-1 mx-auto sm:px-6 lg:px-8" x-show="isVisible">
        <div class="flex flex-wrap items-center justify-between">
            <div class="flex items-center flex-1 w-0">
                <span class="flex p-2">
                   <img class="logo-img" src="<?= $escaper->escapeUrl($viewFileUrl) ?>" alt="Magento" />
                </span>
                <p class="ml-3 font-medium text-white truncate">
                    <span x-text="notificationText"></span>
                </p>
            </div>
            <div class="order-2 shrink-0 sm:order-3 sm:ml-3">
                <?= $block->getChildHtml('login-as-customer-notice-links') ?>
            </div>
        </div>
    </div>
</div>

<script>
    "use strict";

    function initLoginAsCustomer() {
        return {
            isVisible: false,
            adminUserId: null,
            websiteName: null,
            fullname: null,
            notificationText: '',
            receiveCustomerData: function receiveCustomerData(data) {
                if (data.customer) {
                    this.fullname = data.customer.fullname;
                }

                if (data.loggedAsCustomer) {
                    this.adminUserId = data.loggedAsCustomer.adminUserId;
                    this.websiteName = data.loggedAsCustomer.websiteName;
                }

                this.updateBanner();
            },

            updateBanner: function updateBanner() {
                this.isVisible = !!this.adminUserId;

                if (this.fullname !== undefined && this.websiteName !== undefined) {
                    this.notificationText = '<?=
                        $escaper->escapeHtml(__('You are connected as <strong>%1</strong> on %2'), ['<strong>']) /** @phpstan-ignore-line */  ?>'
                        .replace('%1', this.fullname)
                        .replace('%2', this.websiteName);
                }
            }
        }
    }
</script>
