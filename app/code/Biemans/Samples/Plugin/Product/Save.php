<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Samples\Plugin\Product;

use <PERSON><PERSON>mans\Catalog\Setup\Patch\Data\AddDefaultQtyAttribute;
use <PERSON><PERSON>mans\Samples\Service\QtyManager;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\Catalog\Model\ResourceModel\Product as ProductEntity;
use Magento\Catalog\Model\ResourceModel\Product as ProductResourceModel;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\Store;

class Save
{
    public function __construct(
        private readonly QtyManager $service,
        private readonly ProductResourceModel $productResource
    ) {
    }

    /**
     * On saving, we change Stock Options:
     * 1. if product will be new from date in the future we update data
     * 2. if product has new from date in past but new to date in the future we make sure default configs are set
     *
     * @throws CouldNotSaveException
     * @throws LocalizedException
     */
    public function aroundSave(Product $subject, callable $proceed, ProductInterface $product): ProductEntity
    {
        $todayDayTime = strtotime(date('Y-m-d'));

        $newFrom = $product->getData('news_from_date');/** @phpstan-ignore-line */
        $newTo = $product->getData('news_to_date');/** @phpstan-ignore-line */
        if ($product->getId() && $product->getStoreId() && empty($newFrom)) {/** @phpstan-ignore-line */
            $newFrom = $this->getDefaultRawAttributeValue((int)$product->getId(), 'news_from_date');
            $newTo = $this->getDefaultRawAttributeValue((int)$product->getId(), 'news_to_date');
        }

        /** @phpstan-ignore-next-line */
        if (empty($newFrom)) {
            return $proceed($product);
        }

        /** @phpstan-ignore-next-line */
        if (strtotime($product->getData('news_from_date')) > $todayDayTime) {
            $product->setCustomAttribute(AddDefaultQtyAttribute::ATTRIBUTE_CODE, 1);
            $result = $proceed($product);
            $this->service->adjustQty((int)$product->getId());

            return $result;
        }
        $result = $proceed($product);
        /** @phpstan-ignore-next-line */
        if (strtotime($newFrom) <= $todayDayTime && ($newTo && $newTo > $todayDayTime)) {
            $this->service->restoreQty((int)$product->getId());
        }

        return $result;
    }

    /**
     * @throws NoSuchEntityException
     * @return bool|string|mixed[]|null
     */
    protected function getDefaultRawAttributeValue(int $productId, string $attributeCode)
    {
        /** @phpstan-ignore-next-line */
        return $this->productResource->getAttributeRawValue(
            $productId,
            $attributeCode,
            Store::DEFAULT_STORE_ID
        );
    }
}
