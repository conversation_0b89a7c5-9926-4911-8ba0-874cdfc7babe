<div class="pagebuilder-content-type pagebuilder-banner type-nested"
     attr="data.main.attributes"
     ko-style="data.main.style"
     css="data.main.css"
     event="{ mouseover: onMouseOver, mouseout: onMouseOut }, mouseoverBubble: false"
>
    <render args="getOptions().template"></render>
    <div class="pagebuilder-banner-wrapper">
        <div class="pagebuilder-overlay pagebuilder-key-visual-left-overlay"
            attr="data.overlay.attributes"
            ko-style="data.overlay.style"
            css="data.overlay.css"
            event="mousedown: activateEditor"
        >
            <div class="pagebuilder-key-visual-left-content">
                <div class="pagebuilder-banner-text-content">
                    <div if="isWysiwygSupported()"
                        class="inline-wysiwyg"
                        ko-style="data.content.style"
                        css="data.content.css"
                        attr="data.content.attributes"
                        afterRender="initWysiwyg"
                        event="mousedown: stopEvent"
                    >
                        <div html="data.content.html"></div>
                    </div>
                    <div if="isWysiwygSupported()"
                        class="placeholder-text"
                        ifnot="data.content.html"
                        translate="'Edit Banner Text'"
                    ></div>
                    <textarea ifnot="isWysiwygSupported()"
                        class="inline-wysiwyg-textarea"
                        afterRender="initTextarea"
                        event="{keyup: onTextareaKeyUp, focus: onTextareaFocus, blur: onTextareaBlur, mousedown: stopEvent}"
                        attr="placeholder: $t('Edit Banner Text')"
                    ></textarea>
                </div>
                <a if="data.main.attributes()['data-show-button'] !== 'never'"
                    type="button"
                    class="pagebuilder-banner-button"
                    attr="data.button.attributes"
                    ko-style="data.button.style"
                    css="data.button.css"
                >
                    <span data-bind="liveEdit: { field: 'button_text', placeholder: buttonPlaceholder, selectAll: true }"></span>
                </a>
                <a if="data.main.attributes()['data-show-secondary-button'] !== 'never'"
                    type="button"
                    class="pagebuilder-banner-secondary-button"
                    attr="data.secondary_button.attributes"
                    ko-style="data.secondary_button.style"
                    css="data.secondary_button.css"
                >
                    <span data-bind="liveEdit: { field: 'secondary_button_text', placeholder: buttonPlaceholder, selectAll: true }"></span>
                </a>
            </div>
        </div>
        <div class="pagebuilder-banner-image"
            attr="data.wrapper.attributes"
            ko-style="data.wrapper.style"
            css="data.wrapper.css"
            event="{mouseover: onMouseOverWrapper, mouseout: onMouseOutWrapper}"
        >
            <scope args="getUploader().getUiComponent()">
                <render></render>
            </scope>
        </div>
    </div>
</div>
