<div attr="data.main.attributes"
     ko-style="data.main.style"
     css="data.main.css"
>
    <div if="data.content.html
        || (data.link.attributes().href && data.main.attributes()['data-show-button'] !== 'never')
        || (data.secondary_link.attributes().href && data.main.attributes()['data-show-secondary-button'] !== 'never')"
        class="pagebuilder-overlay pagebuilder-key-visual-left-overlay"
    >
        <div class="container">
            <div class="keyvisual__content-wrapper">
                <div if="data.content.html"
                    attr="data.content.attributes"
                    ko-style="data.content.style"
                    css="data.content.css"
                    html="data.content.html"
                    class="keyvisual__content"
                ></div>
                <div class="keyvisual__btn-row">
                    <a if="data.link.attributes().href && data.main.attributes()['data-show-button'] !== 'never'"
                        attr="data.link.attributes"
                        ko-style="data.link.style"
                        css="data.link.css"
                        class="btn btn-primary"
                    >
                        <span class="pagebuilder-banner-button"
                            attr="data.button.attributes"
                            ko-style="data.button.style"
                            css="data.button.css"
                            html="data.button.html"
                        ></span>
                    </a>
                    <a if="data.secondary_link.attributes().href && data.main.attributes()['data-show-secondary-button'] !== 'never'"
                        attr="data.secondary_link.attributes"
                        ko-style="data.secondary_link.style"
                        css="data.secondary_link.css"
                        class="btn btn-secondary"
                    >
                        <span class="pagebuilder-banner-secondary-button"
                            attr="data.secondary_button.attributes"
                            ko-style="data.secondary_button.style"
                            css="data.secondary_button.css"
                            html="data.secondary_button.html">
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="keyvisual__image">
        <div attr="data.wrapper.attributes"
            ko-style="data.wrapper.style"
            css="data.wrapper.css"
            class="pagebuilder-banner-image"
        >
            <div if="data.video_overlay.attributes()['data-video-overlay-color']"
                class="video-overlay"
                attr="data.video_overlay.attributes"
                ko-style="data.video_overlay.style"
            ></div>
        </div>
    </div>
</div>
