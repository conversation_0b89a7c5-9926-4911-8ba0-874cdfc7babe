<?php

declare(strict_types=1);

namespace Biemans\CustomerNameOptional\Setup\Patch\Data;

use <PERSON>gento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};
use <PERSON>gento\Customer\Api\{
    CustomerMetadataInterface,
    AddressMetadataInterface
};

class UpdateFirstnameAttribute implements DataPatchInterface
{
    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        // Make customer last name optional
        $eavSetup->updateAttribute(
            CustomerMetadataInterface::ATTRIBUTE_SET_ID_CUSTOMER, 
            'firstname',
            'is_required', 
            0
        );

        // Make address last name optional
        $eavSetup->updateAttribute(
            AddressMetadataInterface::ATTRIBUTE_SET_ID_ADDRESS, 
            'firstname',
            'is_required', 
            0
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
