<?php

declare(strict_types=1);

namespace Biemans\CustomerTrackAndTraceEmail\Plugin\Management;

use Magento\Checkout\Api\PaymentInformationManagementInterface;
use Magento\Quote\Api\Data\PaymentInterface;
use Magento\Quote\Api\Data\AddressInterface;

class Payment
{
    /**
     * Assign Track and Trace Email from extension attribute to billing address
     *
     * @param string|int $cartId
     * @param AddressInterface|null $billingAddress
     * @return array<mixed>
     */
    public function beforeSavePaymentInformation(
        PaymentInformationManagementInterface $subject,
        $cartId,
        PaymentInterface $paymentMethod,
        AddressInterface $billingAddress = null
    ): array {
        if (!$billingAddress) {
            return [$cartId, $paymentMethod, $billingAddress];
        }

        $attributes = $billingAddress->getExtensionAttributes();

        if (empty($attributes) || !$attributes->getTrackAndTraceEmail()) { /** @phpstan-ignore-line */
            return [$cartId, $paymentMethod, $billingAddress];
        }

        /** @var \Magento\Framework\DataObject $billingAddress */
        $billingAddress->setTrackAndTraceEmail($attributes->getTrackAndTraceEmail()); /** @phpstan-ignore-line */

        return [$cartId, $paymentMethod, $billingAddress];
    }
}
