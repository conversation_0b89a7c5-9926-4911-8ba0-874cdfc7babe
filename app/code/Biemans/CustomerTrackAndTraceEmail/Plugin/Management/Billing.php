<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerTrackAndTraceEmail\Plugin\Management;

use Magento\Quote\Api\Data\AddressInterface;
use Magento\Quote\Api\BillingAddressManagementInterface;

class Billing
{
    /**
     * Assign Track and Trace Email from extension attribute to billing address
     *
     * @param string|int $cartId
     * @param bool $useForShipping
     * @return array<mixed>
     */
    public function beforeAssign(
        BillingAddressManagementInterface $subject,
        $cartId,
        AddressInterface $address,
        $useForShipping = false
    ): array {
        $attributes = $address->getExtensionAttributes();

        if (empty($attributes) || !$attributes->getTrackAndTraceEmail()) { /** @phpstan-ignore-line */
            return [$cartId, $address, $useForShipping];
        }

        $address->setTrackAndTraceEmail($attributes->getTrackAndTraceEmail()); /** @phpstan-ignore-line */

        return [$cartId, $address, $useForShipping];
    }
}
