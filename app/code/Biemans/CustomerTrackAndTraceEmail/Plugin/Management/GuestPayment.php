<?php

declare(strict_types=1);

namespace Biemans\CustomerTrackAndTraceEmail\Plugin\Management;

use Magento\Checkout\Api\GuestPaymentInformationManagementInterface;
use Magento\Quote\Api\Data\PaymentInterface;
use Magento\Quote\Api\Data\AddressInterface;

class GuestPayment
{
    /**
     * Assign Track and Trace Email from extension attribute to guest billing address
     *
     * @param string|int $cartId
     * @param string $email
     * @param AddressInterface|null $billingAddress
     * @return array<mixed>
     */
    public function beforeSavePaymentInformation(
        GuestPaymentInformationManagementInterface $subject,
        $cartId,
        $email,
        PaymentInterface $paymentMethod,
        AddressInterface $billingAddress = null
    ): array {
        if (!$billingAddress) {
            return [$cartId, $email, $paymentMethod, $billingAddress];
        }

        $attributes = $billingAddress->getExtensionAttributes();

        if (empty($attributes) || !$attributes->getTrackAndTraceEmail()) { /** @phpstan-ignore-line */
            return [$cartId, $email, $paymentMethod, $billingAddress];
        }

        /** @var \Magento\Framework\DataObject $billingAddress */
        $billingAddress->setTrackAndTraceEmail($attributes->getTrackAndTraceEmail()); /** @phpstan-ignore-line */

        return [$cartId, $email, $paymentMethod, $billingAddress];
    }
}
