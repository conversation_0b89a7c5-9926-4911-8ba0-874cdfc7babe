<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerTrackAndTraceEmail\Plugin\Management;

use Magento\Quote\Api\Data\AddressInterface;
use Magento\Quote\Model\ShippingAddressManagementInterface;

class Shipping
{
    /**
     * Assign Track and Trace Email from extension attribute to shipping address
     *
     * @param string|int $cartId
     * @return array<mixed>
     */
    public function beforeAssign(
        ShippingAddressManagementInterface $subject,
        $cartId,
        AddressInterface $address
    ): array {
        $attributes = $address->getExtensionAttributes();

        if (empty($attributes) || !$attributes->getTrackAndTraceEmail()) { /** @phpstan-ignore-line */
            return [$cartId, $address];
        }

        $address->setTrackAndTraceEmail($attributes->getTrackAndTraceEmail()); /** @phpstan-ignore-line */

        return [$cartId, $address];
    }
}
