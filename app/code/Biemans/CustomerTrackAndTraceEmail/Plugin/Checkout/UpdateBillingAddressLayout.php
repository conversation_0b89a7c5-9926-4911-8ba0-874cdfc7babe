<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerTrackAndTraceEmail\Plugin\Checkout;

use Magento\Checkout\Block\Checkout\LayoutProcessorInterface;

class UpdateBillingAddressLayout
{
    /**
     * Add to the attention of field.
     *
     * @param array<mixed> $jsLayout
     * @return array<mixed>
     */
    public function afterProcess(
        LayoutProcessorInterface $subject,
        array $jsLayout
    ) {
        if (isset($jsLayout['components']['checkout'])) {
            $checkoutSteps = $jsLayout['components']['checkout']['children']['steps']['children'];

            foreach ($checkoutSteps as $stepCode => $stepArray) {
                if ($stepCode == 'billing-step') {
                    if (!isset($jsLayout
                        ['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']
                        ['children']['payments-list'])) {
                        return $jsLayout;
                    }

                    $paymentForms = &$jsLayout
                    ['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']
                    ['children']['payments-list']['children'];

                    foreach ($paymentForms as $paymentGroup => &$groupConfig) {
                        if (
                            !isset($groupConfig['component'])
                            || 'Magento_Checkout/js/view/billing-address' !== $groupConfig['component']
                        ) {
                            continue;
                        }

                        $this->addTrackAndTraceEmail($groupConfig);
                    }

                } elseif ($stepCode == 'shipping-step') {
                    if (!isset($jsLayout
                        ['components']['checkout']['children']['steps']['children']['shipping-step']['children']
                        ['shippingAddress']['children']['shipping-address-fieldset'])) {
                        return $jsLayout;
                    }

                    $groupConfig = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']
                    ['children']['shippingAddress']['children']['shipping-address-fieldset'];

                    $this->addTrackAndTraceEmailToShipping($groupConfig);
                }
            }
        }

        return $jsLayout;
    }

    /**
     * Add track_and_trace_email field.
     *
     * @param array<mixed> $groupConfig
     */
    private function addTrackAndTraceEmail(array &$groupConfig): UpdateBillingAddressLayout
    {
        $groupConfig['children']['form-fields']['children']['track_and_trace_email'] = [
            'component' => 'Magento_Ui/js/form/element/abstract',
            'config'    => [
                'customScope' => "{$groupConfig['dataScopePrefix']}.custom_attributes",
                'customEntry' => null,
                'template'    => 'ui/form/field',
                'elementTmpl' => 'ui/form/element/input',
                'placeholder' => __('Track and trace email')
            ],
            'provider'  => 'checkoutProvider',
            'dataScope' => "{$groupConfig['dataScopePrefix']}.custom_attributes.track_and_trace_email",
            'label'     => __('Track and trace email'),
            'sortOrder' => 900,
        ];

        return $this;
    }

    /**
     * Add track_and_trace_email field.
     *
     * @param array<mixed> $groupConfig
     */
    private function addTrackAndTraceEmailToShipping(array &$groupConfig): UpdateBillingAddressLayout
    {
        $groupConfig['children']['track_and_trace_email'] = [
            'component' => 'Magento_Ui/js/form/element/abstract',
            'config'    => [
                'customScope' => "shippingAddress.custom_attributes",
                'customEntry' => null,
                'template'    => 'ui/form/field',
                'elementTmpl' => 'ui/form/element/input',
                'placeholder' => __('Track and trace email')
            ],
            'provider'  => 'checkoutProvider',
            'dataScope' => 'shippingAddress.custom_attributes.track_and_trace_email',
            'label'     => __('Track and trace email'),
            'visible' => true,
            'sortOrder' => 900
        ];

        return $this;
    }
}
