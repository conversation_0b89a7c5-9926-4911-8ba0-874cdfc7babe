<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerTrackAndTraceEmail\Plugin\Checkout;

use Magento\Quote\Model\Quote\{
    Address,
    Address\ToOrderAddress
};
use Magento\Sales\Api\Data\OrderAddressInterface;

class ConvertToOrderAddress
{
    /**
     * Add Track and Trace Email to order address from quote address
     *
     * @param array<mixed> $data
     */
    public function afterConvert(
        ToOrderAddress $subject,
        OrderAddressInterface $orderAddress,
        Address $object,
        $data = []
    ): OrderAddressInterface {
        // Take values from quote address and add to order address
        /** @phpstan-ignore-next-line */
        $orderAddress->setTrackAndTraceEmail($object->getTrackAndTraceEmail());

        return $orderAddress;
    }
}
