<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <shipping>
            <biemans_types>
                <group_a_title>Ik regel zelf vervoer.</group_a_title>
                <group_a_methods>pickup_carrier_pickup_carrier,pickup_pickup</group_a_methods>
                <group_b_title>Biemans regelt de verzending.</group_b_title>
            </biemans_types>
        </shipping>
    </default>
</config>
