<?php

declare(strict_types=1);

namespace Biemans\ShipmentMethods\ViewModel;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;

class ShippingTypes implements ArgumentInterface
{
    const BASE_PATH = 'shipping/biemans_types/';
    const PATH_GROUP_A_TITLE = self::BASE_PATH . 'group_a_title';
    const PATH_GROUP_B_TITLE = self::BASE_PATH . 'group_b_title';
    const PATH_GROUP_A_METHODS = self::BASE_PATH . 'group_a_methods';

    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    public function getGroupATitle(): string
    {
        return (string)$this->getValue(self::PATH_GROUP_A_TITLE);
    }

    public function getGroupBTitle(): string
    {
        return (string)$this->getValue(self::PATH_GROUP_B_TITLE);
    }

    /**
     * @return string[]
     */
    public function getGroupAMethods(): array
    {
        $value = $this->getValue(self::PATH_GROUP_A_METHODS);

        try {
            $methods = explode(',', $value);
        } catch (\Throwable $e) {
            $methods = [];
        }

        return $methods;
    }

    /**
     * @return string[]
     */
    public function getMethodsTypes(): array
    {
        return [
            'group_a' => $this->getGroupATitle(),
            'group_b' => $this->getGroupBTitle()
        ];
    }

    /**
     * @return mixed
     */
    protected function getValue(
        string $path,
        string $scopeCode = null,
        string $scopeType = ScopeInterface::SCOPE_STORES
    ) {
        return $this->scopeConfig->getValue($path, $scopeType, $scopeCode);
    }
}
