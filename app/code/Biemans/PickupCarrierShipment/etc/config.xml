<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <carriers>
            <pickup_carrier>
                <active>1</active>
                <model>Biemans\PickupCarrierShipment\Model\Carrier\PickupCarrier</model>
                <title>Pickup</title>
                <name>Ik stuur mijn eigen vervoerder.</name>
                <price>0.00</price>
                <sallowspecific>0</sallowspecific>
                <specificerrmsg>This shipping method is not available. To use this shipping method, please contact us.</specificerrmsg>
                <handling_type>F</handling_type>
                <sort_order>50</sort_order>
            </pickup_carrier>
        </carriers>
    </default>
</config>
