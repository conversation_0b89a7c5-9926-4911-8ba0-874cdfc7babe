<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PickupShipment\Model;

use <PERSON><PERSON><PERSON>\PickupShipment\Api\ClosingDaysInterface;
use <PERSON><PERSON>mans\PickupShipment\ViewModel\Pickup;

class ClosingDays<PERSON>pi implements ClosingDaysInterface
{
    public function __construct(
        private readonly Pickup $pickupViewModel
    ) {}

    /**
     * @return string[]
     */
    public function getDeliveryHolidays(): array
    {
        return json_decode($this->pickupViewModel->getDeliveryHolidays());
    }
}
