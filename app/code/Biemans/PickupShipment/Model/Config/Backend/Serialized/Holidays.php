<?php

declare(strict_types=1);

namespace Biemans\PickupShipment\Model\Config\Backend\Serialized;

use Magento\Config\Model\Config\Backend\Serialized;

class Holidays extends Serialized
{
    public function beforeSave()
    {
        $value = $this->getValue();

        if (is_array($value)) {
            unset($value['__empty']);
            uasort($value, function($a, $b) {
                $a = \DateTime::createFromFormat('d-m-Y', $a['date']);
                $b = \DateTime::createFromFormat('d-m-Y', $b['date']);

                return $a <=> $b;
            });
        }

        $value = array_map(function ($date) {
            try {
                /** @phpstan-ignore-next-line  */
                $date['date'] = \DateTime::createFromFormat('d-m-Y', $date['date'])->format('Y-m-d');
            } catch (\Throwable $e) {
                $date['date'] = null;
            }
            return $date;
        }, $value); /** @phpstan-ignore-line  */

        $this->setValue($value);

        return parent::beforeSave();
    }
}
