<?php

declare(strict_types=1);

namespace Biemans\PickupShipment\Block\Adminhtml\Form\Field;

use Magento\Backend\Block\Template;
use Magento\Config\Block\System\Config\Form\Field\FieldArray\AbstractFieldArray;
use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Framework\DataObject;

class Holidays extends AbstractFieldArray
{
    protected function _prepareToRender(): void
    {
        $this->addColumn(
            'date',
            [
                'label' => __('Date'),
                'class' => 'js-excluded-weekdays-datepicker required'
            ]
        );

        $this->_addAfter = false;
        $this->_addButtonLabel = (string) __('Add Date');

        parent::_prepareToRender();
    }

    protected function _prepareArrayRow(DataObject $row): void
    {
        $key = 'date';
        $rowId = $row['_id'];

        try {
            /** @var \DateTime $sourceDate */
            $sourceDate = \DateTime::createFromFormat('Y-m-d', $row[$key]);
            $renderedDate = $sourceDate->format('d-m-Y');
            $row[$key] = $renderedDate;
            $columnValues = $row['column_values'];
            $columnValues[$this->_getCellInputElementId($rowId, $key)] = $renderedDate;
            $row['column_values'] = $columnValues;
        } catch (\Throwable $e) {
            // Just skipping error values
        }
    }

    protected function _getElementHtml(AbstractElement $element): string
    {
        $html = parent::_getElementHtml($element);
        /** @phpstan-ignore-next-line  */
        $html .= $this->getLayout()
            ->createBlock(Template::class)
            ->setTemplate('Biemans_PickupShipment::config/datepicker-js.phtml')
            ->toHtml()
        ;

        return $html;
    }
}
