<?php

declare(strict_types=1);

namespace <PERSON>iemans\PickupShipment\Observer;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;

class CopyQuoteDataToOrder implements ObserverInterface
{
    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    public function execute(EventObserver $observer)
    {
        try {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $observer->getOrder();
            /** @var \Magento\Quote\Model\Quote $quote */
            $quote = $observer->getQuote();

            if (
                !empty($pickup = $quote->getData('shipping_pickup'))
                && ($quote->getShippingAddress()->getShippingMethod() === 'pickup_pickup')
            ) {
                $order->setData('shipping_pickup', $pickup);
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_PickupShipment: ' . $e->getMessage());
        }
    }
}
