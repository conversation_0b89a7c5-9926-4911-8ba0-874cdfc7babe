<?php

declare(strict_types=1);

namespace <PERSON>iemans\PickupShipment\Plugin;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderExtensionInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\Collection;

class OrderRepository
{
    private OrderExtensionFactory $extensionFactory;

    public function __construct(
        OrderExtensionFactory $extensionFactory
    ) {
        $this->extensionFactory = $extensionFactory;
    }

    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $resultOrder
    ): OrderInterface {
        $orderExtensions = $resultOrder->getExtensionAttributes() ? $resultOrder->getExtensionAttributes() :
            $this->extensionFactory->create();
        $orderExtensions->setShippingPickup($resultOrder->getData('shipping_pickup')); /** @phpstan-ignore-line */
        $resultOrder->setExtensionAttributes($orderExtensions);

        return $resultOrder;
    }

    /**
     * @return Collection<OrderInterface>
     */
    public function afterGetList(
        OrderRepositoryInterface $subject,
        Collection $resultOrder
    ) {
        /** @var OrderInterface $order */
        foreach ($resultOrder->getItems() as $order) {
            $this->afterGet($subject, $order);
        }

        return $resultOrder;
    }
}
