<?php

declare(strict_types=1);

namespace <PERSON>iemans\PickupShipment\Plugin;

use <PERSON>gento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartExtensionFactory;
use Magento\Quote\Api\Data\CartExtensionInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\Data\CartSearchResultsInterface;
use Psr\Log\LoggerInterface;

class CartRepository
{
    private CartExtensionFactory $cartExtensionFactory;
    private LoggerInterface $logger;

    public function __construct(
        CartExtensionFactory $cartExtensionFactory,
        LoggerInterface $logger
    ) {
        $this->cartExtensionFactory = $cartExtensionFactory;
        $this->logger = $logger;
    }

    public function afterGet(
        CartRepositoryInterface $subject,
        CartInterface $quote
    ): CartInterface {
        $this->setShippingPickup($quote);

        return $quote;
    }

    public function afterGetList(
        CartRepositoryInterface $subject,
        CartSearchResultsInterface $quoteSearchResult
    ): CartSearchResultsInterface {
        foreach ($quoteSearchResult->getItems() as $quote) {
            $this->setShippingPickup($quote);
        }

        return $quoteSearchResult;
    }

    public function setShippingPickup(CartInterface $quote): void
    {
        try {
            $extensionAttributes = $quote->getExtensionAttributes();

            /** @var CartExtensionInterface $target */
            $target = $extensionAttributes ?? $this->cartExtensionFactory->create();
            $target->setShippingPickup($quote->getShippingPickup()); /** @phpstan-ignore-line */

            $quote->setExtensionAttributes($target);
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_PickupShipment: ' . $e->getMessage());
        }
    }

    /**
     * @return array<mixed>
     */
    public function beforeSave(CartRepositoryInterface $subject, CartInterface $quote): array
    {
        try {
            $pickup = $quote->getExtensionAttributes()->getShippingPickup(); /** @phpstan-ignore-line */
            if (is_string($pickup)) {
                $quote->setShippingPickup($pickup); /** @phpstan-ignore-line */
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_PickupShipment: ' . $e->getMessage());
        }

        return [$quote];
    }
}
