<script type="text/javascript">
    // Bind click to "Add" buttons and bind datepicker to added date fields
    require([
        'jquery',
        'jquery-ui-modules/datepicker'
    ], function ($) {
        $(function() {
            function bindDatePicker() {
                setTimeout(function() {
                    $('.js-excluded-weekdays-datepicker').datepicker({
                        dateFormat: 'dd-mm-yy'
                    });
                }, 50);
            }

            bindDatePicker();

            $('button.action-add').on('click', function(e) {
                bindDatePicker();
            });
        });
    });
</script>
