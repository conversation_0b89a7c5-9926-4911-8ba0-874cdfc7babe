<?php

declare(strict_types=1);

namespace <PERSON>iemans\PickupShipment\Magewire\Checkout;

use Hyva\Checkout\Model\Magewire\Component\EvaluationInterface;
use Hyva\Checkout\Model\Magewire\Component\EvaluationResultFactory;
use Hyva\Checkout\Model\Magewire\Component\EvaluationResultInterface;
use Magento\Checkout\Model\Session as SessionCheckout;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magewirephp\Magewire\Component;

class Pickup extends Component implements EvaluationInterface
{
    public ?string $value = null;

    public function __construct(
        private readonly SessionCheckout $sessionCheckout,
        private readonly CartRepositoryInterface $quoteRepository,
        private readonly \Biemans\PickupShipment\ViewModel\Pickup $pickupShipmentViewModel
    ) {}

    /**
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function mount(): void
    {
        $quote = $this->sessionCheckout->getQuote();
        $value = $quote->getExtensionAttributes()->getShippingPickup(); /** @phpstan-ignore-line */

        if ($value && !$this->validateSameDayPickup($value)) {
            $value = null;
            $this->updatingValue('');
        }

        $this->value = $value;
    }

    public function updatingValue(string $value): string
    {
        try {
            $quote = $this->sessionCheckout->getQuote();
            $quote->getExtensionAttributes()->setShippingPickup($value); /** @phpstan-ignore-line */
            $this->quoteRepository->save($quote);
        } catch (LocalizedException|\Exception $exception) {
            $this->dispatchErrorMessage('Something went wrong while saving shipment pickup. Please try again.');
        }

        return $value;
    }

    public function evaluateCompletion(EvaluationResultFactory $resultFactory): EvaluationResultInterface
    {
        $value = trim($this->value ?? '');
        if (count(explode(' ', $value)) < 2) {
            return /** @phpstan-ignore-line */ $resultFactory->createErrorMessageEvent()
                ->withCustomEvent('shipping:method:error')
                ->withMessage(__('Pickup date and time is required.')->render());
        }

        if (!$this->validateSameDayPickup($value) || !$this->validateNextDayPickup($value)) {
            $this->value = null;
            $this->updatingValue('');

            return /** @phpstan-ignore-line */ $resultFactory->createErrorMessageEvent()
                ->withCustomEvent('shipping:method:error')
                ->withMessage(__('Pickup date is not valid. Please select another date and time.')->render());
        }

        return $resultFactory->createSuccess();
    }

    private function validateSameDayPickup(string $value): bool
    {
        if (!$this->pickupShipmentViewModel->canPickupSameDate()) {
            $selectedDate = date('d-m-Y', (int)strtotime($value));
            $currentDate = date('d-m-Y');
            if ($selectedDate === $currentDate) {
                return false;
            }
        }

        return true;
    }

    private function validateNextDayPickup(string $value): bool
    {
        $selectedDate = date('d-m-Y', (int)strtotime($value));
        $tomorrowDate = date('d-m-Y', strtotime('+1 day'));

        if ($selectedDate !== $tomorrowDate) {
            return true;
        }

        return $this->pickupShipmentViewModel->canPickupTomorrowDate((int)date('H', (int)strtotime($value)));
    }
}
