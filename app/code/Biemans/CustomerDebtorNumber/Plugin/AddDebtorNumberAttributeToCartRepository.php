<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerDebtorNumber\Plugin;

use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartExtensionFactory;
use Magento\Quote\Api\Data\CartExtensionInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\Data\CartSearchResultsInterface;
use Psr\Log\LoggerInterface;

class AddDebtorNumberAttributeToCartRepository
{
    private CartExtensionFactory $cartExtensionFactory;
    private LoggerInterface $logger;

    public function __construct(
        CartExtensionFactory $cartExtensionFactory,
        LoggerInterface $logger
    ) {
        $this->cartExtensionFactory = $cartExtensionFactory;
        $this->logger = $logger;
    }

    public function afterGet(
        CartRepositoryInterface $subject,
        CartInterface $quote
    ): CartInterface {
        $this->setDebtorNumber($quote);

        return $quote;
    }

    public function afterGetList(
        CartRepositoryInterface $subject,
        CartSearchResultsInterface $quoteSearchResult
    ): CartSearchResultsInterface {
        foreach ($quoteSearchResult->getItems() as $quote) {
            $this->setDebtorNumber($quote);
        }

        return $quoteSearchResult;
    }

    public function setDebtorNumber(CartInterface $quote): void
    {
        try {
            $extensionAttributes = $quote->getExtensionAttributes();

            /** @var CartExtensionInterface $target */
            $target = $extensionAttributes ?? $this->cartExtensionFactory->create();
            $target->setDebtorNumber($quote->getDebtorNumber()); /** @phpstan-ignore-line */

            $quote->setExtensionAttributes($target);
        } catch (\Exception $exception) {
            $this->logger->critical($exception->getMessage());
        }
    }

    /**
     * @return array<mixed>
     */
    public function beforeSave(CartRepositoryInterface $subject, CartInterface $quote): array
    {
        try {
            if (
                $quote->getCustomer()->getId()
                && ($debtorNumber = $quote->getCustomer()->getCustomAttribute('debtor_number'))
                && $debtorNumber->getValue()
            ) {
                $quote->setDebtorNumber($debtorNumber->getValue()); /** @phpstan-ignore-line */
            }
        } catch (\Exception $exception) {
            $this->logger->critical($exception->getMessage());
        }

        return [$quote];
    }
}
