<?php

declare(strict_types=1);

namespace Biemans\CustomerDebtorNumber\Plugin;

use Magento\Framework\DB\Adapter\AdapterInterface;
use Hyva\YireoNextGenImages\Model\AlpinePictureFactory;

class FullSearchDebtorNumber
{
    /**
     * @param AdapterInterface $subject
     * @param array<mixed> $indexList
     * @param string $tableName
     * @return array<mixed>
     */
    public function afterGetIndexList(
        AdapterInterface $subject,
        array $indexList,
        $tableName
    ): array {
        if ($tableName === 'customer_grid_flat') {
            foreach ($indexList as &$index) {
                if (
                    isset($index['INDEX_TYPE'])
                    && isset($index['COLUMNS_LIST'])
                    && strtoupper($index['INDEX_TYPE']) === 'FULLTEXT'
                ) {
                    $index['COLUMNS_LIST'][] = 'debtor_number';
                }
            }
        }

        return $indexList;
    }
}
