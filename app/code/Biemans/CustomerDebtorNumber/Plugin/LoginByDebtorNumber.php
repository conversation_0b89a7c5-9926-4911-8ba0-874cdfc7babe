<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerDebtorNumber\Plugin;

use <PERSON>iemans\CustomerDebtorNumber\Setup\Patch\Data\AddDebtorNumberCustomerAttribute;
use Magento\Customer\Api\{
    AccountManagementInterface,
    CustomerRepositoryInterface
};
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Api\SearchCriteriaBuilderFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class LoginByDebtorNumber
{
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly SearchCriteriaBuilderFactory $searchCriteriaBuilderFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @see AccountManagementInterface::authenticate()
     *
     * @param string $email
     * @param string $password
     * @return array<string>
     */
    public function beforeAuthenticate(
        AccountManagementInterface $subject,
        $email,
        $password
    ): array {
        try {
            // Suppose $email is a debtor_number, search customer by it and provide email back to authenticate()
            // It should be store specific since we can have same debtor number in different websites
            $searchCriteria = $this->searchCriteriaBuilderFactory->create()
                ->addFilter(AddDebtorNumberCustomerAttribute::ATTRIBUTE_CODE, $email)
                ->addFilter('website_id', $this->storeManager->getWebsite()->getId())
                ->create();

            $customers = $this->customerRepository->getList($searchCriteria);

            if ($customers->getTotalCount() > 0) {
                /** @var CustomerInterface $customer */
                $customer = current($customers->getItems());

                $email = $customer->getEmail();
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_CustomerDebtorNumber: ' . $e->getMessage());
        }

        return [$email, $password];
    }
}
