# Biemans_PdfCatalogPublic module

Module contains logic that allows to use Pdf Catalogs for public pages content.

1. admin can chose groups that will be used for the catalogs
2. this catalogs can be downloaded with next pages
   1. media/catalog/pdf/regular_main.pdf
   2. media/catalog/pdf/outlet_main.pdf
   3. media/catalog/pdf/regular_uk.pdf
   4. media/catalog/pdf/outlet_uk.pdf
   5. media/catalog/pdf/regular_pl.pdf
   6. media/catalog/pdf/outlet_pl.pdf
3. for the purposes of the catalogs used pdf catalog functionality without prices
4. catalogs are regenerated each night
5. you can force catalogs for regeneration using next CLI command:

```
bin/magento biemans:pdfcatalog:public
```
6. for process logging check var/log/pdf_catalog_generation.log

## Installation details

For information about a module installation in Magento 2, see [Enable or disable modules](https://devdocs.magento.com/guides/v2.4/install-gde/install/cli/install-cli-subcommands-enable.html).

## Extensibility

Extension developers can interact with the Biemans_PdfCatalogPublic module. For more information about the Magento extension mechanism, see [Magento plug-ins](https://devdocs.magento.com/guides/v2.4/extension-dev-guide/plugins.html).

[The Magento dependency injection mechanism](https://devdocs.magento.com/guides/v2.4/extension-dev-guide/depend-inj.html) enables you to override the functionality of the Biemans_PdfCatalogPublic module.

### Layouts

The module introduces layout handles in the `view/adminhtml/layout` directory.

For more information about a layout in Magento 2, see the [Layout documentation](https://devdocs.magento.com/guides/v2.4/frontend-dev-guide/layouts/layout-overview.html).

### UI components

You can extend product and category updates using the UI components located in the `view/adminhtml/ui_component` directory.

For information about a UI component in Magento 2, see [Overview of UI components](https://devdocs.magento.com/guides/v2.4/ui_comp_guide/bk-ui_comps.html).

## Additional information

For information about significant changes in patch releases, see [Release information](https://devdocs.magento.com/guides/v2.4/release-notes/bk-release-notes.html).
