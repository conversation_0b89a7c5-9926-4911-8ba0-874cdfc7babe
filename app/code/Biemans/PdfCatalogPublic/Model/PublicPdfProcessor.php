<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalogPublic\Model;

use <PERSON><PERSON><PERSON>\PdfCatalog\Model\FileGenerationProcessor;
use <PERSON><PERSON><PERSON>\PdfCatalog\Model\FileGenerationProcessor\DocumentCreatorInterface;
use <PERSON>gent<PERSON>\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;
use Psr\Log\LoggerInterface;
use Throwable;

/**
 * PublicPDfProcessor - class mediator to process feature logic.
 * 1. based on config we check if pdf files is generated for specific groups overnight
 * 2. if they are -  we copy them to pub/media folder to make public
 * 3. if they are not - we trigger generation and copy them to the folder
 *
 * Files are used in CMS pages (like home page).
 */
class PublicPdfProcessor
{
    private const LOG_PREFIX = '[PUBLIC FILES]: ';
    private const USED_CATALOG_TYPES = [
        'regular',
        'outlet'
    ];
    private const PUB_PATH = 'catalog/pdf/';

    private const CONFIG_MAP = [
        'main' => 'biemans_pdf_catalog/public/main_group',
        'uk' => 'biemans_pdf_catalog/public/uk_group',
        'pl' => 'biemans_pdf_catalog/public/pl_group',
    ];

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly FileGenerationProcessor $processor,
        private readonly Filesystem $filesystem,
        private readonly File $io,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly GroupRepositoryInterface $groupRepository
    ) {
    }

    public function execute(): void
    {
        foreach (self::CONFIG_MAP as $key => $path) {
            $this->logger->debug(self::LOG_PREFIX . sprintf('Starting publishing files for %s', $key));
            $customerGroupId = $this->scopeConfig->getValue($path);
            $code = $this->groupRepository->getById((int)$customerGroupId)->getCode();
            foreach (self::USED_CATALOG_TYPES as $catalog) {
                try {
                    $this->logger->debug(self::LOG_PREFIX . sprintf('Checking if catalog exist %s %s', $key
                            , $catalog));
                    $alreadyGenerated = $this->checkFile($catalog, $code);
                    if (!$alreadyGenerated) {
                        $this->logger->debug(self::LOG_PREFIX . sprintf('File does not exist. Generating %s %s',
                                $key, $catalog));
                        $this->processor->execute([$code], [$catalog]);
                    }
                    $this->logger->debug(self::LOG_PREFIX . sprintf('Removing old file %s %s', $key,
                            $catalog));
                    $this->removeOldFile($catalog, $key);
                    $this->logger->debug(self::LOG_PREFIX . sprintf('Cleaning file %s %s', $key,
                            $catalog));
                    $this->copyFileToPublic($catalog, $key, $code);
                } catch (Throwable $exception) {
                    $this->logger->debug(self::LOG_PREFIX . $exception->getMessage());
                }
            }
        }
    }

    private function checkFile(string $catalog, string $code): bool
    {
        $privateFilename = $this->processor->buildFileName($code, $catalog, false) . '.pdf';
        /* @phpstan-ignore-next-line */
        $files = array_diff(scandir($this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)
                ->getAbsolutePath() . DocumentCreatorInterface::FOLDER_FOR_PDF), ['.', '..']);
        if (!empty($files) && in_array($privateFilename, $files)) {

            return true;
        }

        return false;
    }

    private function removeOldFile(string $catalog, string $key): void
    {
        $this->io->rm($this->filesystem->getDirectoryRead(DirectoryList::MEDIA)
                ->getAbsolutePath() . self::PUB_PATH . $this->getPublicName($catalog, $key));
    }

    private function copyFileToPublic(string $catalog, string $key, string $code): void
    {
        if (!$this->filesystem->getDirectoryWrite(DirectoryList::MEDIA)
            ->isDirectory($this->filesystem->getDirectoryWrite(DirectoryList::MEDIA)
                    ->getAbsolutePath() . '/' . self::PUB_PATH)
        ) {
            $this->io->mkdir(
                $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA)->getAbsolutePath()
                . self::PUB_PATH, 0775);
        }

        $this->io->cp($this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)
                ->getAbsolutePath() . DocumentCreatorInterface::FOLDER_FOR_PDF
            . '/' . $this->processor->buildFileName($code, $catalog, false) . '.pdf',
            $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)
                ->getAbsolutePath() . self::PUB_PATH . $this->getPublicName($catalog, $key));
    }

    private function getPublicName(string $catalog, string $country): string
    {
        return $catalog . '_' . $country . '.pdf';
    }
}
