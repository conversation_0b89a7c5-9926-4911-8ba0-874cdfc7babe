<?php

declare(strict_types=1);

namespace <PERSON>iemans\ProductDataExcel\Service;

use Exception;
use Magento\Eav\Api\AttributeOptionManagementInterface;

/**
 * Class OptionValue - return value for option based on customers attribute code and option Id
 */
class OptionValue
{
    public function __construct(
        private readonly AttributeOptionManagementInterface $attributeOptionManagement
    ) {
    }

    public function getOptionValueById(string $attributeCode, int $optionId): ?string
    {
        try {
            /** @phpstan-ignore-next-line */
            $options = $this->attributeOptionManagement->getItems('customer', $attributeCode);

            foreach ($options as $option) {
                if ($option->getValue() == $optionId) {
                    return (string)$option->getLabel();
                }
            }
        } catch (Exception $e) {
            //do nothing
        }

        return null;
    }
}
