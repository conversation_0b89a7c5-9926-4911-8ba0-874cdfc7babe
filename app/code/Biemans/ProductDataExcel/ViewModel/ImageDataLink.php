<?php

declare(strict_types=1);

namespace Biemans\ProductDataExcel\ViewModel;

use Exception;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class ImageDataLink implements ArgumentInterface
{
    private const DOWNLOAD_URL_PATTERN = 'excel/file/images';
    private const WETRANSFER_URL_PATTERN = 'product_data_excel/we_transfer_settings/we_transfer_link';
    private const FALLBACK_WETRANSFER_URL = 'https://we.tl/t-WuyrR78AJ8';

    public function __construct(
        private readonly UrlInterface          $urlBuilder,
        private readonly LoggerInterface       $logger,
        private readonly ScopeConfigInterface  $scopeConfig,
        private readonly StoreManagerInterface $storeManager
    )
    {
    }

    /**
     * Method returns links to the Excel product data upload.
     *
     * @return string
     */
    public function getFileLink(): string
    {
        $result = '';
        try {
            $result = $this->urlBuilder->getUrl(self::DOWNLOAD_URL_PATTERN);
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage());
        }

        return $result;
    }

    public function getWeTransferLink(): string
    {
        $storeId = $this->storeManager->getStore()->getId();

        $weTransferUrl = $this->scopeConfig->getValue(
            self::WETRANSFER_URL_PATTERN,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );

        return $weTransferUrl ?: self::FALLBACK_WETRANSFER_URL;
    }
}
