<?php

namespace <PERSON><PERSON>mans\ProductDataExcel\Model;

use Biemans\ProductDataExcel\Service\ProductDataProvider;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

/**
 * Class ExcelFileGenerator - creates excel using raw queries for speed.
 */
class ExcelFileGenerator
{
    public const TMP_FOLDER_FILES = 'biemans_excel_tpm';
    private const HEADERS = [
        'article code',
        'grouped article',
        'image-link'
    ];

    public function __construct(
        private readonly Filesystem $filesystem,
        private readonly File $io,
        private readonly ProductDataProvider $dataProvider
    ) {
    }

    private const EXCEL_TITLE = 'Products Biemans';

    /**
     * create Excel file and save it in temp folder
     * @param string $customerGroupId
     * @param bool $isBSpecial
     * @return Spreadsheet
     * @throws Exception
     */
    public function generateByCustomerGroup(string $customerGroupId, bool $isBSpecial): Spreadsheet {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setTitle(self::EXCEL_TITLE);
        $dataArray = $this->dataProvider->getProductData($customerGroupId, $isBSpecial);
        $sheet->fromArray(array_merge([self::HEADERS], $dataArray));
        //make header bold
        $sheet->getStyle('A1:Z1')->applyFromArray(['font' => ['bold' => true]]);

        return $spreadsheet;
    }

    /**
     * check if folder for tmp files exist, cleanup it if it exists
     * @param string $filePath
     * @return void
     * @throws FileSystemException
     */
    public function prepareFolder(string $filePath): void {
        //if not exist - create
        if (!$this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)
            ->isDirectory(DirectoryList::VAR_DIR . '/' . self::TMP_FOLDER_FILES)
        ) {
            $this->io->mkdir(
                $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
                . '/' . self::TMP_FOLDER_FILES, 0775);
        }
        $this->io->rmdir($this->filesystem
                ->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . self::TMP_FOLDER_FILES . '/' . $filePath, true);
    }
}
