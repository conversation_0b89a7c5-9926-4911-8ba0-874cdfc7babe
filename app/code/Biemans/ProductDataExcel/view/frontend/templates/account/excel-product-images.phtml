<?php

declare(strict_types=1);

use <PERSON>iemans\ProductDataExcel\ViewModel\ImageDataLink;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var ImageDataLink $filesProvider */
$filesProvider = $viewModels->require(ImageDataLink::class);

$excelProductLink = $filesProvider->getFileLink();
$weTransferLink = $filesProvider->getWeTransferLink();

if (empty($excelProductLink)) {
    return '';
}
?>
<div class="flex mb-4 px-5 py-6 gap-4 bg-secondary-200 flex-wrap items-center">
    <span class="mr-auto">
        <?= $escaper->escapeHtml(__('Download latest Product Image Data')) ?>
    </span>

    <div class="flex flex-wrap gap-4">
        <a target="_blank"
           href="<?= $weTransferLink ?>"
           class="btn btn-ghost btn-with-icon border-current"
        >
            <?= __('Download Images') ?>
            <?= $heroicons->downloadHtml('w-4 h-4 !transition-none icon') ?>
        </a>
    </div>

    <div class="flex flex-wrap gap-4">
        <a target="_blank"
           href="<?= $excelProductLink ?>"
           class="btn btn-ghost btn-with-icon border-current"
        >
            <?= __('Download Excel') ?>
            <?= $heroicons->downloadHtml('w-4 h-4 !transition-none icon') ?>
        </a>
    </div>
</div>