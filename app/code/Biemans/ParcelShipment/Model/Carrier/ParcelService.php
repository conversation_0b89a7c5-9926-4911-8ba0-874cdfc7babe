<?php

declare(strict_types=1);

namespace Biemans\ParcelShipment\Model\Carrier;

use Biemans\ShipmentMethods\Model\Carrier\ShippingTrait;
use Magento\Shipping\Model\Carrier\AbstractCarrier;
use Magento\Shipping\Model\Carrier\CarrierInterface;

class ParcelService extends AbstractCarrier implements CarrierInterface
{
    use ShippingTrait;

    /** @var string */
    protected $_code = 'parcel_service';

    /** @var bool */
    protected $_isFixed = true;
}
