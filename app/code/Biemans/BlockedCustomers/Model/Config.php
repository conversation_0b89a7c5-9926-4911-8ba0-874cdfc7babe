<?php

declare(strict_types=1);

namespace Bie<PERSON>\BlockedCustomers\Model;

use <PERSON>iemans\BlockedCustomers\Setup\Patch\Data\AddBlockedCustomerAttribute;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class Config
{
    const XML_PATH_BASE = 'biemans_blocked_customers/general/';
    const XML_PATH_ENABLED = self::XML_PATH_BASE . 'enable';
    const XML_PATH_WARNING_MESSAGE = self::XML_PATH_BASE . 'warning_message';
    const XML_PATH_EMAILS = self::XML_PATH_BASE . 'send_to';

    private ScopeConfigInterface $scopeConfig;
    private FilterProvider $filterProvider;
    private StoreManagerInterface $storeManager;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        FilterProvider $filterProvider,
        StoreManagerInterface $storeManager
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->filterProvider = $filterProvider;
        $this->storeManager = $storeManager;
    }

    public function canBlock(): bool
    {
        return (bool)$this->getConfig(self::XML_PATH_ENABLED);
    }

    public function getWarningMessage(): string
    {
        $message = $this->getConfig(self::XML_PATH_WARNING_MESSAGE);

        if (!empty($message)) {
            $storeId = $this->storeManager->getStore()->getId();
            /** @var \Magento\Cms\Model\Template\Filter $blockFilter */
            $blockFilter = $this->filterProvider->getBlockFilter();

            return $blockFilter->setStoreId($storeId)->filter($message);
        }

        return $message;
    }

    /**
     * @param int|null $storeId
     * @return array<string>
     */
    public function getEmailSendTo(int $storeId = null): array
    {
        $emails = array_map(
            'trim',
            explode(',', (string) $this->getConfig(self::XML_PATH_EMAILS, $storeId))
        );

        foreach ($emails as $key => $email) {
            if (empty($email)) {
                unset($emails[$key]);
            }
        }

        return $emails;
    }

    private function getConfig(string $path, int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            $path,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isCustomerBlocked(CustomerInterface $customerData): bool
    {
        $blockedAttribute = $customerData->getCustomAttribute(
            AddBlockedCustomerAttribute::ATTRIBUTE_CODE
        );

        return (bool)(($blockedAttribute && $blockedAttribute->getValue()) ? $blockedAttribute->getValue(): false);
    }
}
