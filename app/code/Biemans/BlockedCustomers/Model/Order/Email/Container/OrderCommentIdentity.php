<?php

declare(strict_types=1);

namespace <PERSON>iemans\BlockedCustomers\Model\Order\Email\Container;

use Magento\Sales\Model\Order;

class OrderCommentIdentity extends \Magento\Sales\Model\Order\Email\Container\OrderCommentIdentity
{
    protected ?Order $order = null;

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }
}
