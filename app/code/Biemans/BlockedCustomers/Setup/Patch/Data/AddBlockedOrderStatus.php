<?php

declare(strict_types=1);

namespace Biemans\BlockedCustomers\Setup\Patch\Data;

use Magento\Framework\Exception\AlreadyExistsException;
use Magento\Framework\Setup\{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};
use Magento\Sales\Model\Order\{
    Status,
    StatusFactory
};
use Magento\Sales\Model\ResourceModel\Order\{
    Status as StatusResource,
    StatusFactory as StatusResourceFactory
};
use Magento\Sales\Setup\SalesSetupFactory;

class AddBlockedOrderStatus implements DataPatchInterface
{
    const ORDER_STATUS_BLOCKED = 'blocked';
    const ORDER_STATE_BLOCKED = 'blocked';

    private ModuleDataSetupInterface $moduleDataSetup;
    private StatusFactory $statusFactory;
    private StatusResourceFactory $statusResourceFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        StatusFactory $statusFactory,
        StatusResourceFactory $statusResourceFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->statusFactory = $statusFactory;
        $this->statusResourceFactory = $statusResourceFactory;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();


        /** @var StatusResource $statusResource */
        $statusResource = $this->statusResourceFactory->create();
        /** @var Status $status */
        $status = $this->statusFactory->create();
        $status->setData([
            'status' => self::ORDER_STATUS_BLOCKED,
            'label' => __('Blocked'),
        ]);

        try {
            $statusResource->save($status);
        } catch (AlreadyExistsException $exception) {
            return $this;
        }

        $status->assignState(self::ORDER_STATE_BLOCKED, true, true);

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
