<script>
    function biemansStockStatus(element, data) {
        return {
            element: element,
            restUrl: data.restUrl ? data.restUrl : '',
            label: data.label ? data.label : '',
            messageSelector: '.stock-message',
            statusSelector: '.stock-status',

            init() {
                let self = this;

                // Gather products
                let products = [];
                Array.from(document.querySelectorAll(self.messageSelector + '[data-stock-product-id]'))
                    .map(element => {
                        products.push(element.getAttribute('data-stock-product-id'));
                    }
                );

                if (
                    this.restUrl &&
                    products.length
                ) {
                    fetch(this.restUrl + JSON.stringify(products) + '?t=' + Date.now(), {
                        "headers": {
                            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                        },
                        "method": "GET",
                        "mode": "cors",
                        "credentials": "include"
                    })
                    .then(function(response) {
                        if (response.ok) {
                            return response.json();
                        }
                    })
                    .then(function(response) {
                        let stockItems = JSON.parse(response);

                        for (let key in stockItems) {
                            document.querySelectorAll(self.messageSelector + '[data-stock-product-id="' + key + '"]').forEach((messageElement) => {
                                if ((parseInt(stockItems[key]) > 0)) {
                                    messageElement.innerHTML = self.label.replace('%s', parseInt(stockItems[key]));
                                    messageElement.style.display = 'flex';

                                    document.querySelectorAll(self.statusSelector + '[data-stock-product-id="' + key + '"]').forEach((statusElement) => {
                                        statusElement.style.display = 'none';
                                    });
                                }
                            });
                        }
                    })
                }
            }
        }
    }
</script>
