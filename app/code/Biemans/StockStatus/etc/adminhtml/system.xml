<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="biemans_stock_status" translate="label" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Stock Status</label>
            <tab>biemans</tab>
            <resource>Biemans_StockStatus::system_config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="label" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Stock Label</label>
                    <comment><![CDATA[Use %s for qty placeholder in message: i.e. "%s more available".]]></comment>
                </field>
                <field id="cache_lifetime" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Stock cache lifetime (seconds)</label>
                    <validate>validate-digits</validate>
                    <comment>Will be used to store stock data/product after first access for set amount of seconds.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
