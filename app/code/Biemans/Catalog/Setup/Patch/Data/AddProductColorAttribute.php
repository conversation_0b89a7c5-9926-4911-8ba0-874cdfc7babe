<?php

declare(strict_types=1);

namespace Biemans\Catalog\Setup\Patch\Data;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddProductColorAttribute implements DataPatchInterface
{
    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;
    private ProductAttributeRepositoryInterface $productAttributeRepository;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        ProductAttributeRepositoryInterface $productAttributeRepository
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->productAttributeRepository = $productAttributeRepository;
    }

    public function apply()
    {
        $attributeCode = 'color';

        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Product::ENTITY,
            $attributeCode,
            [
                'backend' => '',
                'comparable' => false,
                'default' => '',
                'filterable' => true,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'input' => 'select',
                'label' => 'Color',
                'required' => false,
                'searchable' => false,
                'type' => 'int',
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'group' => 'General',
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => true,
            ]
        );

        $attribute = $this->productAttributeRepository->get($attributeCode);

        /** @phpstan-ignore-next-line */
        $attribute->addData([
            'additional_data' => '{"swatch_input_type":"visual","update_product_preview_image":"0", "use_product_image_for_swatch":"0"}',
            'frontend_input' => 'select'
        ]);

        $this->productAttributeRepository->save($attribute);

        // Add attribute to all sets
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);
        $attributeSetIds = $eavSetup->getAllAttributeSetIds($entityTypeId);
        // Add attribute to all groups
        foreach ($attributeSetIds as $attributeSetId) {
            $attributeGroupId = $eavSetup->getAttributeGroupId($entityTypeId, $attributeSetId, 'General');
            /** @phpstan-ignore-next-line */
            $eavSetup->addAttributeToGroup($entityTypeId, $attributeSetId, $attributeGroupId, $attributeCode, 900);
        }

        return $this;
    }

    public static function getDependencies(): array
    {
        return [];
    }

    public function getAliases(): array
    {
        return [];
    }
}
