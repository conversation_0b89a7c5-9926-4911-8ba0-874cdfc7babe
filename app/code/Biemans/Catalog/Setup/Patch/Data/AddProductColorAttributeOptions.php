<?php

declare(strict_types=1);

namespace <PERSON>iemans\Catalog\Setup\Patch\Data;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddProductColorAttributeOptions implements DataPatchInterface
{
    private ProductAttributeRepositoryInterface $productAttributeRepository;

    public function __construct(
        ProductAttributeRepositoryInterface $productAttributeRepository
    ) {
        $this->productAttributeRepository = $productAttributeRepository;
    }

    public function apply()
    {
        $attributeCode = 'color';
        $attribute = $this->productAttributeRepository->get($attributeCode);

        $attributeOptions = [
            'optionvisual' => [
                'value' => [
                    'option_1' => ['.01'],
                    'option_2' => ['.02'],
                    'option_3' => ['.26'],
                    'option_4' => ['.27']
                ]
            ],
            'swatchvisual' => [
                'value' => [
                    'option_1' => '#D4BC65',
                    'option_2' => '#D3D3D3',
                    'option_3' => '#88543B',
                    'option_4' => '#88543B'
                ]
            ],
        ];

        /** @phpstan-ignore-next-line */
        $attribute->addData($attributeOptions);
        $this->productAttributeRepository->save($attribute);

        return $this;
    }

    public static function getDependencies(): array
    {
        return [
            AddProductColorAttribute::class
        ];
    }

    public function getAliases(): array
    {
        return [];
    }
}
