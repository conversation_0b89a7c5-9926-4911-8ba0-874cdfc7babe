<?php

declare(strict_types=1);

namespace <PERSON>ie<PERSON>\Catalog\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\
{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};

class AddOutOfStockMessageAttribute implements DataPatchInterface
{
    const ATTRIBUTE_CODE = 'out_of_stock_message';

    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $eavSetup->addAttribute(
            $entityTypeId,
            self::ATTRIBUTE_CODE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'input' => 'textarea',
                'label' => 'Out Of Stock Message',
                'required' => false,
                'searchable' => false,
                'type' => 'text',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'group' => 'General'
            ]
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
