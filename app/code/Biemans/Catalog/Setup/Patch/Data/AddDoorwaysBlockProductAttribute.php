<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Catalog\Setup\Patch\Data;

use Biemans\Catalog\Model\Product\Attribute\Source\CmsBlocks;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};

class AddDoorwaysBlockProductAttribute implements DataPatchInterface
{
    const ATTRIBUTE_CODE = 'doorways_block';

    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $eavSetup->addAttribute(
            Product::ENTITY,
            self::ATTRIBUTE_CODE,
            [
                'type' => 'varchar',
                'label' => 'Doorways Block',
                'input' => 'select',
                'source' => CmsBlocks::class,
                'frontend' => '',
                'required' => false,
                'sort_order' => '200',
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'default' => null,
                'visible' => true,
                'user_defined' => true,
                'searchable' => false,
                'filterable' => false,
                'comparable' => false,
                'visible_on_front' => false,
                'unique' => false,
                'apply_to' => '',
                'group' => 'Content',
                'used_in_product_listing' => false,
                'used_for_promo_rules' => false,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => true,
                'option' => ''
            ]
        );

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
