<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Catalog\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

/**
 * Class AddUKMaxQtyAttribute - creates attribute used for the UK Website Qty's setting.
 * Since Max Qty is global - this attributes will be used.
 */
class AddUKMaxQtyAttribute implements DataPatchInterface
{
    public const UK_MAX_QTY_ATTRIBUTE_CODE = 'uk_max_qty';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $eavSetup->addAttribute(
            $entityTypeId,
            self::UK_MAX_QTY_ATTRIBUTE_CODE,
            [
                'type' => 'int',
                'label' => 'United Kingdom Maximum Qty Allowed in Shopping Cart',
                'input' => 'text',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'group' => 'General',
                'required' => false,
                'used_in_product_listing' => true,
                'used_for_promo_rules' => false,
                'visible_on_front' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => true,
                'user_defined' => 1,
                'filterable' => 1,
                'filterable_in_search' => 1,
            ]
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
