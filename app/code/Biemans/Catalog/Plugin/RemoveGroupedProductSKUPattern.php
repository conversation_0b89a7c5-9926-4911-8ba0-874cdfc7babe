<?php

declare(strict_types=1);

namespace <PERSON>ie<PERSON>\Catalog\Plugin;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\GroupedProduct\Model\Product\Type\Grouped;
use Magento\Store\Model\ScopeInterface;

class RemoveGroupedProductSKUPattern
{
    const XML_PATH_REMOVE_REGEX = 'catalog/frontend/sku_remove_pattern';

    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    public function afterGetSku(
        ProductInterface $subject,
        ?string $sku
    ): ?string {
        if (
            $sku !== null
            && ($removeRegex = $this->getRemoveRegex())
            && ($subject->getTypeId() === Grouped::TYPE_CODE)
        ) {
            return preg_replace($removeRegex, '', $sku);
        }

        return $sku;
    }

    private function getRemoveRegex(): string
    {
        static $regex = null;

        if (is_null($regex)) {
            $regex = $this->getConfig(self::XML_PATH_REMOVE_REGEX);
        }

        return $regex;
    }

    private function getConfig(string $path, int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            $path,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
