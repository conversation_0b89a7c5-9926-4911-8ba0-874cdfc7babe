<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Catalog\Plugin\CatalogInventory\Data;

use <PERSON>iemans\Catalog\Setup\Patch\Data\AddPolandMaxQtyAttribute;
use <PERSON><PERSON>mans\Catalog\Setup\Patch\Data\AddPolandQtyAttributes;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogInventory\Api\Data\StockItemInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Interceptor for @see StockItemInterface
 */
class PolandQtyModification
{
    private const POLAND_WEBSITE_CODE = 'biemans_pl';

    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly StoreManagerInterface $storeManager
    ) {
    }

    /**
     * Intercepted method getQtyIncrements.
     *
     * @param StockItemInterface $subject
     * @param false|float $result
     *
     * @return false|float
     * @throws LocalizedException
     * @see StockItemInterface::getQtyIncrements
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetQtyIncrements(StockItemInterface $subject, false|float $result): false|float
    {
        if ($this->storeManager->getWebsite()->getCode() !== self::POLAND_WEBSITE_CODE) {
            return $result;
        }

        return $this->getValueFromProduct(AddPolandQtyAttributes::PL_QTY_INCREMENT_ATTRIBUTE_CODE,
            (int)$subject->getProductId()) ?? $result;
    }

    /**
     * Intercepted method getMinSaleQty.
     *
     * @param StockItemInterface $subject
     * @param float $result
     *
     * @return float
     * @throws LocalizedException
     * @see StockItemInterface::getMinSaleQty
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetMinSaleQty(StockItemInterface $subject, float $result): float
    {
        if ($this->storeManager->getWebsite()->getCode() !== self::POLAND_WEBSITE_CODE) {
            return $result;
        }

        return $this->getValueFromProduct(AddPolandQtyAttributes::PL_MIN_QTY_ATTRIBUTE_CODE,
            (int)$subject->getProductId()) ?? $result;
    }

    /**
     * Intercepted method getMaxSaleQty.
     *
     * @param StockItemInterface $subject
     * @param float $result
     *
     * @return float
     * @throws LocalizedException
     * @see StockItemInterface::getMinSaleQty
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetMaxSaleQty(StockItemInterface $subject, float $result): float
    {
        if ($this->storeManager->getWebsite()->getCode() !== self::POLAND_WEBSITE_CODE) {
            return $result;
        }

        return $this->getValueFromProduct(AddPolandMaxQtyAttribute::PL_MAX_QTY_ATTRIBUTE_CODE,
            (int)$subject->getProductId()) ?? $result;
    }

    /**
     * method return specific for Poland Qty attribute.
     * @param string $attributeCode
     * @param int $productId
     * @return float|null
     */
    private function getValueFromProduct(string $attributeCode, int $productId): ?float
    {
        try {
            $product = $this->productRepository->getById($productId);
            /** @phpstan-ignore-next-line */
            if (!empty($product->getData($attributeCode))) {
                /** @phpstan-ignore-next-line */
                return (float)$product->getData($attributeCode);
            }
        } catch (NoSuchEntityException $exception) {
            //do nothing and return null later.
        }

        return null;
    }
}
