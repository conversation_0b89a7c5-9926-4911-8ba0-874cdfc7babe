<?php

declare(strict_types=1);

namespace <PERSON>iemans\Catalog\ViewModel;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Framework\Pricing\Amount\AmountInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;

/**
 * ViewModel checks all TierPrices and return lowest price.
 * Used on listing pages.
 */
class TierPriceMinimumAmount implements ArgumentInterface
{
    /**
     * return lowest price tier price for the product
     * @param ProductInterface $product
     * @return AmountInterface|null
     */
    public function getLowestTierPriceAmount(ProductInterface $product): ?AmountInterface
    {
        /** @phpstan-ignore-next-line */
        $tierPrice = $product->getPriceInfo()
            ->getPrice(TierPrice::PRICE_CODE)
            ->getTierPriceList();

        if (empty($tierPrice)) {
            return null;
        }
        $lowestPrice = null;
        $result = null;
        foreach ($tierPrice as $price) {
            /** @var AmountInterface $priceAmount */
            $priceAmount = $price['price'];
            if ($lowestPrice == null || $lowestPrice > $priceAmount->getValue()) {
                $lowestPrice = $priceAmount->getValue();
                $result = $price['price'];
            }
        }

        return $result;
    }
}
