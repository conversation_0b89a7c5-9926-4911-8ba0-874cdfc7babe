<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\Catalog\ViewModel;

use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCollectionProductAttribute;
use Magento\Catalog\Model\Product;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\GroupedProduct\Model\Product\Type\Grouped;

/**
 * Class Collection - view model to retrieve label related based on Biemans Collection Attribute.
 * Next labels supported based on the Attribute:
 *  - Out of collection
 * @TODO
 * move logic of NEW label to this attribute and class
 */
class CollectionLabel implements ArgumentInterface
{
    public const OUT_COLLECTION_LABEL_TEXT = 'Out 31-12';
    public const OUT_COLLECTION_OPTION_VALUE = 'Out of collection';

    /**
     * If any of children or product itself has Out of Collection option we show label
     */
    public function hasOutCollectionLabel(Product $product): bool
    {
        if ($product->getTypeId() === Grouped::TYPE_CODE) {
            /** @phpstan-ignore-next-line */
            $associatedProducts = $product->getTypeInstance()
                ->getAssociatedProductCollection($product)
                ->addAttributeToSelect([AddCollectionProductAttribute::ATTRIBUTE_CODE]);
            foreach ($associatedProducts as $associatedProduct) {
                if ($this->checkOption($associatedProduct, self::OUT_COLLECTION_OPTION_VALUE)) {
                    return true;
                }
            }
        }

        return $this->checkOption($product, self::OUT_COLLECTION_OPTION_VALUE);
    }

    public function getOutCollectionLabel(): string
    {
        return self::OUT_COLLECTION_LABEL_TEXT;
    }

    /**
     * we check if option required for specific label is present in the product Biemans Collection attribute.
     */
    private function checkOption(Product $product, string $optionValue): bool
    {
        $collection = $product->getCustomAttribute(AddCollectionProductAttribute::ATTRIBUTE_CODE)?->getValue();
        if (empty($collection)) {
            return false;
        }
        foreach (explode(',', $collection) as $optionId) {
            /* @phpstan-ignore-next-line */
            $attribute = $product->getResource()->getAttribute(AddCollectionProductAttribute::ATTRIBUTE_CODE);
            $value = (string)$attribute->getSource()->getOptionText($optionId);
            if (strtolower($value) === strtolower($optionValue)) {
                return true;
            }
        }

        return false;
    }
}
