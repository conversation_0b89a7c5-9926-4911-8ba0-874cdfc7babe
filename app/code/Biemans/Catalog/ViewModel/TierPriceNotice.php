<?php

declare(strict_types=1);

namespace <PERSON>iemans\Catalog\ViewModel;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Framework\Pricing\Amount\Base;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class TierPriceNotice implements ArgumentInterface
{
    private const NOTICE_TEXT = 'Order %s for lowest price';

    /**
     * We get tier prices to show on the PDP notice about qty required for minimum price
     * @param ProductInterface $product
     * @return string
     */
    public function getQtyForTierPrices(ProductInterface $product): string
    {
        /** @phpstan-ignore-next-line */
        $prices = $product->getPriceInfo()
            ->getPrice(TierPrice::PRICE_CODE)
            ->getTierPriceList();
        if (empty($prices)) {
            return '';
        }
        $lowestPrice = null;
        $amountToBuy = 0;
        foreach ($prices as $price) {
            /** @var Base $priceAmount */
            $priceAmount = $price['price'];
            if ($lowestPrice == null || $lowestPrice > $priceAmount->getValue()) {
                $lowestPrice = $priceAmount->getValue();
                $amountToBuy = $price['price_qty'];
            }
        }
        if ($amountToBuy != 0) {
            return sprintf(__(self::NOTICE_TEXT)->render(), $amountToBuy);
        }

        return '';
    }
}
