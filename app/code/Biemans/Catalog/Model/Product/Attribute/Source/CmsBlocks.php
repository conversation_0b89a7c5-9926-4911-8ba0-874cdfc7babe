<?php

declare(strict_types=1);

namespace <PERSON>iemans\Catalog\Model\Product\Attribute\Source;

use Magento\Cms\Model\Config\Source\Block;

class CmsBlocks extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    private Block $block;

    public function __construct(
        Block $block
    ) {
        $this->block = $block;
    }

    /**
     * @return array<mixed>
     */
    public function getAllOptions()
    {
        return array_merge([
                [
                    'value' => '',
                    'label' => __('-- No block --')
                ]
            ],
            $this->block->toOptionArray()
        );
    }
}
