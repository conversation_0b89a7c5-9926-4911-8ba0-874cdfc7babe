<?php

declare(strict_types=1);

namespace Biemans\CustomerMenu\Plugin\Customer\Block\Account;

use Magento\Customer\Block\Account\Navigation;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Element\Html\Link;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Interceptor for @see Navigation -
 *  removes defined links from defined websites - so Customer do not have them shown.
 */
class RemoveLink
{
    private const REMOVE_LINKS_MAP = [
        'biemans_pl' => [
            'customer-account-navigation-sendcloud-parcel'
        ]
    ];

    public function __construct(private readonly StoreManagerInterface $storeManager) {}

    /**
     * Intercepted method getLinks.
     *
     * @param Navigation $subject
     * @param Link[] $result
     *
     * @return Link[]
     * @throws LocalizedException
     * @see Navigation::getLinks
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetLinks(Navigation $subject, array $result): array
    {
        $currentWebsite = $this->storeManager->getWebsite();
        if (!in_array($currentWebsite->getCode(), array_keys(self::REMOVE_LINKS_MAP))) {
            return $result;
        }

        $linkPathsForRemoval = self::REMOVE_LINKS_MAP[$currentWebsite->getCode()];
        foreach ($result as $key =>$link) {
            if (!in_array($link->getNameInLayout(), $linkPathsForRemoval)) {
                continue;
            }
            unset($result[$key]);
        }

        return $result;
    }
}
