<?php

declare(strict_types=1);

namespace <PERSON>ie<PERSON>\ErrorAvOrderStatus\Plugin;

use <PERSON>iemans\CustomerDebtorNumber\Setup\Patch\Data\AddDebtorNumberCustomerAttribute;
use <PERSON>iemans\Prepress\Model\Config;
use Magento\Framework\App\Area;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\{OrderRepositoryInterface, OrderRepositoryInterfaceFactory};
use Psr\Log\LoggerInterface;

class NotifyAdminOnErrorAv
{
    const ERROR_AV_STATUS = 'error_av';

    public function __construct(
        private readonly Config $config,
        private readonly TransportBuilder $transportBuilder,
        private readonly StateInterface $inlineTranslation,
        private readonly LoggerInterface $logger,
        private readonly OrderRepositoryInterfaceFactory $orderRepositoryFactory
    ) {}

    public function afterSave(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ): OrderInterface {
        if ($order->getStatus() !== self::ERROR_AV_STATUS) {
            return $order;
        }

        try {
            /** @var \Magento\Sales\Model\Order&OrderInterface $order */
            $templateId = 'biemans_admin_notification_error_av_status_template';
            $logoUrl = $this->config->getEmailLogoUrl((int)$order->getStore()->getStoreId());
            $orderRepository = $this->orderRepositoryFactory->create();
            /** @var \Magento\Sales\Model\Order $loadedOrder */
            $loadedOrder = $orderRepository->get($order->getEntityId());
            $debtorNumber = $loadedOrder->getData(AddDebtorNumberCustomerAttribute::ATTRIBUTE_CODE);

            $templateVars = [
                'order' => $loadedOrder,
                'logo_url' => $logoUrl,
                'debtor_number' => $debtorNumber,
                'billing_name' => $loadedOrder->getBillingAddress()->getFirstname() . ' ' . $loadedOrder->getBillingAddress()->getLastname(),
            ];

            $this->inlineTranslation->suspend();

            $templateOptions = [
                'area' => Area::AREA_FRONTEND,
                'store' => $loadedOrder->getStore()->getId()
            ];

            $this->transportBuilder
                ->setTemplateIdentifier($templateId)
                ->setTemplateOptions($templateOptions)
                ->setTemplateVars($templateVars)
                ->setFrom($this->config->getAdminEmail())
                ->addTo($this->config->getAdminEmail()['email']);

            $transport = $this->transportBuilder->getTransport();

            $transport->sendMessage();

            $this->inlineTranslation->resume();
        } catch (\Throwable $e) {
            $this->logger->info($e->getMessage());
            return $order;
        }

        return $order;
    }
}
