<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Sendcloud\Model\ParcelImport\WebhookValidator;

use <PERSON><PERSON><PERSON>\Sendcloud\Exception\ExpectedInvalidDataException;
use B<PERSON><PERSON>\Sendcloud\Model\ParcelImport\WebhookValidatorInterface;
use <PERSON>iemans\Sendcloud\Service\ParcelIdProvider;
use RuntimeException;

/**
 * Class ParcelUnique - checks id we have parcel already imported.
 */
class ParcelUnique implements WebhookValidatorInterface
{
    public function __construct(private readonly ParcelIdProvider $provider) {
    }

    public function validate(array $webhookData): void {
        if (empty($webhookData['parcel']['id'])) {
            throw new RuntimeException('No Parcel ID found in the webhookData.');
        }
        if (!empty($this->provider->getParcelByExternalParcelId($webhookData['parcel']['id']))) {
            throw new ExpectedInvalidDataException(
                sprintf('%s Parcel Id found in the system.', $webhookData['parcel']['id']));
        }
    }
}
