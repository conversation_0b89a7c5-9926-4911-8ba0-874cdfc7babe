<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\Sendcloud\Controller\Parcel;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;

/**
 * Controller for the 'sendcloud/parcel/index' URL route.
 */
class Index implements HttpGetActionInterface
{
    public function __construct(
        private readonly ResultFactory $resultFactory
    ) {
    }

    /**
     * Execute controller action.
     */
    public function execute() {

        return $this->resultFactory->create(ResultFactory::TYPE_PAGE);
    }
}
