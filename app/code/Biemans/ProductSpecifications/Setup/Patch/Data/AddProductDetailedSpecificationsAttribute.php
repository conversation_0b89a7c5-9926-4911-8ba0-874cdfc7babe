<?php

declare(strict_types=1);

namespace Biemans\ProductSpecifications\Setup\Patch\Data;

use Biemans\ProductSpecifications\Model\Product\Attribute\Source\Attributes;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\{
    Backend\ArrayBackend,
    ScopedAttributeInterface
};
use Magento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};

class AddProductDetailedSpecificationsAttribute implements DataPatchInterface
{
    const ATTRIBUTE_CODE = 'detailed_specifications_attributes';

    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $eavSetup->addAttribute(
            Product::ENTITY,
            self::ATTRIBUTE_CODE,
            [
                'type' => 'text',
                'label' => 'Detailed Product Specifications',
                'input' => 'multiselect',
                'source' => Attributes::class,
                'frontend' => '',
                'required' => false,
                'backend' => ArrayBackend::class,
                'sort_order' => '210',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'default' => null,
                'visible' => true,
                'user_defined' => true,
                'searchable' => false,
                'filterable' => false,
                'comparable' => false,
                'visible_on_front' => false,
                'unique' => false,
                'apply_to' => '',
                'group' => 'General',
                'used_in_product_listing' => true,
                'used_for_promo_rules' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'option' => ''
            ]
        );

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
