<?php

declare(strict_types=1);

namespace Biemans\ProductSpecifications\Model\Product\Attribute\Source;

use Biemans\ProductSpecifications\Setup\Patch\Data\{
    AddProductDetailedSpecificationsAttribute,
    AddProductSpecificationsAttribute
};
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Framework\Api\{
    FilterBuilder,
    Search\FilterGroupBuilder,
    SearchCriteriaBuilderFactory,
    SortOrder,
    SortOrderFactory
};

class Attributes extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    private SearchCriteriaBuilderFactory $searchCriteriaBuilderFactory;
    private FilterBuilder $filterBuilder;
    private FilterGroupBuilder $filterGroupBuilder;
    private ProductAttributeRepositoryInterface $attributeRepository;
    private SortOrderFactory $sortOrderFactory;

    public function __construct(
        SearchCriteriaBuilderFactory $searchCriteriaBuilderFactory,
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        ProductAttributeRepositoryInterface $attributeRepository,
        SortOrderFactory $sortOrderFactory
    ) {
        $this->searchCriteriaBuilderFactory = $searchCriteriaBuilderFactory;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->attributeRepository = $attributeRepository;
        $this->sortOrderFactory = $sortOrderFactory;
    }

    /**
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\InputException
     */
    public function getAllOptions()
    {
        /** @var \Magento\Framework\Api\Search\FilterGroup[] $filterGroups */
        $filterGroups = [
            $this->filterGroupBuilder
                ->setFilters([
                    $this->filterBuilder
                        ->setConditionType('nin')
                        ->setField('attribute_code')
                        ->setValue([
                            AddProductDetailedSpecificationsAttribute::ATTRIBUTE_CODE,
                            AddProductSpecificationsAttribute::ATTRIBUTE_CODE
                        ])
                        ->create()
                ])
                ->create()
        ];

        /** @var SortOrder $sortOrder */
        $sortOrder = $this->sortOrderFactory->create();
        $sortOrder->setField('frontend_label')
            ->setDirection('asc');

        $searchCriteriaBuilder = $this->searchCriteriaBuilderFactory->create();
        $searchCriteriaBuilder
            ->setFilterGroups($filterGroups)
            ->addSortOrder($sortOrder);
        $attributes = $this->attributeRepository->getList($searchCriteriaBuilder->create());

        $options = [];
        foreach ($attributes->getItems() as $attribute) {
            if (!$attribute->getIsVisible()) {
                continue;
            }

            $options[] = [
                'value' => $attribute->getAttributeCode(),
                'label' => $attribute->getDefaultFrontendLabel() . ' [' . $attribute->getAttributeCode() . ']'
            ];
        }

        return $options;
    }
}
