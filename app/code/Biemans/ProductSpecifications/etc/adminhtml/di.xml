<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <type name="Magento\Config\Model\Config\Structure\Data">
        <plugin
            name="biemans_detailed_specifications_attributes_per_type"
            type="Biemans\ProductSpecifications\Plugin\Config\Field\ProductTypeDetailedSpecifications"
        />
    </type>
</config>
