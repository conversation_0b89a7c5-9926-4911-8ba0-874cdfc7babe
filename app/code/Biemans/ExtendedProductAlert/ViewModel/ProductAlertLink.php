<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedProductAlert\ViewModel;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\Url\Helper\Data as UrlHelper;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;

class ProductAlertLink implements ArgumentInterface
{
    private UrlHelper $urlHelper;
    private UrlInterface $urlBuilder;
    private StoreManagerInterface $storeManager;

    public function __construct(
        UrlHelper $urlHelper,
        UrlInterface $urlBuilder,
        StoreManagerInterface $storeManager
    ) {
        $this->urlHelper = $urlHelper;
        $this->urlBuilder = $urlBuilder;
        $this->storeManager = $storeManager;
    }

    public function getSaveUrl(ProductInterface $product, string $type): string
    {
        $baseUrl = $this->urlBuilder->getBaseUrl();
        $currentUrl = $this->urlBuilder->getCurrentUrl();
        if (!strpos($currentUrl, $baseUrl)) {
            // Force URL to have store code in it
            $storeCode = $this->storeManager->getStore()->getCode();
            $baseUrlWithoutStore = str_replace('/' . $storeCode, '', $baseUrl);
            $currentUrl = str_replace($baseUrlWithoutStore, $baseUrl, $currentUrl);
        }

        return $this->urlBuilder->getUrl(
            'biemans-stock-alert/update/' . $type,
            [
                'product_id' => $product->getId(),
                ActionInterface::PARAM_NAME_URL_ENCODED => $this->urlHelper->getEncodedUrl($currentUrl)
            ]
        );
    }
}
