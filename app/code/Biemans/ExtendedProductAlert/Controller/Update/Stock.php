<?php

declare(strict_types=1);

namespace Biemans\ExtendedProductAlert\Controller\Update;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\ProductAlert\Controller\Add as AddController;
use Magento\ProductAlert\Model\StockFactory;
use Magento\Store\Model\StoreManagerInterface;

class Stock extends AddController implements HttpPostActionInterface
{
    private ProductRepositoryInterface $productRepository;
    private StoreManagerInterface $storeManager;
    private StockFactory $stockFactory;

    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        ProductRepositoryInterface $productRepository,
        StoreManagerInterface $storeManager,
        StockFactory $stockFactory
    ) {
        parent::__construct($context, $customerSession);
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->stockFactory = $stockFactory;
    }

    /**
     * Method for updating info about product alert stock
     * if stock alert exist - remove
     * if stock alert does not exist - add
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $responseData = [];
        $backUrl = $this->getRequest()->getParam(Action::PARAM_NAME_URL_ENCODED);
        $productId = (int)$this->getRequest()->getParam('product_id');
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        if (!$backUrl || !$productId) {
            $resultRedirect->setPath('/');
            return $resultRedirect;
        }
        $responseData['backUrl'] = $backUrl;

        try {
            /* @var $product \Magento\Catalog\Model\Product */
            $product = $this->productRepository->getById($productId);
            $store = $this->storeManager->getStore();

            /** @var \Magento\ProductAlert\Model\Stock $model */
            // There are no service contracts to save the model
            $model = $this->stockFactory->create()
                ->setCustomerId($this->customerSession->getCustomerId())
                ->setProductId($product->getId())
                ->setWebsiteId($store->getWebsiteId())
                ->setStoreId($store->getId())
                ->loadByParam();
            if ($model->getId()) {
                $model->delete(); /** @phpstan-ignore-line */
            }else{
                $model->save(); /** @phpstan-ignore-line */
            }
            $responseData['success'] = __('Alert subscription has been updated.');
        } catch (NoSuchEntityException $noEntityException) {
            $responseData['error_message'] = __('There are not enough parameters.');
        } catch (\Exception $e) {
            $responseData['error_message'] = __("The alert subscription couldn't update at this time. Please try again later.");
        }

        /** @var Json $resultJson */
        $resultJson = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $resultJson->setData($responseData);

        return $resultJson;
    }
}
