<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="customer">
            <group id="guest_restrictions" translate="label" sortOrder="900" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Guest Restrictions</label>
                <field id="enable" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Hide prices and disable cart and checkout.</comment>
                </field>
                <field id="disable_button_text" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Disable Button Text</label>
                    <comment>This text is used on the button that is visible when checkout is disabled.</comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="disable_button_link" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Disable Button Link</label>
                    <comment>This link is used for the button that is visible when checkout is disabled. Leave empty to disable the button.</comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
