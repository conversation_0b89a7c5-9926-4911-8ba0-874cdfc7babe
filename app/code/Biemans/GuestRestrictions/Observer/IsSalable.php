<?php

declare(strict_types=1);

namespace <PERSON>iemans\GuestRestrictions\Observer;

use Biemans\GuestRestrictions\Model\Config;
use Magento\Framework\Event\{
    Observer,
    ObserverInterface
};

class IsSalable implements ObserverInterface
{
    private Config $config;

    public function __construct(
        Config $config
    ) {
        $this->config = $config;
    }

    public function execute(Observer $observer)
    {
        $observer->getSalable()->setIsSalable(
            !$this->config->isGuestRestricted()
            && $observer->getSalable()->getIsSalable()
        );
    }
}
