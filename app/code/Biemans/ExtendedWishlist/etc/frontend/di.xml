<?xml version="1.0" encoding="UTF-8" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\CustomerData\SectionPoolInterface">
        <arguments>
            <argument name="sectionSourceMap" xsi:type="array">
                <item name="wishlist_items" xsi:type="string">Biemans\ExtendedWishlist\CustomerData\Wishlist</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Wishlist\Model\ResourceModel\Item\Collection">
        <plugin name="biemans_wishlist_display_not_visible_simple_products" type="Biemans\ExtendedWishlist\Plugin\DisplayNotVisibleSimpleProducts"/>
    </type>
    <type name="Magento\Wishlist\Block\Customer\Wishlist">
        <plugin name="biemans_wishlist_hide_grouped_products" type="Biemans\ExtendedWishlist\Plugin\HideGroupedProducts"/>
    </type>
    <type name="Magento\Wishlist\Model\Item">
        <plugin name="biemans_wishlist_keep_item_in_wishlist_on_add_to_cart" type="Biemans\ExtendedWishlist\Plugin\KeepItemInWishlistOnAddToCart"/>
    </type>
    <type name="Magento\Wishlist\Controller\Index\Remove">
        <plugin name="biemans_wishlist_remove_grouped_product" type="Biemans\ExtendedWishlist\Plugin\RemoveGroupedProduct"/>
    </type>
</config>
