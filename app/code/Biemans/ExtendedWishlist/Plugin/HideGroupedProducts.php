<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\ExtendedWishlist\Plugin;

use Magento\Wishlist\Block\Customer\Wishlist;
use Magento\Wishlist\Model\ResourceModel\Item\Collection;

class HideGroupedProducts
{
    public function afterGetWishlistItems(Wishlist $subject, Collection $collection): Collection
    {
        static $typeFilterAdded = null;

        if (is_null($typeFilterAdded)) {
            $collection->getSelect()->joinLeft( /** @phpstan-ignore-line */
                ['cpe' => 'catalog_product_entity'],
                'main_table.product_id = cpe.entity_id',
                ['type_id', 'sku']
            );
            $collection->addFieldToFilter('type_id', ['neq' => 'grouped']);
            $collection->unshiftOrder('sku', 'ASC');

            $typeFilterAdded = true;
        }

        return $collection;
    }
}
