<?php

declare(strict_types=1);

namespace Biemans\ExtendedWishlist\Plugin;

use Magento\Wishlist\Controller\Index\Remove;
use Magento\Wishlist\Model\{
    Item,
    ItemFactory
};
use Psr\Log\LoggerInterface;

class RemoveGroupedProduct
{
    private ItemFactory $itemFactory;
    private LoggerInterface $logger;

    public function __construct(
        ItemFactory $itemFactory,
        LoggerInterface $logger
    ) {
        $this->itemFactory = $itemFactory;
        $this->logger = $logger;
    }

    public function beforeExecute(Remove $subject): Remove
    {
        try {
            $id = (int)$subject->getRequest()->getParam('item');

            /** @var Item $item */
            $item = $this->itemFactory->create()->loadWithOptions($id);

            // Remove grouped product when one simple associated product is removed
            if (
                !empty($parentId = (int)$item->getBuyRequest()->getData('parent_item_id'))
                && ($id !== $parentId)
            ) {
                /** @var Item $groupItem */
                $groupItem = $this->itemFactory->create()->load($parentId); /** @phpstan-ignore-line */
                $groupItem->delete(); /** @phpstan-ignore-line */
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_ExtendedWishlist: ' . $e->getMessage());
        }

        return $subject;
    }
}
