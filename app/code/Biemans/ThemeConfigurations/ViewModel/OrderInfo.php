<?php

declare(strict_types=1);

namespace Biemans\ThemeConfigurations\ViewModel;

use Magento\Customer\Api\Data\{
    AddressInterface,
    CustomerInterface
};
use Magento\Customer\Api\{
    AddressRepositoryInterface,
    CustomerRepositoryInterface
};
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\Data\OrderInterface;

class OrderInfo implements ArgumentInterface
{
    private AddressRepositoryInterface $addressRepository;
    private CustomerRepositoryInterface $customerRepository;
    private OrderRepositoryInterface $orderRepository;
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        AddressRepositoryInterface $addressRepository,
        CustomerRepositoryInterface $customerRepository,
        OrderRepositoryInterface $orderRepository,
        CartRepositoryInterface $cartRepository
    ) {
        $this->addressRepository = $addressRepository;
        $this->customerRepository = $customerRepository;
        $this->orderRepository = $orderRepository;
        $this->cartRepository = $cartRepository;
    }

    public function getOrder(int $orderId): ?OrderInterface
    {
        static $order = null;

        if (is_null($order)) {
            try {
                $order = $this->orderRepository->get($orderId);
            } catch (\Exception $e) {
            }
        }

        return $order;
    }

    public function getCustomerData(int $customerId): ?CustomerInterface
    {
        static $customer = null;

        if (is_null($customer)) {
            try {
                $customer = $this->customerRepository->getById($customerId);
            } catch (\Exception $e) {
            }
        }

        return $customer;
    }

    public function getCustomerAddress(int $addressId): ?AddressInterface
    {
        static $address = null;

        if (is_null($address)) {
            try {
                $address = $this->addressRepository->getById($addressId);
            } catch (\Exception $e) {
            }
        }

        return $address;
    }

    public function getQuote(int $quoteId): ?CartInterface
    {
        static $quote = null;

        if (is_null($quote)) {
            try {
                $quote = $this->cartRepository->get($quoteId);
            } catch (\Exception $e) {
            }
        }

        return $quote;
    }
}
