<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Variable\Model\Config\Structure\AvailableVariables">
        <arguments>
            <argument name="configPaths" xsi:type="array">
                <item name="general/store_information" xsi:type="array">
                    <item name="general/store_information/fax" xsi:type="string">1</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Model\Order\Email\Sender\OrderSender">
        <plugin name="biemans_english_order_email" type="Biemans\ThemeConfigurations\Plugin\OrderSenderEnLocale"/>
    </type>

    <!-- Set order/invoice/shipment/credit increment ID to 6 digits -->
    <type name="Magento\SalesSequence\Model\Sequence">
        <arguments>
            <argument name="pattern" xsi:type="string">%s%'.06d%s</argument>
        </arguments>
    </type>
</config>
