<?php

declare(strict_types=1);

namespace Biemans\ThemeConfigurations\Plugin;

use Biemans\ThemeConfigurations\ViewModel\BiemansWebsites;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Email\Sender\OrderSender;
use Magento\Store\Model\StoreManagerInterface;

/**
 * @todo
 * it is not working as designed. has side effect - customer is always redirected to the en website even if we set
 * current store to be one before.
 * may be we need to rewrite method and do not set order storeId
 */
class OrderSenderEnLocale
{
    private StoreManagerInterface $storeManager;
    private ?int $storeId = null;

    public function __construct(
        StoreManagerInterface $storeManager
    )
    {
        $this->storeManager = $storeManager;
    }

    /**
     * @return array<mixed>
     * @throws LocalizedException
     * @see OrderSender::send()
     *
     */
    public function beforeSend(OrderSender $subject, Order $order, bool $forceSyncMode = false): array
    {
        //we want this logic be only on the main website (temporary solution)
        if ($this->storeManager->getWebsite()->getCode() !== BiemansWebsites::MAIN_CODE) {
            return [$order, $forceSyncMode];
        }
        try {
            $this->storeId = (int)$order->getStoreId();
            $storeId = $this->storeManager->getStore('en')->getId();
            $order->setStoreId($storeId);
        } catch (\Exception $e) {
        }

        return [$order, $forceSyncMode];
    }

    /**
     * Reassign previous store to order after email was sent
     *
     * @see OrderSender::send()
     */
    public function afterSend(OrderSender $subject, bool $result, Order $order, bool $forceSyncMode = false): bool
    {
        try {
            if ($this->storeId) {
                $order->setStoreId($this->storeId);
                $this->storeManager->setCurrentStore($this->storeId);
            }
        } catch (\Exception $e) {
        }

        return $result;
    }
}
