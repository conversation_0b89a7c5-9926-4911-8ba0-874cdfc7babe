<?php

declare(strict_types=1);

namespace Biemans\ThemeConfigurations\Plugin\Controller\Cart;

use Magento\Checkout\Controller\Cart\Delete;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Message\ManagerInterface as MessageManagerInterface;
use Magento\Framework\UrlInterface;

class DeleteRedirect
{
    private UrlInterface $url;
    private MessageManagerInterface $messageManager;

    public function __construct(
        UrlInterface $url,
        MessageManagerInterface $messageManager
    ) {
        $this->url = $url;
        $this->messageManager = $messageManager;
    }

    /**
     * @see Delete::execute()
     *
     * @param Redirect $result
     * @return Redirect
     */
    public function afterExecute(Delete $subject, $result)
    {
        $hasErrors = false;
        $messages = $this->messageManager->getMessages()->getItems();
        foreach ($messages as $message) {
            if ($message->getType() === 'error') {
                $hasErrors = true;
                break;
            }
        }

        if (!$hasErrors) {
            $this->messageManager->addSuccessMessage(__('You removed the item.'));
        }

        return $result->setUrl($this->url->getUrl('checkout/cart'));
    }
}
