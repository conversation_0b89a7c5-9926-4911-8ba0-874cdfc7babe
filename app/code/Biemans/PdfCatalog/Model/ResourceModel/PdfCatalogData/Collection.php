<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalog\Model\ResourceModel\PdfCatalogData;

use <PERSON><PERSON>mans\PdfCatalog\Model\PdfCatalogData;
use <PERSON>iemans\PdfCatalog\Model\ResourceModel\PdfCatalogData as ResourceModel;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    protected function _construct()
    {
        $this->_init(PdfCatalogData::class, ResourceModel::class);
    }
}
