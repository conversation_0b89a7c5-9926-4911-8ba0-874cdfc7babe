<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalog\Model;

use <PERSON><PERSON>mans\PdfCatalog\Model\FileGenerationProcessor\DocumentCreatorInterface;
use <PERSON>iemans\PdfCatalog\Model\FileGenerationProcessor\IndexDataProvider;
use <PERSON><PERSON>mans\PdfCatalog\Model\FileGenerationProcessor\MetaDataFactory;
use Biemans\PdfCatalog\Services\CustomerGroups;
use Biemans\PdfCatalog\Services\SubTreeCategories;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilderFactory;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;
use Throwable;

/**
 * Class FileGenerationProcessor - mediator to handle logic for the pdf files generation.
 */
class FileGenerationProcessor
{
    public const SUPPORTED_CATALOG_TYPES = [
        'regular' => [
            'title' => "Biemans Catalog",
            'image' => "/MetaImages/biemans-catalog-front-2025.jpg",
        ],
        'b-special' => [
            'title' => "Biemans B-Special Catalog",
            'image' => "/MetaImages/biemans-catalog-front-2025.jpg",
        ],
        'outlet' => [
            'title' => "Biemans Outlet Catalog",
            'image' => "/MetaImages/biemans-catalog-front-2025.jpg",
        ],
        'new' => [
            'title' => "Biemans New Catalog",
            'image' => "/MetaImages/New-Biemans-2026.jpg",
            '3' => [
                'image' => "/MetaImages/New-Biemans-Polska-2026.jpg"
            ]
        ],
        'out-of-collection' => [
            'title' => "Biemans Out of Collection Catalog",
            'image' => "/MetaImages/OUT-Biemans-2026.jpg",
            '3' => [
                'image' => "/MetaImages/OUT-Biemans-Polska-2026.jpg"
            ]
        ]
    ];

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly DocumentCreatorInterface $documentCreator,
        private readonly IndexDataProvider $dataProvider,
        private readonly CustomerGroups $customerGroups,
        private readonly SubTreeCategories $subTreeCategories,
        private readonly MetaDataFactory $metaDataFactory,
        private readonly State $state,
        private readonly SearchCriteriaBuilderFactory $builderFactory,
        private readonly GroupRepositoryInterface $customerGroupRepository
    ) {
    }

    /**
     * Scenario:
     * 1. fetch data from index
     * 2. for each Customer Group and Catalog Type
     * 2.1 generate file
     * 2.2 remove old file
     * 2.3 save file
     *
     * @param string[] $customerGroups
     * @param string[] $catalogTypes
     * @param bool $withPrices
     * @return void
     * @throws LocalizedException
     * @throws Throwable
     */
    public function execute(array $customerGroups = [], array $catalogTypes = [], bool $withPrices = false): void
    {
        $this->logger->debug('Processing Files');
        try {
            $areaCode = $this->state->getAreaCode();
        } catch (\Throwable $exception) {
            $areaCode = null;
        }
        if (!$areaCode) {
            $this->state->setAreaCode(Area::AREA_ADMINHTML);
        }
        $catalogTypes = empty($catalogTypes) ? array_keys(self::SUPPORTED_CATALOG_TYPES) : $catalogTypes;
        $customerGroups = empty($customerGroups) ? $this->customerGroups->getAllCustomerGroupsCodes() : $customerGroups;
        $customerGroupsMap = $this->getCustomerGroupMap();
        $counter = 0;
        try {
            foreach ($customerGroups as $customerGroup) {
                $websiteId = $customerGroupsMap[$customerGroup] ?? 0;
                foreach ($catalogTypes as $catalogType) {
                    $fullData = $this->dataProvider->getData($customerGroup, $catalogType);
                    $this->logger->debug(sprintf(
                        'Found %s products in the index for %s Customer Group and %s Catalog',
                        count($fullData),
                        $customerGroup,
                        $catalogType
                    ));
                    if (empty($fullData)) {
                        continue;
                    }
                    $fullTree = $this->subTreeCategories->getCategoryTree();
                    $preparedTree = $this->prepareCatalogTree($fullData);
                    $metaData = $this->metaDataFactory->create();
                    $metaData->setFileNameToSave($this->buildFileName($customerGroup, $catalogType, $withPrices));
                    $metaData->setFirstPageImagePath(
                        self::SUPPORTED_CATALOG_TYPES[$catalogType][$websiteId]['image']
                            ?? self::SUPPORTED_CATALOG_TYPES[$catalogType]['image']
                    );
                    $metaData->setFirstPageTitle(self::SUPPORTED_CATALOG_TYPES[$catalogType]['title']);
                    $metaData->setCustomerGroup($customerGroup);
                    $this->logger->debug('Generating Catalog File.');
                    $this->documentCreator->buildDocument(
                        $fullTree,
                        $preparedTree,
                        $metaData,
                        $withPrices
                    );
                    $counter++;
                }
            }
            $this->logger->debug(sprintf('Prepared %s Files', $counter));
        } catch (Throwable $e) {
            $this->logger->critical('Error happened: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * logic to build name pattern for pdf files.
     */
    public function buildFileName(string $customerGroup, string $catalogType, bool $withPrices): string
    {
        $priceSuffix = $withPrices ? '_with_prices' : '_without_prices';
        return strtolower($catalogType) . '_' . strtolower($customerGroup) . $priceSuffix;
    }

    /**
     * we return tree of Categories of 1-3 lvls as  Sorted array of the product data
     * empty categories is returned empty
     * @param mixed[] $data
     * @return mixed[]
     * @throws NoSuchEntityException
     */
    public function prepareCatalogTree(array $data, ?int $categoryId = null): array
    {
        $subTree = $this->subTreeCategories->getCategoryTree();
        //we will have 3 levels of categories and need group products
        //by them so later render products in required order
        $orderedResult = [];
        foreach ($subTree as $mainNode) {
            foreach ($mainNode->getChildrenData() as $nodeLvlTwo) {
                $itemsLvl2 = [];
                foreach ($data as $key => $datum) {
                    $categories = explode(',', $datum['category_id'] ?? '');
                    if ($categoryId) {
                        // When generating the catalog for a single category we don't need other categories that might be assigned to product
                        $categories = [$categoryId];
                    }
                    if (in_array($nodeLvlTwo->getId(), $categories)) {
                        $itemsLvl2[] = $datum;
                    }
                }
                if(!empty($itemsLvl2)){
                    $orderedResult[$nodeLvlTwo->getId()]['category_header'] = $nodeLvlTwo->getName();
                    $orderedResult[$nodeLvlTwo->getId()]['items'] = $itemsLvl2;
                }
                foreach ($nodeLvlTwo->getChildrenData() as $nodeLvlThree) {
                    $itemsLvl3 = [];
                    foreach ($data as $key => $datum) {
                        $categories = explode(',', $datum['category_id'] ?? '');
                        if ($categoryId) {
                            // When generating the catalog for a single category we don't need other categories that might be assigned to product
                            $categories = [$categoryId];
                        }
                        if (in_array($nodeLvlThree->getId(), $categories)) {
                            $itemsLvl3[] = $datum;
                        }
                    }
                    if(!empty($itemsLvl3)){
                        $orderedResult[$nodeLvlThree->getId()]['category_header'] = $nodeLvlTwo->getName(). ' - '.
                            $nodeLvlThree->getName();
                        $orderedResult[$nodeLvlThree->getId()]['items'] = $itemsLvl3;
                    }
                }
            }
        }

        return array_filter($orderedResult);
    }

    /**
     * @return mixed[]
     * @throws LocalizedException
     */
    private function getCustomerGroupMap(): array
    {
        static $customerGroupMap = [];
        if (!empty($customerGroupMap)) {
            return $customerGroupMap;
        }

        $searchCriteria = $this->builderFactory->create()->create();
        $groups = $this->customerGroupRepository->getList($searchCriteria)->getItems();

        foreach ($groups as $group) {
            $websiteId = $group->getExtensionAttributes()?->getWebsiteId();
            /** @phpstan-ignore-next-line */
            $customerGroupMap[$group->getCode()] = $websiteId;
        }

        /** @phpstan-ignore-next-line */
        return $customerGroupMap;
    }
}
