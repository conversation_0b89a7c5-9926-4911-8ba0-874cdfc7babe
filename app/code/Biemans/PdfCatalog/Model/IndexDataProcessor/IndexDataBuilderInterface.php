<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalog\Model\IndexDataProcessor;

use <PERSON><PERSON>mans\PdfCatalog\Api\Data\PdfCatalogDataInterface;
use Magento\Catalog\Api\Data\ProductInterface;

/**
 * Interface represents Data Builder for specific data required in the Pdf Catalog Data Index
 */
interface IndexDataBuilderInterface
{
    /**
     * Method should implement logic to build specific data for the Pdf Catalog Index
     * @param ProductInterface $product
     * @param PdfCatalogDataInterface $pdfCatalogData
     * @return PdfCatalogDataInterface
     */
    public function build(ProductInterface $product, PdfCatalogDataInterface $pdfCatalogData): PdfCatalogDataInterface;

}
