<?php

declare(strict_types=1);

namespace <PERSON>iemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder;

use Magento\Framework\Exception\NoSuchEntityException;

trait ProductTrait
{
    /**
     * @throws NoSuchEntityException
     * @return bool|string|mixed[]|null
     */
    protected function getRawAttributeValue(int $productId, string $attributeCode)
    {
        /** @phpstan-ignore-next-line */
        return $this->productResource->getAttributeRawValue(
            $productId,
            $attributeCode,
            (int)$this->storeManager->getStore()->getStoreId() /** @phpstan-ignore-line */
        );
    }

    public function getProductIdBySku(string $sku): int
    {
        return (int)$this->productResource->getIdBySku($sku);
    }
}
