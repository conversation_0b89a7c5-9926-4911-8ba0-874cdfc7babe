<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder;

use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCollectionProductAttribute;
use <PERSON>iemans\PdfCatalog\Api\Data\PdfCatalogDataInterface;
use Biemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilderInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product as ProductResourceModel;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\GroupedProduct\Model\Product\Type\Grouped;
use Magento\Store\Model\StoreManagerInterface;

/**
 * There multiple possible types for the custom catalog
 * Product can be in different catalog types
 * Initially -
 * Biemans catalog - Just as the website is done but:
 * 1. without Outlet
 * 2. without New with a date in the future
 * 3. without B-special
 * 4. without Our of collection
 *  no problem if products are in two categories.
 */
class CatalogTypes implements IndexDataBuilderInterface
{
    use ProductTrait;

    private const BIEMANS_REGULAR_CATALOG_TYPE = 'regular';
    private const BIEMANS_NEW_CATALOG_TYPE = 'new';
    private const BIEMANS_OUT_OF_COLLECTION_CATALOG_TYPE = 'out-of-collection';
    private const BIEMANS_OUT_OF_COLLECTION_OPTION_VALUE = 'Out of collection';

    public function __construct(
        private readonly Grouped $groupedType,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly ProductResourceModel $productResource,
        private readonly StoreManagerInterface $storeManager
    ) {}

    /**
     * @inheritDoc
     */
    public function build(ProductInterface $product, PdfCatalogDataInterface $pdfCatalogData): PdfCatalogDataInterface
    {
        $result = [];
        //values from children
        if ($this->checkChildren($product, self::BIEMANS_NEW_CATALOG_TYPE)) {
            $result[] = self::BIEMANS_NEW_CATALOG_TYPE;
        }
        if ($this->checkChildren($product, self::BIEMANS_OUT_OF_COLLECTION_OPTION_VALUE)) {
            $result[] = self::BIEMANS_OUT_OF_COLLECTION_CATALOG_TYPE;
        }
        //value from main product
        $collections = $product->getCustomAttribute(AddCollectionProductAttribute::ATTRIBUTE_CODE)
            ?->getValue();
        if ($collections == null && empty($result)) {
            return $pdfCatalogData->setCatalogTypes(self::BIEMANS_REGULAR_CATALOG_TYPE);
        }
        foreach (explode(',', (string)$collections) as $optionId) {
            /* @phpstan-ignore-next-line */
            $attribute = $product->getResource()->getAttribute(AddCollectionProductAttribute::ATTRIBUTE_CODE);
            $value = $attribute->getSource()->getOptionText($optionId);
            $result[] = strtolower((string)$value);
        }

        return $pdfCatalogData->setCatalogTypes(implode(',', array_filter(array_unique($result))));
    }

    /**
     * We need to check if any of the simple products have New/Out-of-collection
     * Collection type. Grouped products does not have it.
     *
     * @param ProductInterface $product
     * @param string $optionValue
     * @return bool
     * @throws NoSuchEntityException
     */
    public function checkChildren(ProductInterface $product, string $optionValue): bool
    {
        /* @phpstan-ignore-next-line */
        $childrenIds = $this->groupedType->getChildrenIds((int)$product->getId());
        if (empty($childrenIds)) {
            return false;
        }

        /** @var \Magento\Catalog\Model\ResourceModel\Eav\Attribute|null $attribute */
        $attribute = $this->productAttributeRepository->get(AddCollectionProductAttribute::ATTRIBUTE_CODE);
        if (!$attribute) {
            return false;
        }

        foreach (reset($childrenIds) as $childrenId) {
            $collection = $this->getRawAttributeValue((int)$childrenId, AddCollectionProductAttribute::ATTRIBUTE_CODE);
            if (empty($collection)) {
                continue;
            }
            if (is_array($collection)) {
                $collection = implode(',', $collection);
            }
            foreach (explode(',', (string)$collection) as $optionId) {
                /* @phpstan-ignore-next-line */
                $value = (string)$attribute->getSource()->getOptionText($optionId);
                if (strtolower($value) === strtolower($optionValue)) {
                    return true;
                }
            }
        }

        return false;
    }
}
