<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalog\Model\IndexDataProcessor;

use Generator;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\GroupedProduct\Model\Product\Type\Grouped;
use Psr\Log\LoggerInterface;

/**
 * Class fetch products and send them to the product data processor.
 */
class ProductProvider
{
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Method returns generator that fetched all products that are:
     * - enabled
     * - visible
     * - grouped
     * If list of sku's provided - we get only that product.
     * @param string[] $skus
     * @return Generator
     */
    public function getProducts(array $skus): Generator
    {
        $this->logger->debug('Start of Product getting.');

        //building search criteria
        if (!empty($skus)) {
            $this->logger->debug('Sku\'s to fetch: ' . implode(',', $skus));
            $this->searchCriteriaBuilder->addFilter(ProductInterface::SKU, $skus, 'in');
        }
        $this->searchCriteriaBuilder->addFilter(ProductInterface::STATUS, Status::STATUS_ENABLED);
        $this->searchCriteriaBuilder->addFilter(ProductInterface::VISIBILITY,
            Visibility::VISIBILITY_NOT_VISIBLE, 'neq');
        $this->searchCriteriaBuilder->addFilter(ProductInterface::TYPE_ID, Grouped::TYPE_CODE);
        $searchCriteria = $this->searchCriteriaBuilder->create();

        //preparing batches for generator
        $productAmount = $this->productRepository->getList($searchCriteria)->getTotalCount();
        $this->logger->debug('Total amount of the products found: ' . $productAmount);
        foreach ($this->productRepository->getList($searchCriteria)->getItems() as $item) {
            yield $item;
        }
    }
}
