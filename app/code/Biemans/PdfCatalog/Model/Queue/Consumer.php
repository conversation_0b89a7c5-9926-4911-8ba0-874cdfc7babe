<?php

declare(strict_types=1);

namespace <PERSON>iemans\PdfCatalog\Model\Queue;

use <PERSON>iemans\PdfCatalog\Model\FileGenerationProcessor;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Log\LoggerInterface;
use Throwable;

class Consumer
{
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly FileGenerationProcessor $fileGenerationProcessor,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * method receives initial data for pdf catalog rendering
     * @param string $serializedData
     * @return void
     */
    public function process(string $serializedData): void
    {
        try {
            $pdfFileInfo = $this->serializer->unserialize($serializedData);
            /* @phpstan-ignore-next-line */
            $this->fileGenerationProcessor->execute([$pdfFileInfo['customerGroup']], [$pdfFileInfo['customCatalog']],
                /* @phpstan-ignore-next-line */
                (bool)$pdfFileInfo['withPrices']);

        } catch (Throwable $e) {
            $this->logger->critical(
                sprintf('Queue for file: %s . Error details: %s.', $serializedData, $e->getMessage())
            );
        }
    }
}
