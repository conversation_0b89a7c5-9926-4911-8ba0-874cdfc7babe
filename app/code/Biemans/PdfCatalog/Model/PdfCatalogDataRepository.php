<?php

declare(strict_types=1);

namespace Biemans\PdfCatalog\Model;

use <PERSON><PERSON>mans\PdfCatalog\Api\Data\PdfCatalogDataInterface;
use <PERSON><PERSON>mans\PdfCatalog\Api\Data\PdfCatalogDataInterfaceFactory;
use <PERSON><PERSON>mans\PdfCatalog\Api\Data\PdfCatalogDataSearchResultsInterface;
use Biemans\PdfCatalog\Api\Data\PdfCatalogDataSearchResultsInterfaceFactory;
use Biemans\PdfCatalog\Api\PdfCatalogDataRepositoryInterface;
use Biemans\PdfCatalog\Model\ResourceModel\PdfCatalogData as Resource;
use Biemans\PdfCatalog\Model\ResourceModel\PdfCatalogData\CollectionFactory;
use Exception;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Api\SearchCriteriaInterface;


class PdfCatalogDataRepository implements PdfCatalogDataRepositoryInterface
{
    public function __construct(
        private readonly Resource $resource,
        private readonly PdfCatalogDataInterfaceFactory $entityFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly PdfCatalogDataSearchResultsInterfaceFactory $searchResultsFactory,
        private readonly CollectionProcessorInterface $collectionProcessor
    ) {
    }

    /**
     * @param PdfCatalogDataInterface $pdfCatalogData
     * @return PdfCatalogDataInterface
     * @throws CouldNotSaveException
     */
    public function save(PdfCatalogDataInterface $pdfCatalogData): PdfCatalogDataInterface
    {
        try {
            /* @phpstan-ignore-next-line */
            $this->resource->save($pdfCatalogData);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save entity: %1',
                $exception->getMessage()
            ));
        }
        return $pdfCatalogData;
    }

    /**
     * @param $entityId
     * @return PdfCatalogDataInterface
     */
    public function getByEntityId($entityId): PdfCatalogDataInterface
    {
        $pdfCatalogData = $this->entityFactory->create();
        /* @phpstan-ignore-next-line */
        $this->resource->load($pdfCatalogData, $entityId);

        return $pdfCatalogData;
    }

    public function getList(SearchCriteriaInterface $criteria): PdfCatalogDataSearchResultsInterface
    {
        /* @phpstan-ignore-next-line */
        $collection = $this->collectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    public function delete(PdfCatalogDataInterface $pdfCatalogData): bool
    {
        try {
            /* @phpstan-ignore-next-line */
            $this->resource->delete($pdfCatalogData);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete entity: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }
}
