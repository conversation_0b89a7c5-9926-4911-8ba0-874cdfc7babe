<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PdfCatalog\Model;

use <PERSON><PERSON><PERSON>\PdfCatalog\Model\FileGenerationProcessor\DocumentCreatorInterface;
use <PERSON><PERSON>mans\PdfCatalog\Model\FileGenerationProcessor\IndexDataProvider;
use <PERSON><PERSON>mans\PdfCatalog\Model\FileGenerationProcessor\MetaDataFactory;
use Biemans\PdfCatalog\Services\SubTreeCategories;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Throwable;

/**
 * Class CategoryFileGenerationProcessor - mediator to handle logic for the pdf files generation
 * for specific category.
 */
class CategoryFileGenerationProcessor
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly DocumentCreatorInterface $documentCreator,
        private readonly IndexDataProvider $dataProvider,
        private readonly SubTreeCategories $subTreeCategories,
        private readonly MetaDataFactory $metaDataFactory,
        private readonly FileGenerationProcessor $fileGenerationProcessor,
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly State $state
    ) {}

    /**
     * Scenario:
     *
     * 1. fetch data from index
     * 2. use specified category and group
     * 2.1 generate file
     * 2.2 save file regular_[category_id]_[customer_group]
     * 2.3 return pass to the caller
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws Throwable
     */
    public function execute(string $customerGroup, int $categoryId): void
    {
        $this->logger->debug(sprintf('Processing File for Category %s', $categoryId));
        try {
            $areaCode = $this->state->getAreaCode();
        } catch (Throwable $exception) {
            $areaCode = null;
        }
        if (!$areaCode) {
            $this->state->setAreaCode(Area::AREA_ADMINHTML);
        }
        try {
            $fullData = $this->dataProvider->getCategoryData($customerGroup, $categoryId);
            $this->logger->debug(sprintf(
                'Found %s products in the index for %s Customer Group and %s Category Id',
                count($fullData),
                $customerGroup,
                $categoryId
            ));
            if (empty($fullData)) {
                throw new RuntimeException('No data found in the index for the category.');
            }
            $fullTree = $this->subTreeCategories->getCategoryTree();
            $preparedTree = $this->fileGenerationProcessor->prepareCatalogTree($fullData, (int)$categoryId);
            $metaData = $this->metaDataFactory->create();
            $metaData->setFileNameToSave($this->buildFileName($customerGroup, $categoryId));
            $metaData->setFirstPageImagePath("/MetaImages/biemans-catalog-front.jpg");
            $metaData->setFirstPageTitle("Biemans Catalog");
            $metaData->setCustomerGroup($customerGroup);
            $this->logger->debug('Generating Catalog File.');
            $this->documentCreator->buildDocument(
                $fullTree,
                $preparedTree,
                $metaData,
                false,
                false
            );

            $this->logger->debug(sprintf('Prepared File for Category %s', $categoryId));
        } catch (Throwable $e) {
            $this->logger->critical('Error happened: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * logic to build name pattern for pdf files with category.
     * @throws NoSuchEntityException
     */
    public function buildFileName(string $customerGroup, int $categoryId): string
    {
        $category = $this->categoryRepository->get($categoryId);

        return 'regular_' . strtolower($customerGroup) . '_' .
            strtolower(str_replace(' ', '_', $category->getName()));
    }
}
