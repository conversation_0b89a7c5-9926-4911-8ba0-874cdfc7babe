# <PERSON>iemans PdfCatalog

### Generation of the PDF Catalog

Logic implemented in 2 steps.
First index with data is generated by cron.
After that CRONs adds to queue all Customer Groups and Custom Catalog combinations.
Queue generates PDF catalogs per each Customer Group and Custom Catalog(Regular/New/B-Special/Outlet/Our of Stock).

### Features
1. Data index to prepare data for the pdf rendering.
    1. updates daily and contains info about the product
2. Pdf rendering logic that updates daily
3. CLI command to manually update pdf catalog index

> ```bin/magento biemans:pdfcatalog:index```

4. For specific list of products

> ```bin/magento biemans:pdfcatalog:index -s "G.D341,G.ET.341"```

5. <PERSON><PERSON><PERSON> command to manually generate pdf catalog files

> ```bin/magento biemans:pdfcatalog:files```

6. For specific list of Customer Group and Catalog Type with Prices

> ```bin/magento biemans:pdfcatalog:files -g "E-90" -c "regular" -p 1```

7. It is possible to download the PDF files from the Customer Account Homepage


### Troubleshooting

1. <PERSON><PERSON><PERSON> uses custom log file
2. File is located in var/log/pdf_catalog_generation.log

