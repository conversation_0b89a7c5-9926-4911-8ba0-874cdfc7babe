<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd"
>
    <table name="biemans_pdf_catalog_data">
        <column xsi:type="int" name="entity_id" nullable="false" identity="true" unsigned="true"/>
        <column xsi:type="varchar" name="sku" nullable="false" length="255" comment="Product SKU"/>
        <column xsi:type="mediumtext" name="group_prices" nullable="false"
                comment="Price for Product and it's children for all Customer Groups"/>
        <column xsi:type="mediumtext" name="product_data" nullable="false"
                comment="Aggregated information about the product"/>
        <column xsi:type="text" name="catalog_types" nullable="false"
                comment="List of pdf catalog types"/>
        <column xsi:type="varchar" name="category_id" nullable="false" length="255"
                comment="Categories for the product"/>
        <column xsi:type="datetime" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="BIEMANS_PDF_CATALOG_DATA_SKU" indexType="fulltext">
            <column name="sku" />
        </index>
    </table>
</schema>
