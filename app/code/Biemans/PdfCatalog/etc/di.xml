<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- start  preferences for the pdfcatalog entity-->
    <preference for="Biemans\PdfCatalog\Api\Data\PdfCatalogDataInterface"
                type="Biemans\PdfCatalog\Model\PdfCatalogData"/>
    <preference for="Biemans\PdfCatalog\Api\PdfCatalogDataRepositoryInterface"
                type="<PERSON>iemans\PdfCatalog\Model\PdfCatalogDataRepository"/>
    <preference for="Biemans\PdfCatalog\Api\Data\PdfCatalogDataSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>
    <preference for="Biemans\PdfCatalog\Model\FileGenerationProcessor\DocumentCreatorInterface"
                type="<PERSON><PERSON>mans\PdfCatalog\Model\FileGenerationProcessor\TcPdfCreator"/>
    <!-- end of preferences for the pdfcatalog entity-->

    <!-- start cli commands defining-->
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="biemans_generate_pdf_index" xsi:type="object">
                    Biemans\PdfCatalog\Console\Command\GeneratePdfData
                </item>
                <item name="biemans_generate_pdf_files" xsi:type="object">
                    Biemans\PdfCatalog\Console\Command\GeneratePdfFiles
                </item>
                <item name="biemans_update_collection_new" xsi:type="object">
                    Biemans\PdfCatalog\Console\Command\NewUpdate
                </item>
            </argument>
        </arguments>
    </type>
    <!-- end cli commands defining-->

    <!-- start pdf catalog custom file logger-->
    <virtualType name="Biemans\PdfCatalog\Log\Handler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/pdf_catalog_generation.log</argument>
        </arguments>
    </virtualType>

    <virtualType name="PdfCatalogLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Biemans\PdfCatalog\Log\Handler</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Biemans\PdfCatalog\Model\IndexDataProcessor\ProductProvider">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <type name="Biemans\PdfCatalog\Model\IndexDataProcessor">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <type name="Biemans\PdfCatalog\Model\IndexDataProcessor\PersistenceManager">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <type name="Biemans\PdfCatalog\Model\FileGenerationProcessor">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <type name="Biemans\PdfCatalog\Model\FileGenerationProcessor\TcPdfCreator">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <type name="Biemans\PdfCatalog\Model\Queue\Consumer">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <type name="Biemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder\ProductData">
        <arguments>
            <argument name="logger" xsi:type="object">PdfCatalogLogger</argument>
        </arguments>
    </type>
    <!-- end pdf index logger-->
    <!-- start of builder setup-->
    <type name="Biemans\PdfCatalog\Model\IndexDataProcessor">
        <arguments>
            <argument name="dataBuilders" xsi:type="array">
                <item name="category" xsi:type="object">
                    Biemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder\CategoryId
                </item>
                <item name="catalogTypes" xsi:type="object">
                    Biemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder\CatalogTypes
                </item>
                <item name="prices" xsi:type="object">
                    Biemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder\GroupPrices
                </item>
                <item name="productData" xsi:type="object">
                    Biemans\PdfCatalog\Model\IndexDataProcessor\IndexDataBuilder\ProductData
                </item>
            </argument>
        </arguments>
    </type>
    <!-- end of builder setup-->

    <!-- Admin grid -->
    <type name="Biemans\PdfCatalog\Model\ResourceModel\PdfCatalogData\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">biemans_pdf_catalog_data</argument>
            <argument
                name="resourceModel"
                xsi:type="string"
            >Biemans\PdfCatalog\Model\ResourceModel\PdfCatalogData\Collection</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item
                    name="biemans_pdf_catalog_listing_data_source"
                    xsi:type="string"
                >Biemans\PdfCatalog\Model\ResourceModel\PdfCatalogData\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
</config>
