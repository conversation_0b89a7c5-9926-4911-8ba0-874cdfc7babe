<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="biemans" translate="label" sortOrder="10010">
            <label><![CDATA[Biemans]]></label>
        </tab>
        <section id="biemans_pdf_catalog" type="text" translate="label" sortOrder="20" showInDefault="1"
                 showInWebsite="1" showInStore="1">
            <label>Pdf Catalog Auto Generation Settings</label>
            <tab>biemans</tab>
            <resource>Biemans_PdfCatalog::system_config</resource>
            <group id="general" type="text" translate="label" sortOrder="10" showInDefault="1" showInWebsite="0"
                   showInStore="0">
                <label>General</label>
                <field id="enable_queue" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <label>Enable Auto Generation</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="prices" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <label>Use prices</label>
                    <source_model>Biemans\PdfCatalog\Model\Config\Source\Prices</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
