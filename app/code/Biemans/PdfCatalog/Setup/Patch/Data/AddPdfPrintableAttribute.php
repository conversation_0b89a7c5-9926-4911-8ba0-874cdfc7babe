<?php

declare(strict_types=1);

namespace <PERSON>iemans\PdfCatalog\Setup\Patch\Data;

use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\Source\Boolean;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddPdfPrintableAttribute implements DataPatchInterface
{
    public const CATEGORY_PDF_ATTRIBUTE_CODE = 'pdf_printable';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {
    }

    public function apply(): DataPatchInterface {
        $this->moduleDataSetup->getConnection()->startSetup();
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Category::ENTITY,
            self::CATEGORY_PDF_ATTRIBUTE_CODE,
            [
                'type' => 'int',
                'label' => 'Enable Stand-alone Generation for Pdf Catalog',
                'input' => 'boolean',
                'source' => Boolean::class,
                'sort_order' => 100,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'visible' => true,
                'required' => false,
                'default' => 0,
                'group' => 'General Information'
            ]
        );
        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    public static function getDependencies():array {
        return [];
    }

    public function getAliases():array {
        return [];
    }
}
