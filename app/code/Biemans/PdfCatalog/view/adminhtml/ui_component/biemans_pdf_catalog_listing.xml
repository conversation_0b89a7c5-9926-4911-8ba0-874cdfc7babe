<?xml version="1.0" ?>
<listing
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
>
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item
                name="provider"
                xsi:type="string"
            >biemans_pdf_catalog_listing.biemans_pdf_catalog_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>biemans_pdf_catalog_columns</spinner>
        <deps>
            <dep>biemans_pdf_catalog_listing.biemans_pdf_catalog_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="biemans_pdf_catalog_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render" />
        </settings>
        <aclResource>Coutinho_BexRequest::Request</aclResource>
        <dataProvider
            name="biemans_pdf_catalog_listing_data_source"
            class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
        >
            <settings>
                <requestFieldName>entity_id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks" />
        <columnsControls name="columns_controls" />
        <exportButton name="export_button" />
        <filterSearch name="fulltext" />
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                </item>
            </argument>
        </filters>
        <paging name="listing_paging" />
    </listingToolbar>
    <columns name="biemans_pdf_catalog_columns">
        <column name="entity_id">
            <settings>
                <filter>textRange</filter>
                <sorting>desc</sorting>
                <label translate="true">Entity ID</label>
            </settings>
        </column>
        <column name="sku">
            <settings>
                <filter>text</filter>
                <label translate="true">SKU</label>
            </settings>
        </column>
        <column name="category_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Category ID</label>
                <visible>true</visible>
            </settings>
        </column>
        <column name="group_prices">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Biemans_PdfCatalog/js/grid/columns/details</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="contentField" xsi:type="string">group_prices</item>
                    <item name="has_preview" xsi:type="string">1</item>
                    <item name="align" xsi:type="string">center</item>
                    <item name="label" xsi:type="string" translate="true">Group Prices</item>
                </item>
            </argument>
        </column>
        <column name="product_data">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Biemans_PdfCatalog/js/grid/columns/details</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="contentField" xsi:type="string">product_data</item>
                    <item name="has_preview" xsi:type="string">1</item>
                    <item name="align" xsi:type="string">center</item>
                    <item name="label" xsi:type="string" translate="true">Product Data</item>
                </item>
            </argument>
        </column>
        <column name="catalog_types">
            <settings>
                <filter>text</filter>
                <label translate="true">Catalog Types</label>
            </settings>
        </column>
        <column
            name="created_at"
            class="Magento\Ui\Component\Listing\Columns\Date"
            component="Magento_Ui/js/grid/columns/date"
        >
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
            </settings>
        </column>
    </columns>
</listing>
