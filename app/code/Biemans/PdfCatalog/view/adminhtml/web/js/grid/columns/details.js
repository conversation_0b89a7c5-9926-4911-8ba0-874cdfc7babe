define([
    'Magento_Ui/js/grid/columns/column',
    'jquery',
    'mage/template',
    'text!Biemans_PdfCatalog/template/grid/cells/details/group-prices.html',
    'text!<PERSON>iemans_PdfCatalog/template/grid/cells/details/product-data.html',
    'underscore',
    'Magento_Ui/js/modal/modal',
    'mage/translate'
], function (Column, $, mageTemplate, groupPricesTemplate, productDataTemplate, _) {
    'use strict';

    return Column.extend({
        defaults: {
            bodyTmpl: 'Biemans_PdfCatalog/grid/cells/details',
            fieldClass: {
                'data-grid-details-cell': true
            }
        },

        /**
         * Get details data per row
         */
        getDetails: function (row) {
            return row[this.index] ? JSON.parse(row[this.index]) : '';
        },

        /**
         * Check if preview available
         */
        isPreviewAvailable: function () {
            return this['has_preview'] || false;
        },

        /**
         * Build modal preview
         */
        preview: function (row) {
            let self = this,
                modalHtml = '',
                title = $.mage.__('Details');

            switch(this.index) {
                case 'group_prices':
                    title = $.mage.__('Group Prices')
                    modalHtml = mageTemplate(groupPricesTemplate, {details: this.getDetails(row)});
                    break;
                case 'product_data':
                    title = $.mage.__('Product Data')
                    let details = this.getDetails(row);
                    if (details.specs) {
                        $.each(details.specs, function(i, item) {
                            details.specs[i] = self.sortObject(item);
                        });
                    }
                    modalHtml = mageTemplate(productDataTemplate, {details: details});
                    break;
            }

            let previewPopup = $('<div></div>').html(modalHtml);
            previewPopup.modal({
                title: title,
                innerScroll: true,
                modalClass: '_details-box',
                buttons: []
            }).trigger('openModal');
        },

        /**
         * Get field handler per row
         */
        getFieldHandler: function (row) {
            if (this.isPreviewAvailable()) {
                return this.preview.bind(this, row);
            }
        },

        sortObject: function(unordered, sortArrays = true) {
            if (!unordered || typeof unordered !== 'object') {
                return unordered;
            }

            if (Array.isArray(unordered)) {
                const newArr = unordered.map((item) => this.sortObject(item, sortArrays));
                if (sortArrays) {
                    newArr.sort();
                }
                return newArr;
            }

            const ordered = {};
            Object.keys(unordered)
                .sort()
                .forEach((key) => {
                    ordered[key] = unordered[key];
                });

            return ordered;
        }
    });
});
