<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PdfCatalog\ViewModel;

use <PERSON>iemans\PdfCatalog\Services\GeneratableCategoriesProvider;
use Exception;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Psr\Log\LoggerInterface;

class PdfCatalogCategory implements ArgumentInterface
{

    public function __construct(
        private readonly Session $customerSession,
        private readonly GroupRepositoryInterface $groupRepository,
        private readonly GeneratableCategoriesProvider $categoriesProvider,
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Method prepares categories list for form based on customer group. Next condition should be true for category
     * to go to the list:
     * 1. it should be enabled for stand-alone generation
     * 2. customer has products available in pdf index
     *
     * @return mixed[]
     */
    public function getCategoriesList(): array
    {
        $result = [];
        try {
            $customerGroup = $this->groupRepository->getById((int)$this->customerSession->getCustomerGroupId());
            $categoriesData = $this->categoriesProvider->getCategoriesList($customerGroup->getCode());
            if (empty($categoriesData)) {
                return $result;
            }
            foreach ($categoriesData as $key => $categoryDatum) {
                $categoryPath = explode('/', $categoryDatum['path']);
                $categoriesForPath = array_slice($categoryPath, 2);
                $categoryPathNames = [];
                foreach ($categoriesForPath as $item) {
                    $category = $this->categoryRepository->get((int)$item);
                    $categoryPathNames[] = $category->getName();
                }
                $result[] = ['id' => $categoryDatum['category_id'],
                    'label' => implode(' | ', $categoryPathNames)];
            }
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage());
        }
        usort($result, function($a, $b) {
            return strcmp($a['label'], $b['label']);
        });

        return $result;
    }
}
