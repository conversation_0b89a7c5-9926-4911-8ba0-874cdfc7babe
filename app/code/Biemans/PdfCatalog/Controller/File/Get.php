<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PdfCatalog\Controller\File;

use <PERSON><PERSON><PERSON>\PdfCatalog\Model\FileGenerationProcessor;
use <PERSON><PERSON><PERSON>\PdfCatalog\Model\FileGenerationProcessor\DocumentCreatorInterface;
use Exception;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\NotFoundException;
use Magento\Framework\Message\Manager;
use Psr\Log\LoggerInterface;


/**
 * Controller for the 'pdf/file/get' URL route.
 * Returns file to the Customer based on his CustomerGroup.
 */
class Get implements HttpGetActionInterface
{
    private const NOT_LOGGED_URL = 'customer/account/login';
    private const CATALOG_TYPE_PARAM = 'catalog_type';
    private const WITH_PRICES_PARAM = 'prices';
    private const MISSING_PARAM_MESSAGE = 'Allowed value for required parameter %s is missing.';

    public function __construct(
        private readonly FileFactory $fileFactory,
        private readonly DirectoryList $directoryList,
        private readonly Session $customerSession,
        private readonly ResultFactory $resultFactory,
        private readonly RequestInterface $request,
        private readonly FileGenerationProcessor $fileGenerationProcessor,
        private readonly GroupRepositoryInterface $groupRepository,
        private readonly Manager $messageManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Execute controller action.
     */
    public function execute()
    {
        if (!$this->customerSession->isLoggedIn()) {
            /** @phpstan-ignore-next-line */
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath(self::NOT_LOGGED_URL);
        }

        try {
            $catalogType = $this->request->getParam(self::CATALOG_TYPE_PARAM);
            if (empty($catalogType) || !in_array($catalogType, array_keys
                (FileGenerationProcessor::SUPPORTED_CATALOG_TYPES))) {
                throw new \RuntimeException(sprintf(self::MISSING_PARAM_MESSAGE,
                    self::CATALOG_TYPE_PARAM));
            }

            $prices = $this->request->getParam(self::WITH_PRICES_PARAM);
            if ($prices === null || !in_array($prices, ['0', '1'])) {
                throw new \RuntimeException(sprintf(self::MISSING_PARAM_MESSAGE,
                    self::WITH_PRICES_PARAM));
            }

            $customerGroup = $this->groupRepository->getById((int)$this->customerSession->getCustomerGroupId());
            $fileName = $this->fileGenerationProcessor->buildFileName($customerGroup->getCode(), $catalogType,
                (bool)$prices);
            $filePath = $this->directoryList->getPath(DirectoryList::VAR_DIR) . '/' .
                DocumentCreatorInterface::FOLDER_FOR_PDF . '/' . $fileName . '.pdf';
            if (!file_exists($filePath)) {
                throw new NotFoundException(__('The requested PDF file does not exist. File : '. $filePath));
            }

            return $this->fileFactory->create(
                $fileName.'.pdf',
                [
                    'type' => 'filename',
                    'value' => $filePath,
                ],
                DirectoryList::VAR_DIR,
                'application/pdf'
            );
        } catch (Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while serving the PDF file.'));
            $this->logger->error($e->getMessage());
            /** @phpstan-ignore-next-line */
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath('/');
        }
    }
}
