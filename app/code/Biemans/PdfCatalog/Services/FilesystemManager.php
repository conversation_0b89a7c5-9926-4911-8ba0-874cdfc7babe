<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalog\Services;

use <PERSON>iemans\PdfCatalog\Model\FileGenerationProcessor\MetaData;
use iio\libmergepdf\Merger;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;
use RuntimeException;
use TCPDF;

/**
 * Class FilesystemManager - service class used to manipulate with Filesystem during PDF files generation.
 */
class FilesystemManager
{

    public function __construct(
        private readonly Filesystem $filesystem,
        private readonly File $io
    ) {
    }

    /**
     * Method save files to var/pdf_catalog folder. so it can be used later.
     * @param int $chunkNum
     * @param MetaData $metaData
     * @param string $folderForPdf
     * @param TCPDF $libraryObject
     * @return void
     * @throws FileSystemException
     */
    public function saveFile(int $chunkNum, MetaData $metaData, string $folderForPdf, TCPDF $libraryObject): void
    {
        $fileName = $metaData->getFileNameToSave() . $chunkNum . '.pdf';
        if (!$this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)
            ->isDirectory(DirectoryList::MEDIA . '/' . $folderForPdf . '/' .
                $metaData->getFileNameToSave())
        ) {
            $this->io->mkdir(
                $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
                . $folderForPdf . '/' . $metaData->getFileNameToSave(), 0775);
        }
        $libraryObject->Output(
            $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . $folderForPdf . '/' . $metaData->getFileNameToSave() . '/' . $fileName, 'F');

    }

    /**
     * remove dir after merging file.
     * @param MetaData $metaData
     * @param string $folderForPdf
     * @return void
     * @throws FileSystemException
     */
    public function cleanChunkFolder(MetaData $metaData, string $folderForPdf): void
    {
        $this->io->rmdir($this->filesystem
                ->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . $folderForPdf . '/' . $metaData->getFileNameToSave(), true);
    }

    /**
     * merge pdf files
     * @param MetaData $metaData
     * @param string $folderForPdf
     * @return void
     * @throws FileSystemException
     */
    public function mergeFiles(MetaData $metaData, string $folderForPdf): void
    {
        /* @phpstan-ignore-next-line */
        $filesToMerge = array_diff(scandir($this->filesystem
                ->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . $folderForPdf . '/' . $metaData->getFileNameToSave()), ['.', '..']);
        if (!$filesToMerge) {
            throw new RuntimeException('No files found after pdf chunks creation.');
        }
        $fullPathFiles = [];
        foreach ($filesToMerge as $file) {
            $fullPathFiles[] = $this->filesystem
                    ->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
                . $folderForPdf . '/' . $metaData->getFileNameToSave() . '/' . $file;
        }
        natsort($fullPathFiles);

        $merger = new Merger;
        $merger->addIterator($fullPathFiles);
        file_put_contents($this
                ->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . $folderForPdf . '/' . $metaData->getFileNameToSave() . '.pdf', $merger->merge());
    }

    /**
     * to avoid server disk space issues we need to set custom folder for the tmp files and clean it each run.
     * @param string $folderForPdf
     * @param string $tmpFolderForPdf
     * @param string $globConstFolder
     * @return void
     * @throws FileSystemException
     */
    public function prepareCacheDirectory(string $folderForPdf, string $tmpFolderForPdf, string $globConstFolder): void
    {
        //if not exist - create
        if (!$this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)
            ->isDirectory(DirectoryList::VAR_DIR . '/' . $folderForPdf . '/' .
                $tmpFolderForPdf)
        ) {
            $this->io->mkdir(
                $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
                . $folderForPdf . '/' . $tmpFolderForPdf, 0775);
        }
        //clean it up if we have something there
        $this->io->rmdir($this->filesystem
                ->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . $folderForPdf . '/' . $tmpFolderForPdf . '/*', true);
        //set folder as CACHE folder
        $cacheDir = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath()
            . $folderForPdf . '/' . $tmpFolderForPdf . '/';
        if (!defined($globConstFolder)) {
            define($globConstFolder, $cacheDir);
        }
    }

    /**
     * Method removes all pdf files when index is generated. This way we will not have
     * @param string $folderForPdf
     * @return void
     * @throws FileSystemException
     */
    public function cleanAllPdf(string $folderForPdf): void
    {
        $files = glob($this->filesystem
                ->getDirectoryWrite(DirectoryList::VAR_DIR)->getAbsolutePath() . $folderForPdf . '/*.pdf');
        if($files === false){
            return;
        }
        foreach ($files as $file) {
            if ($this->io->fileExists($file)) {
                $this->io->rm($file);
            }
        }
    }

}
