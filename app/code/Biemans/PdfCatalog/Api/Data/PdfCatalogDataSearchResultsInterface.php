<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\PdfCatalog\Api\Data;

use Biemans\PdfCatalog\Api\Data\PdfCatalogDataInterface;
use Magento\Framework\Api\SearchResultsInterface;

interface PdfCatalogDataSearchResultsInterface extends SearchResultsInterface
{

    /**
     * @return PdfCatalogDataInterface[]
     */
    public function getItems(): array;

    /**
     * @param PdfCatalogDataInterface[] $items
     * @return PdfCatalogDataSearchResultsInterface
     */
    public function setItems(array $items): PdfCatalogDataSearchResultsInterface;
}
