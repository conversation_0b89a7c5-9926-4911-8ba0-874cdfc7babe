<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedMagepalGTM\Service;

use Exception;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Psr\Log\LoggerInterface;

class CurrencyProvider
{

    public function __construct(
        private readonly CustomerSession $customerSession,
        private readonly GroupRepositoryInterface $groupRepositoryInterface,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * since we have custom currency for the customers per customer group. we need this logic used.
     * @return string|null
     */
    public function getCurrentCustomerCurrency(): ?string
    {
        if (!$this->customerSession->isLoggedIn()) {
            return null;
        }

        try {
            return $this->groupRepositoryInterface
                ->getById((int)$this->customerSession->getCustomerGroupId())
                ->getExtensionAttributes()
                ->getCurrency();
        } catch (Exception $e) {
            $this->logger->error('[GTM:DATALAYER] Error for Currency Fetching for customer with Id - ' .
                $this->customerSession->getCustomerId());
        }

        return null;
    }

}
