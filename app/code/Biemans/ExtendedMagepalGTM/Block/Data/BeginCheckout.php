<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedMagepalGTM\Block\Data;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use MagePal\GoogleAnalytics4\Block\Data\Cart;
use MagePal\GoogleTagManager\Block\DataLayer;

class BeginCheckout extends Template
{
    public const GA4_BEGIN_CHECKOUT_PAGE = 'begin_checkout';

    /**
     * @param Context $context
     * @param Cart $cart
     * @param mixed[] $data
     */
    public function __construct(
        Context $context,
        private readonly Cart $cart,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Add product data to datalayer
     *
     * @return $this
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function _prepareLayout()
    {
        /** @var Template $tm */
        $tm = $this->getParentBlock();
        $data = [
            'event' => self::GA4_BEGIN_CHECKOUT_PAGE,
            'ecommerce' => [
                'currency' => $this->cart->getStoreCurrencyCode(),
                'items' =>  $this->cart->getCart(),
                'value' => $this->cart->getCartSubTotal()
            ]
        ];

        $tm->addVariable('list', 'checkout'); /** @phpstan-ignore-line */
        $tm->addCustomDataLayerByEvent(self::GA4_BEGIN_CHECKOUT_PAGE, $data); /** @phpstan-ignore-line */

        return $this;
    }
}
