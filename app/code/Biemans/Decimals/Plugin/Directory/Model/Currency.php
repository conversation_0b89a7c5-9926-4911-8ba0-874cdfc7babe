<?php

namespace <PERSON><PERSON>mans\Decimals\Plugin\Directory\Model;

use Biemans\Decimals\Model\Config;

class Currency
{
    private Config $config;

    public function __construct(
        Config $config
    ) {
        $this->config = $config;
    }

    /**
     * @param array<mixed> $options
     * @return array<mixed>
     */
    public function beforeFormatPrecision(
        \Magento\Directory\Model\Currency $subject,
        ?float $price,
        ?int $precision,
        array $options = [],
        bool $includeContainer = true,
        bool $addBrackets = false
    ): array {
        return [
            $price,
            $precision ?? $this->config->getPrecision(),
            $options,
            $includeContainer,
            $addBrackets
        ];
    }
}
