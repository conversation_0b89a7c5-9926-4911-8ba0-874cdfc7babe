<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="biemans" translate="label" sortOrder="10010">
            <label><![CDATA[Biemans]]></label>
        </tab>
        <section id="biemans_old_price" type="text" translate="label" sortOrder="20" showInDefault="1"
                 showInWebsite="1" showInStore="1">
            <label>Old Prices</label>
            <tab>biemans</tab>
            <resource>Biemans_OldPrice::system_config</resource>
            <group id="general" type="text" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>General</label>
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Show old prices for the products.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
