<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\OrderHub\Observer;

use Magento\Framework\Event\{Observer, ObserverInterface};

class CopyQuoteDataToOrder implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $observer->getOrder();
        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $observer->getQuote();

        if (!empty($orderHubId = $quote->getData('order_hub_id'))) {
            $order->setData('order_hub_id', $orderHubId);
        }
    }
}
