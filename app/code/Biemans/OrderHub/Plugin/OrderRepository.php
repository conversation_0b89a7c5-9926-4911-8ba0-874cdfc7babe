<?php

declare(strict_types=1);

namespace Biemans\OrderHub\Plugin;

use Magento\Sales\Api\Data\{OrderExtensionFactory, OrderExtensionInterface, OrderInterface};
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\Collection;
use Psr\Log\LoggerInterface;

class OrderRepository
{
    public function __construct(
        private readonly OrderExtensionFactory $extensionFactory,
        private readonly LoggerInterface $logger
    ) {}

    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $resultOrder
    ): OrderInterface {
        $orderExtensions = $resultOrder->getExtensionAttributes() ? $resultOrder->getExtensionAttributes() :
            $this->extensionFactory->create();
        $orderExtensions->setOrderHubId($resultOrder->getData('order_hub_id')); /** @phpstan-ignore-line */
        $resultOrder->setExtensionAttributes($orderExtensions);

        return $resultOrder;
    }

    /**
     * @return Collection<OrderInterface>
     */
    public function afterGetList(
        OrderRepositoryInterface $subject,
        Collection $resultOrder
    ) {
        /** @var OrderInterface $order */
        foreach ($resultOrder->getItems() as $order) {
            $this->afterGet($subject, $order);
        }

        return $resultOrder;
    }

    /**
     * @return OrderInterface[]
     */
    public function beforeSave(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ) {
        try {
            if (
                ($extensionAttributes = $order->getExtensionAttributes())
                && ($hubId = $extensionAttributes->getOrderHubId())
            ) {
                $order->setOrderHubId($hubId); /** @phpstan-ignore-line */
            }
        } catch (\Exception $exception) {
            $this->logger->critical($exception->getMessage());
        }

        return [$order];
    }
}
