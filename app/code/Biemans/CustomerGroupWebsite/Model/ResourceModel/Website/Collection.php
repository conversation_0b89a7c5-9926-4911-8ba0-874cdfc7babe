<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\CustomerGroupWebsite\Model\ResourceModel\Website;

use Biemans\CustomerGroupWebsite\Model\ResourceModel\Website as WebsiteResourceModel;
use B<PERSON>mans\CustomerGroupWebsite\Model\Website;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'entity_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            Website::class,
            WebsiteResourceModel::class
        );
    }
}
