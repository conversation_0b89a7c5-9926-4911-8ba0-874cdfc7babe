<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerGroupWebsite\Model;

use <PERSON>iemans\CustomerGroupWebsite\Api\Data\WebsiteInterface;
use Magento\Framework\Model\AbstractModel;

class Website extends AbstractModel implements WebsiteInterface
{

    public function _construct()
    {
        $this->_init(ResourceModel\Website::class);
    }

    public function getCustomerGroupId(): ?int
    {
        return $this->getData(self::CUSTOMER_GROUP_ID);
    }

    public function setCustomerGroupId(int $customerGroupId): WebsiteInterface
    {
        return $this->setData(self::CUSTOMER_GROUP_ID, $customerGroupId);
    }

    public function getWebsiteId(): ?int
    {
        return !is_null($this->getData(self::WEBSITE_ID)) ? (int)$this->getData(self::WEBSITE_ID) : null;
    }

    public function setWebsiteId(int $websiteId): WebsiteInterface
    {
        return $this->setData(self::WEBSITE_ID, $websiteId);
    }

    public function getEntityId(): ?int
    {
        return $this->getData(self::ENTITY_ID);
    }

    public function setEntityId($entityId): WebsiteInterface
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }
}
