<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerGroupWebsite\Plugin;

use <PERSON>iemans\CustomerGroupWebsite\Api\Data\WebsiteInterface;
use <PERSON><PERSON>mans\CustomerGroupWebsite\Api\Data\WebsiteInterfaceFactory;
use <PERSON><PERSON>mans\CustomerGroupWebsite\Api\WebsiteRepositoryInterface;
use Magento\Customer\Api\Data\GroupInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilderFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;

class AddWebsiteRepo
{

    public function __construct(
        private readonly RequestInterface $request,
        private readonly SearchCriteriaBuilderFactory $searchCriteriaBuilderFactory,
        private readonly WebsiteRepositoryInterface $websiteRepository,
        private readonly WebsiteInterfaceFactory $websiteFactory
    ) {}

    /**
     * @param GroupRepositoryInterface $subject
     * @param GroupInterface $result
     * @param GroupInterface $entity
     * @return GroupInterface
     * @throws LocalizedException
     */
    public function afterSave(
        GroupRepositoryInterface $subject, GroupInterface $result, GroupInterface $entity
    ): GroupInterface
    {
        $extensionAttributes = $entity->getExtensionAttributes();

        if ($extensionAttributes && $result->getId() > 0) {

            // Prevent losing currency when saving from admin panel
            $groupData = $this->request->getParams();
            if ($groupData && isset($groupData['website_id'])) {
                $extensionAttributes->setWebsiteId($groupData['website_id']);
            }

            // Search currency entry for current group
            $searchCriteria = $this->searchCriteriaBuilderFactory->create()
                ->addFilter('customer_group_id', $result->getId())
                ->create();

            $preferredWebsites = $this->websiteRepository->getList($searchCriteria)->getItems();
            $preferredWebsite = current($preferredWebsites);

            if ($preferredWebsite instanceof WebsiteInterface) {
                $preferredWebsite->setWebsiteId((int)$extensionAttributes->getWebsiteId());
            } else {
                $preferredWebsite = $this->websiteFactory->create();
                $preferredWebsite->setWebsiteId((int)$extensionAttributes->getWebsiteId())
                    ->setCustomerGroupId((int)$result->getId());
            }

            $this->websiteRepository->save($preferredWebsite);
        }

        return $result;
    }

    /**
     * @throws LocalizedException
     * @phpstan-ignore-next-line
     */
    public function afterGetList(GroupRepositoryInterface $subject, $result)
    {
        foreach ($result->getItems() as $item) {
            $this->afterGetById($subject, $item, $item->getId());
        }

        return $result;
    }

    /**
     * @throws LocalizedException
     * @phpstan-ignore-next-line
     */
    public function afterGetById(
        GroupRepositoryInterface $subject,
        GroupInterface $result,
        $customerGroupId
    ): GroupInterface
    {
        if ($customerGroupId > 0) {

            $extensionAttributes = $result->getExtensionAttributes();

            $searchCriteria = $this->searchCriteriaBuilderFactory->create()
                ->addFilter('customer_group_id', $customerGroupId)
                ->create();

            $preferredWebsites = $this->websiteRepository->getList($searchCriteria)->getItems();
            $preferredWebsite = current($preferredWebsites);

            if ($preferredWebsite instanceof WebsiteInterface) {
                $extensionAttributes->setWebsiteId($preferredWebsite->getWebsiteId());
                $result->setExtensionAttributes($extensionAttributes);
            }
        }

        return $result;
    }
}
