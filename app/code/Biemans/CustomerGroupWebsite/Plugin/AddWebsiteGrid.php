<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupWebsite\Plugin;

use Magento\Customer\Model\ResourceModel\Group\Grid\Collection as GroupCollection;
use Zend_Db_Select;

class AddWebsiteGrid
{
    /* @phpstan-ignore-next-line */
    public function aroundGetSelect(GroupCollection $subject, callable $proceed)
    {
        $select = $proceed();

        $tables = $select->getPart(Zend_Db_Select::FROM);

        // Check if store_website is not already in the query
        if (!isset($tables['preffered_website'])) {
            $select->joinLeft( /** @phpstan-ignore-line */
                ['biemans_customer_group_website' => 'biemans_customer_group_website'],
                "main_table.customer_group_id = biemans_customer_group_website.customer_group_id",
                "website_id"
            );
            $select->joinLeft(
                ['preffered_website' => $subject->getTable('store_website')],
                "preffered_website.website_id = biemans_customer_group_website.website_id",
                ['preferred_website_name' => "preffered_website.name"]
            );
        }

        return $select;
    }
}
