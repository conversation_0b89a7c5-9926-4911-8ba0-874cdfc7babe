<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupWebsite\Plugin;

use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Customer\Block\Adminhtml\Group\Edit\Form;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\System\Store as SystemStore;
use Magento\Customer\Api\Data\GroupInterfaceFactory;

class AddPreferredWebsiteFormField
{

    public function __construct(
        private readonly RequestInterface $request,
        private readonly SystemStore $systemStore,
        private readonly GroupRepositoryInterface $groupRepository,
        private readonly GroupInterfaceFactory $groupDataFactory,
    ) {}

    public function afterSetLayout(Form $subject, Form $result): Form
    {
        $groupId = (int)$this->request->getParam('id');
        $customerGroup = $this->groupDataFactory->create();
        if ($groupId) {
            $customerGroup = $this->groupRepository->getById($groupId);
        }
        $extensionAttributes = $customerGroup->getExtensionAttributes();

        /** @var \Magento\Framework\Data\Form $form */
        $form = $result->getForm();
        $fieldset = $form->getElement('base_fieldset');

        $fieldset->addField(
            'website_id',
            'select',
            [
                'name' => 'website_id',
                'label' => __('Preferred Website PDF Catalogs'),
                'title' => __('Preferred Website PDF Catalogs'),
                'can_be_empty' => true,
                'values' => $this->systemStore->getWebsiteValuesForForm(),
                'value' => $extensionAttributes->getWebsiteId(),
                'note' => __('Select websites you want to be preferred in PDF catalogs for this customer group.')
            ]
        );

        return $result;
    }
}
