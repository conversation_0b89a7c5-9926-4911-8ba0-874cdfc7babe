<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="biemans_customer_group_website" resource="default" engine="innodb" comment="Customer Group Website Table">
        <column xsi:type="int" name="entity_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
        <column name="website_id" nullable="true" xsi:type="int" comment="Website ID"  identity="false"/>
        <column name="customer_group_id" nullable="true" xsi:type="int" comment="Customer Group ID" identity="false"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
    </table>
</schema>
