<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:DependencyInjection/etc/di.xsd">
    <preference for="Biemans\CustomerGroupWebsite\Api\WebsiteRepositoryInterface"
                type="Biemans\CustomerGroupWebsite\Model\WebsiteRepository"/>
    <preference for="Biemans\CustomerGroupWebsite\Api\Data\WebsiteInterface"
                type="Biemans\CustomerGroupWebsite\Model\Website"/>
    <preference for="Biemans\CustomerGroupWebsite\Api\Data\WebsiteSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>
    <type name="Magento\Customer\Api\GroupRepositoryInterface">
        <plugin name="add_website_id_extension_attribute" type="Biemans\CustomerGroupWebsite\Plugin\AddWebsiteRepo"/>
    </type>

    <type name="Magento\Customer\Model\ResourceModel\Group\Grid\Collection">
        <plugin name="add_website_name_to_grid" type="Biemans\CustomerGroupWebsite\Plugin\AddWebsiteGrid"/>
    </type>
</config>
