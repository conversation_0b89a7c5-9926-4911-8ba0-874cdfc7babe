<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupWebsite\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

interface WebsiteSearchResultsInterface extends SearchResultsInterface
{

    /**
     * Get Customer Group Website list.
     * @return WebsiteInterface[]
     */
    public function getItems(): array;

    /**
     * Set Customer Group Website list.
     * @param WebsiteInterface[] $items
     * @return $this
     */
    public function setItems(array $items): WebsiteSearchResultsInterface;
}
