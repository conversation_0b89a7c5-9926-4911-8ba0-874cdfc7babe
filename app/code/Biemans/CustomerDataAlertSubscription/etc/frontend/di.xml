<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\CustomerData\SectionPoolInterface">
        <arguments>
            <argument name="sectionSourceMap" xsi:type="array">
                <item name="stock_alert" xsi:type="string">Biemans\CustomerDataAlertSubscription\CustomerData\StockAlert</item>
            </argument>
        </arguments>
    </type>
</config>
