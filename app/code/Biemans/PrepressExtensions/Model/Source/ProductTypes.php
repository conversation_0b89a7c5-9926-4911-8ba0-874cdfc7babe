<?php

declare(strict_types=1);

namespace Biemans\PrepressExtensions\Model\Source;

use <PERSON>iemans\Catalog\Setup\Patch\Data\AddProductTypeAttribute;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;

class ProductTypes implements \Magento\Framework\Data\OptionSourceInterface
{
    private ProductAttributeRepositoryInterface $productAttributeRepository;

    public function __construct(
        ProductAttributeRepositoryInterface $productAttributeRepository
    ) {
        $this->productAttributeRepository = $productAttributeRepository;
    }

    /**
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\InputException
     */
    public function toOptionArray(): array
    {
        $types = $this->productAttributeRepository->get(AddProductTypeAttribute::ATTRIBUTE_CODE)->getOptions();
        $options = [];
        foreach ($types as $type) {
            if ($type->getValue()) {
                $options[] = [
                    'value' => $type->getValue(),
                    'label' => $type->getLabel()
                ];
            }
        }

        return $options;
    }

    /**
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\InputException
     */
    public function toArray(): array
    {
        $options = $this->toOptionArray();
        $array = [];
        foreach ($options as $option) {
            $array[$option['value']] = $option['label'];
        }

        return $array;
    }
}
