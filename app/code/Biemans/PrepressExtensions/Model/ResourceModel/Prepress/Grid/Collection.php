<?php

declare(strict_types=1);

namespace Biemans\PrepressExtensions\Model\ResourceModel\Prepress\Grid;

use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;

class Collection extends SearchResult
{
    public function load($printQuery = false, $logQuery = false)
    {
        if (!$this->isLoaded()) {
            /** @var \Magento\Framework\Model\ResourceModel\Db\AbstractDb $resourceConnection */
            $resourceConnection = $this->getResource();
            $orderItemTable = $resourceConnection->getTable('sales_order_item');
            $orderGridTable = $resourceConnection->getTable('sales_order_grid');

            $this->getSelect()->joinLeft(
                $orderItemTable,
                $orderItemTable . '.`item_id` = main_table.item_id',
                [
                    'order_id',
                    'product_sku' => 'sku',
                    'product_name' => 'name',
                    'product_options' => 'product_options'
                ]
            )->joinLeft(
                $orderGridTable,
                $orderGridTable . '.`entity_id` = order_id',
                [
                    'increment_id',
                    'customer_name' => 'billing_name',
                    'debtor_number' => 'debtor_number',
                    'company' => 'company',
                    'order_reference' => 'order_reference',
                    'order_hub_id' => 'order_hub_id'
                ]
            );
        }

        return parent::load($printQuery, $logQuery);
    }

    public function getSelect()
    {
        static $filterMapAdded = null;

        if (!$filterMapAdded) {
            /** @var \Magento\Framework\Model\ResourceModel\Db\AbstractDb $resourceConnection */
            $resourceConnection = $this->getResource();
            $orderItemTable = $resourceConnection->getTable('sales_order_item');
            $orderGridTable = $resourceConnection->getTable('sales_order_grid');

            $this->addFilterToMap(
                'product_sku',
                $orderItemTable . '.sku'
            );

            $this->addFilterToMap(
                'product_name',
                $orderItemTable . '.name'
            );

            $this->addFilterToMap(
                'customer_name',
                $orderGridTable . '.billing_name'
            );

            $this->addFilterToMap(
                'debtor_number',
                $orderGridTable . '.debtor_number'
            );

            $this->addFilterToMap(
                'company',
                $orderGridTable . '.company'
            );

            $this->addFilterToMap(
                'status',
                'main_table.status'
            );

            $this->addFilterToMap(
                'personalisation_technique',
                'main_table.status' // or any real column just to bypass the error
            );

            $filterMapAdded = true;
        }

        return parent::getSelect();
    }
}
