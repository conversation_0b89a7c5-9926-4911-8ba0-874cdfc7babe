<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
	<type name="Magento\Framework\App\Http\Context">
        <plugin
            name="biemans_exclude_prepress_customer_cache_context_plugin"
            type="Biemans\PrepressExtensions\Plugin\ExcludePrepressCacheContextPlugin"
        />
	</type>
    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Grid" type="Magento\Sales\Model\ResourceModel\Grid">
        <arguments>
            <argument name="columns" xsi:type="array">
                <item name="company" xsi:type="string">sales_billing_address.company</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="biemans_prepress_prepress_listing_data_source" xsi:type="string">
                    Biemans\PrepressExtensions\Model\ResourceModel\Prepress\Grid\Collection
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Biemans\PrepressExtensions\Model\ResourceModel\Prepress\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">biemans_prepress_prepress</argument>
            <argument name="resourceModel" xsi:type="string">
                Biemans\Prepress\Model\ResourceModel\Prepress\Collection
            </argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Helper\Category">
        <plugin
            name="biemans_prevent_category_access_if_prevent_personalization"
            type="Biemans\PrepressExtensions\Plugin\PreventCategoryAccessPlugin"
        />
    </type>
    <type name="Magento\Catalog\Model\ResourceModel\Category">
        <plugin
            name="biemans_add_prevent_personalization_in_category_children_collection"
            type="Biemans\PrepressExtensions\Plugin\AddPreventPersonalizationToCollection"
        />
    </type>
</config>
