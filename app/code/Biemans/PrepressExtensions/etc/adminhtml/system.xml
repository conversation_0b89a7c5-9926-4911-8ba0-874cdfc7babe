<?xml version="1.0" ?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd"
>
    <system>
        <section id="biemans_prepress">
            <group
                id="exclude_products"
                type="text"
                translate="label"
                sortOrder="30"
                showInDefault="1"
                showInWebsite="1"
                showInStore="1"
            >
                <label>Exclude Products</label>
                <field
                    id="types"
                    translate="label"
                    type="multiselect"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                >
                    <label>Product Types</label>
                    <source_model>Biemans\PrepressExtensions\Model\Source\ProductTypes</source_model>
                    <comment>This will work in combination with customer attribute "Prevent user from personalizing specific items" > YES.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
