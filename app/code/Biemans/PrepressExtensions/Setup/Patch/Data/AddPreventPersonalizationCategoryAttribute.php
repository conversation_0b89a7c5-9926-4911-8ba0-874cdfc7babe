<?php

declare(strict_types=1);

namespace Biemans\PrepressExtensions\Setup\Patch\Data;

use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Model\Entity\Attribute\Source\Boolean;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddPreventPersonalizationCategoryAttribute implements DataPatchInterface
{
    const ATTRIBUTE_CODE = 'prevent_personalization';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Category::ENTITY,
            self::ATTRIBUTE_CODE,
            [
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'General',
                'input' => 'select',
                'label' => 'Prevent personalizing',
                'required' => false,
                'sort_order' => 50,
                'source' => Boolean::class,
                'type' => 'int'
            ]
        );

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    public function getAliases()
    {
        return [];
    }

    public static function getDependencies()
    {
        return [];
    }
}
