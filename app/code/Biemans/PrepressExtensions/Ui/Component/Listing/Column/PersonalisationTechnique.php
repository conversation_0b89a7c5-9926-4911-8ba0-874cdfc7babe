<?php

namespace <PERSON>iemans\PrepressExtensions\Ui\Component\Listing\Column;

use Magento\Ui\Component\Listing\Columns\Column;

class PersonalisationTechnique extends Column
{
    /**
     * @param mixed[] $dataSource
     * @return mixed[]
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $technique = '';
                if (!empty($item['product_options'])) {
                    $options = @json_decode($item['product_options'], true);

                    if (is_array($options) && isset($options['options'])) {
                        foreach ($options['options'] as $option) {
                            if (isset($option['personalisation_technique'])) {
                                $technique = $option['personalisation_technique']. ' (SKU: '. $option['option_sku'].')';
                                break;
                            }
                        }
                    }
                }

                $item['personalisation_technique'] = $technique;
            }
        }

        return $dataSource;
    }
}
