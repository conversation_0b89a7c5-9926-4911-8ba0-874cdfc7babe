<?php

declare(strict_types=1);

namespace <PERSON>ie<PERSON>\PrepressExtensions\Plugin;

use <PERSON><PERSON><PERSON>\PrepressExtensions\Model\Context as ExcludePrepressContext;
use <PERSON><PERSON><PERSON>\PrepressExtensions\Setup\Patch\Data\AddExcludePrepressCustomerAttribute;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\Api\AttributeInterface;
use Magento\Framework\App\Http\Context as HttpContext;

class ExcludePrepressCacheContextPlugin
{
    private Session $customerSession;

    public function __construct(Session $customerSession)
    {
        $this->customerSession = $customerSession;
    }

    /**
     * Add Customer custom_catalog cache context for caching purposes
     *
     * @param HttpContext $subject
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeGetVaryString(HttpContext $subject)
    {
        $customerData = $this->customerSession->getCustomerData();

        if (!($customerData instanceof CustomerInterface)) {
            $subject->setValue(
                ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS,
                ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS_DEFAULT_VALUE,
                ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS_DEFAULT_VALUE
            );

            return [];
        }

        $excludePrePressAttribute = $customerData->getCustomAttribute(
            AddExcludePrepressCustomerAttribute::ATTRIBUTE_CODE
        );

        if (!($excludePrePressAttribute instanceof AttributeInterface)) {
            $subject->setValue(
                ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS,
                ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS_DEFAULT_VALUE,
                ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS_DEFAULT_VALUE
            );

            return [];
        }

        $excludePrePress = $excludePrePressAttribute->getValue();

        $subject->setValue(
            ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS,
            $excludePrePress,
            ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS_DEFAULT_VALUE
        );

        return [];
    }
}
