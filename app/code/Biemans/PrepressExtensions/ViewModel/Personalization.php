<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PrepressExtensions\ViewModel;

use <PERSON><PERSON>mans\PrepressExtensions\Model\Context as ExcludePrepressContext;
use Magento\Customer\Model\Context as CustomerContext;
use Magento\Framework\App\Http\Context as HttpContext;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Personalization implements ArgumentInterface
{
    public function __construct(
        private readonly HttpContext $httpContext,
    ) {}

    public function isCustomerExcluded(): bool
    {
        // Call this to initialize pre-press context data
        $this->httpContext->getVaryString();

        // Treat guests as customers with excluded privileges
        return (bool)$this->httpContext->getValue(ExcludePrepressContext::CONTEXT_EXCLUDE_PRE_PRESS)
            || ($this->httpContext->getValue(CustomerContext::CONTEXT_GROUP) === 0);
    }
}
