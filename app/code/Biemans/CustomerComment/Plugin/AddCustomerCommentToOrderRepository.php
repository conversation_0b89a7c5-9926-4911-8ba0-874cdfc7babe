<?php

declare(strict_types=1);

namespace Biemans\CustomerComment\Plugin;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderStatusHistoryInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\Collection;

class AddCustomerCommentToOrderRepository
{
    private OrderExtensionFactory $extensionFactory;

    public function __construct(
        OrderExtensionFactory $extensionFactory
    ) {
        $this->extensionFactory = $extensionFactory;
    }

    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $resultOrder
    ): OrderInterface {
        $orderExtensions = $resultOrder->getExtensionAttributes() ?: $this->extensionFactory->create();
            $this->extensionFactory->create();

        $histories = $resultOrder->getStatusHistories();
        if ($histories !== null) {
            foreach ($histories as $history) {
                if (
                    !empty($history) /** @phpstan-ignore-line */
                    && !empty($history->getComment())
                    && (
                        strpos($history->getComment(), 'Klant:') !== false
                        || strpos($history->getComment(), 'Customer:') !==false
                        || strpos($history->getComment(), 'Kunde:') !==false
                        || strpos($history->getComment(), 'Client :') !== false
                    )
                ) {
                    $orderExtensions->setCustomerComment($history->getComment()); /** @phpstan-ignore-line */
                }
             }

        }

        $resultOrder->setExtensionAttributes($orderExtensions);

        return $resultOrder;
    }

    /**
     * @return Collection<OrderInterface>
     */
    public function afterGetList(
        OrderRepositoryInterface $subject,
        Collection $resultOrder
    ) {
        /** @var OrderInterface $order */
        foreach ($resultOrder->getItems() as $order) {
            $this->afterGet($subject, $order);
        }
        return $resultOrder;
    }
}
