<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedCart\Plugin;

use Magento\Checkout\Block\Cart\Grid;
use Magento\Framework\DB\Select;
use Magento\Quote\Model\Quote\Item;

class FixPager
{
    const POSITION_MULTIPLIER = 10000000;

    /**
     * @return Item[]
     */
    public function aroundGetItems(Grid $subject, callable $proceed)
    {
        /** @var  \Magento\Theme\Block\Html\Pager|false $pager */
        $pager = $subject->getChildBlock('pager');

        if ($pager) {
            /** @var \Magento\Quote\Model\ResourceModel\Quote\Item\Collection $collection */
            $collection = $pager->getCollection();

            $collection->clear();
            $collection
                ->getSelect()
                ->reset(Select::LIMIT_COUNT)
                ->reset(Select::LIMIT_OFFSET);
            /** @phpstan-ignore-next-line */
            $collection->setPageSize(false);
            $collection->load();
        }

        $result = $proceed();

        if ($pager) {
            $collection = $pager->getCollection();
            $pageSize = $pager->getLimit();

            $collection->setPageSize($pageSize);

            // Fetching current page before setting page size will always return 1
            $currentPage = $collection->getCurPage();

            // Sort array by positions
            $sortedItems = [];
            foreach ($result as $item) {
                $sortedItems[(int)$item->getPosition() * self::POSITION_MULTIPLIER + (int)$item->getId()] = $item;
            }
            ksort($sortedItems);

            $result = array_slice($sortedItems, ($currentPage - 1) * $pageSize, $pageSize);

            // Update items in collection to show the right amounts in toolbar
            $collection->removeAllItems();

            foreach ($result as $item) {
                $collection->addItem($item);
            }
        }

        return $result;
    }
}
