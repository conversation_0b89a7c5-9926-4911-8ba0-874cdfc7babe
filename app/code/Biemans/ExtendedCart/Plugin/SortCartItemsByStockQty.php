<?php

declare(strict_types=1);

namespace Biemans\ExtendedCart\Plugin;

use Magento\Checkout\Block\Cart\Grid;
use Magento\Framework\App\ResourceConnection;
use Magento\Quote\Model\{
    Quote,
    ResourceModel\Quote\Item\Collection
};
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class SortCartItemsByStockQty
{
    private ResourceConnection $resourceConnection;
    private StoreManagerInterface $storeManager;
    private LoggerInterface $logger;

    public function __construct(
        ResourceConnection $resourceConnection,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger
    ) {
        $this->resourceConnection = $resourceConnection;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
    }

    /**
     * @see Quote::getItemsCollection()
     */
    public function afterGetItemsCollection(Quote $subject, Collection $items): Collection
    {
        static $joined = false;

        if (!$joined) {
            $items = $this->sortQuoteItemsByStockQty($subject, $items);
            $joined = true;
        }

        return $items;
    }

    /**
     * @see Grid::getItemsForGrid()
     */
    public function afterGetItemsForGrid(Grid $subject, Collection $items): Collection
    {
        static $joinedForGrid = false;

        if (!$joinedForGrid) {
            $items = $this->sortQuoteItemsByStockQty($subject->getQuote(), $items);
            $joinedForGrid = true;
        }

        return $items;
    }

    private function sortQuoteItemsByStockQty(Quote $quote, Collection $items): Collection
    {
        try {
            if (
                ($websiteId = $this->getQuoteWebsiteId($quote))
                && ($stockTable = $this->resourceConnection->getTableName('cataloginventory_stock_item'))
            ) {
                $items->getSelect()
                    ->joinLeft(
                        ['stock_table' => $stockTable],
                        '`stock_table`.`product_id` = `main_table`.`product_id`',
                        ['stock_qty' => 'stock_table.qty']
                    )->order(
                        [new \Zend_Db_Expr('stock_qty > 0 ASC'), 'sku ASC']
                    );
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return $items;
    }

    private function getQuoteWebsiteId(Quote $quote): int
    {
        return (int)$this->storeManager->getStore((int)$quote->getStoreId())->getWebsiteId();
    }
}
