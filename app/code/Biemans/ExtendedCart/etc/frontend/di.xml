<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Quote\Model\Quote">
        <plugin name="biemans_sort_cart_items_by_stock_qty" type="Biemans\ExtendedCart\Plugin\SortCartItemsByStockQty"/>
    </type>
    <type name="Magento\Checkout\Block\Cart\Grid">
        <plugin name="biemans_sort_cart_items_by_stock_qty" type="Biemans\ExtendedCart\Plugin\SortCartItemsByStockQty"/>
    </type>
    <type name="Magento\Checkout\Block\Cart\Grid">
        <plugin name="biemans_fix_pager" type="Biemans\ExtendedCart\Plugin\FixPager"/>
    </type>

    <type name="Magento\Customer\CustomerData\SectionPoolInterface">
        <arguments>
            <argument name="sectionSourceMap" xsi:type="array">
                <item name="cart_items" xsi:type="string">Biemans\ExtendedCart\CustomerData\CartItems</item>
            </argument>
        </arguments>
    </type>
</config>
