<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\ExtendedCart\Observer;

use <PERSON><PERSON>mans\Catalog\ViewModel\IsNewProduct;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Event\{Observer, ObserverInterface};

class AddToCartValidation implements ObserverInterface
{
    public function __construct(
        private readonly IsNewProduct $isNewProduct,
        private readonly ProductRepositoryInterface $productRepository
    ) {}

    public function execute(Observer $observer)
    {
        /** @var Product $product */
        $product = $observer->getEvent()->getProduct();
        /** @var mixed[]|int|DataObject $requestInfo */
        $requestInfo = $observer->getEvent()->getInfo();

        // Try to get simple product from request, if available
        if (is_array($requestInfo) && !empty($requestInfo['current_item'])) {
            try {
                /** @var Product $product */
                $product = $this->productRepository->getById((int)$requestInfo['current_item']);
            } catch (NoSuchEntityException $exception) {}
        } elseif ($requestInfo instanceof DataObject && !empty($requestInfo->getData('current_item'))) {
            try {
                /** @var Product $product */
                $product = $this->productRepository->getById((int)$requestInfo->getData('current_item'));
            } catch (NoSuchEntityException $exception) {}
        }

        if ($releaseDate = $this->isNewProduct->getFutureRelease($product)) {
            throw new LocalizedException(
                __(
                    'Article %1 cannot be ordered yet. It will only become saleable on %2.',
                    $product->getSku(),
                    $releaseDate
                )
            );
        }
    }
}
