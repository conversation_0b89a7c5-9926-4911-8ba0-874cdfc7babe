<?php

declare(strict_types=1);

use <PERSON>iemans\Configurator\ViewModel\Configurator;
use Biemans\ThemeConfigurations\ViewModel\Customer;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var Product $product */
if ($block->getProduct()) {
    $product = $block->getProduct();
    /** @var Product $item */
} elseif ($block->getParentBlock() && !empty($item = $block->getParentBlock()->getItem())) {/** @phpstan-ignore-line */
    $product = $item;
} else {
    $product = $currentProduct->get();
}

/** @var Configurator $configuratorViewModel */
$configuratorViewModel = $viewModels->require(Configurator::class);
$configuratorViewModel->setProduct($product);

/** @var Customer $customerViewModel */
$customerViewModel = $viewModels->require(Customer::class);

if (!$configuratorViewModel->hasOptions() || !$customerViewModel->isCustomerLoggedIn()) {
    return '';
}

$uniqueId = '_' . uniqid();
?>

<script defer>
    function initConfiguratorData<?= (int)$product->getId(); ?><?= $uniqueId; ?>(options) {
        return {
            allowedPersonalisation: false,
            init() {
                const sku = options.textSku;
                const customerData = JSON.parse(localStorage.getItem('mage-cache-storage'));
                if (typeof customerData.customer == 'undefined' || !sku) return;
                const allowedSkus = customerData.customer.allowed_personalisation_types;
                if (typeof allowedSkus == 'undefined') {
                    this.allowedPersonalisation = true;
                    return;
                }
                if (allowedSkus.length > 0) {
                    this.allowedPersonalisation = allowedSkus.indexOf(sku) != -1;
                }
            },
            getData: function () {
                return {
                    data: options,
                    formId: '<?= $block->getData('form_id'); ?>',
                    qtyInputId: '<?= $block->getData('qty_input_id'); ?>'
                };
            }
        };
    }
</script>

<button
    x-cloak
    x-show="allowedPersonalisation"
    x-data='initConfiguratorData<?= (int)$product->getId(); ?><?= $uniqueId; ?>(<?= $configuratorViewModel->getJsonConfig() ?>)'
    x-init="$dispatch('toggle-biemans-configurator-title', true)"
    @click.prevent.stop="$dispatch('toggle-biemans-configurator', getData())"
    data-open-configurator="<?= (int)$configuratorViewModel->hasRequiredOptions(); ?>"
    class="option underline"
>
    <span><?= $escaper->escapeHtml(__('Add details')) /** @phpstan-ignore-line */ ?></span>
</button>
