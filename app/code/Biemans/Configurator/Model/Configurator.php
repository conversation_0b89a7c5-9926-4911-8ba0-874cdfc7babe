<?php

declare(strict_types=1);

namespace <PERSON>iemans\Configurator\Model;

use <PERSON><PERSON><PERSON>\Configurator\ViewModel\Configurator as ViewModelConfigurator;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Configuration\Item\ItemInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Model\Quote;
use Psr\Log\LoggerInterface;

class Configurator
{
    const BUY_REQUEST_TEXT_PARAM = 'configurator_text';
    const OPTION_CODE_TEXT = self::BUY_REQUEST_TEXT_PARAM;

    const BUY_REQUEST_SAME_TEXT_PARAM = 'configurator_same_text';

    const BUY_REQUEST_IMAGE_PARAM = 'configurator_image';
    const OPTION_CODE_IMAGE = self::BUY_REQUEST_IMAGE_PARAM;

    public const BUY_REQUEST_ADV_PERSONALISATION = 'configurator_adv_personalisation';
    public const OPTION_ADV_PERSONALISATION = self::BUY_REQUEST_ADV_PERSONALISATION;

    const OPTION_CODE_PRICE = 'product_price';


    public function __construct(
        private readonly File $file,
        private readonly ViewModelConfigurator $viewModelConfigurator,
        private readonly Json $json,
        private readonly RequestInterface $request,
        private readonly Session $customerSession,
        private readonly CartRepositoryInterface $quoteRepository,
        private readonly Request $restRequest,
        private readonly LoggerInterface $logger
    ) {}

    public function calculateProductPriceWithOptions(
        ?float $price,
        Product $product,
        int $qty = 1
    ): ?float
    {
        $this->viewModelConfigurator->setProduct($product);

        // Calculate text price
        if (
            $price
            && $this->viewModelConfigurator->hasCustomTextOption()
            && ($this->viewModelConfigurator->getPersonalisationPrice() > 0)
            && $product->getCustomOption(self::OPTION_CODE_TEXT)
            && !empty($product->getCustomOption(self::OPTION_CODE_TEXT)->getValue())
        ) {
            //if we have advanced personalisation opted we use its price
            $additionalCost = $this->getPersonalisationPrice($qty);
            if (!empty($product->getCustomOption(self::OPTION_ADV_PERSONALISATION)) &&
                $product->getCustomOption(self::OPTION_ADV_PERSONALISATION)->getValue() == 1) {
                $additionalCost = $this->getAdvPersonalisationPrice($qty);
            }

            return $price + $additionalCost;
        }

        // Calculate image price
        foreach ($this->viewModelConfigurator->getPossibleImageOptionIds() as $possibleImageOptionId) {
            if (
                $price
                && $this->viewModelConfigurator->hasImageOption()
                && ($this->viewModelConfigurator->getPersonalisationPrice() > 0)
                && $product->getCustomOption($possibleImageOptionId)
                && !empty($product->getCustomOption($possibleImageOptionId)->getValue())
            ) {
                //if we have advanced personalisation opted we use its price
                $additionalCost = $this->getPersonalisationPrice($qty);
                if (!empty($product->getCustomOption(self::OPTION_ADV_PERSONALISATION)) &&
                    $product->getCustomOption(self::OPTION_ADV_PERSONALISATION)->getValue() == 1) {
                    $additionalCost = $this->getAdvPersonalisationPrice($qty);
                }

                return $price + $additionalCost;
            }
        }

        return $price;
    }

    protected function getPersonalisationPrice(int $qty): float
    {
        $personalisationPrice = $this->viewModelConfigurator->getPersonalisationPrice();

        if ($personalisationTierPrices = $this->viewModelConfigurator->getPersonalisationTierPrices()) {
            foreach ($personalisationTierPrices as $personalisationTierPrice) {
                if ($qty >= $personalisationTierPrice['qty']) {
                    $personalisationPrice = (float)$personalisationTierPrice['price'];
                }
            }
        }

        return $personalisationPrice;
    }

    protected function getAdvPersonalisationPrice(int $qty): float
    {
        $price = $this->viewModelConfigurator->getAdvPersonalisationPrice();

        if ($tierPrices = $this->viewModelConfigurator->getAdvPersonalisationTierPrices()) {
            foreach ($tierPrices as $tierPrice) {
                if ($qty >= $tierPrice['qty']) {
                    $price = (float)$tierPrice['price'];
                }
            }
        }

        return $price;
    }

    public function addOptionsToQuoteItem(
        DataObject $buyRequest,
        Product $product
    ): Product
    {
        $this->viewModelConfigurator->setProduct($product);

        // Prevent validation for add to wishlist
        if ($buyRequest->getData('action') === 'add') {
            return $product;
        }

        if (
            !$this->customerSession->getCustomerGroupId()
            && ($cartId = (int)$this->restRequest->getParam('cartId'))
        ) {
            /** @var Quote $quote */
            $quote = $this->getQuoteByCartId($cartId);
            /**
             * For REST requests, make sure that the customer group ID is set in session and per product,
             * so that the personalised price is calculated correctly
             */
            $this->customerSession->setCustomerGroupId($quote->getCustomerGroupId());
            $product->setCustomerGroupId($quote->getCustomerGroupId());
        }

        if (
            $this->viewModelConfigurator->hasCustomTextOption()
            || $this->viewModelConfigurator->hasImageOption()
        ) {
            /**
             * Add price as option for cart update configurator.
             * Price should be set before any other custom options, to have it as a base price.
             */
            $product->addCustomOption(self::OPTION_CODE_PRICE, $product->getFinalPrice(1));
        }


        if ($this->viewModelConfigurator->hasAdvPersonalisationOption()) {
            $product = $this->addAdvPersonalisation($buyRequest, $product);
        }

        if ($this->viewModelConfigurator->hasCustomTextOption()) {
            $product = $this->addTextOptionToQuoteItem($buyRequest, $product);
        }

        if ($this->viewModelConfigurator->hasImageOption()) {
            $product = $this->addImageOptionToQuoteItem($buyRequest, $product);
        }

        return $product;
    }

    private function addAdvPersonalisation(DataObject $buyRequest, Product $product): Product
    {
        //we need this value in custom options to change SKU for Text/Image Options if they used.
        $advPersonalisation = (int)$buyRequest->getData(self::BUY_REQUEST_ADV_PERSONALISATION);
        $product->addCustomOption(self::OPTION_ADV_PERSONALISATION, $this->json->serialize($advPersonalisation));

        // Add text data to info_buyRequest for every product under group, for reordering
        $buyRequest = $product->getCustomOption('info_buyRequest');
        if ($buyRequest && $buyRequest->getValue()) {
            $buyRequestData = $this->json->unserialize($buyRequest->getValue());
            if (is_array($buyRequestData)) {
                $buyRequestData[self::BUY_REQUEST_ADV_PERSONALISATION] = $advPersonalisation;
                $product->addCustomOption('info_buyRequest', $this->json->serialize($buyRequestData));
            }
        }

        return $product;
    }

    protected function addTextOptionToQuoteItem(
        DataObject $buyRequest,
        Product $product
    ): Product
    {
        $isEmpty = true;
        $textData = [];
        $sameText = (bool)$buyRequest->getData(self::BUY_REQUEST_SAME_TEXT_PARAM);
        $sku = $this->viewModelConfigurator->getTextSku();

        if (!empty($customText = $buyRequest->getData(self::BUY_REQUEST_TEXT_PARAM))) {
            $textData = $this->json->unserialize($customText);

            // If same text flag is set, only use first text item
            if ($sameText && isset($textData[0])) {
                $textData = [$textData[0]];
            }
        } elseif (
            ($options = $buyRequest->getData('options'))
            && !empty($options[self::BUY_REQUEST_TEXT_PARAM])
        ) {
            try {
                $customText = $options[self::BUY_REQUEST_TEXT_PARAM];
                $textOptionData = $this->json->unserialize($customText);
                $textData = $this->json->unserialize($textOptionData['value'] ?? '');
                $sku = $textOptionData['sku'] ?? $sku;
            } catch (\Exception $e) {
            }
        }

        /** @phpstan-ignore-next-line */
        foreach ($textData as $page) {
            foreach ($page as $row) {
                if (!empty(trim($row))) {
                    $isEmpty = false;
                }
            }
        }

        if (!$isEmpty) {
            $product->addCustomOption(self::OPTION_CODE_TEXT, $this->json->serialize($textData));

            //change sku if adv personalisation is chosen
            if ($product->getCustomOption(self::OPTION_ADV_PERSONALISATION) && $product->getCustomOption
                (self::OPTION_ADV_PERSONALISATION)->getValue() == 1) {
                $sku = $this->viewModelConfigurator->getAdvPersonalisationSku();
            }
            $product->addCustomOption(self::OPTION_CODE_TEXT . '_sku', $sku);

            // Add text data to info_buyRequest for every product under group, for reordering
            $buyRequest = $product->getCustomOption('info_buyRequest');
            if ($buyRequest && $buyRequest->getValue()) {
                $buyRequestData = $this->json->unserialize($buyRequest->getValue());
                if (is_array($buyRequestData)) {
                    $buyRequestData[self::BUY_REQUEST_TEXT_PARAM] = $customText;
                    $buyRequestData[self::BUY_REQUEST_SAME_TEXT_PARAM] = $sameText;
                    $product->addCustomOption('info_buyRequest', $this->json->serialize($buyRequestData));
                }
            }
        }

        if (
            $isEmpty && $this->viewModelConfigurator->hasTextOptionRequired()
            // Do not show error messages from quick order module, as these are shown next to every item
            && ($this->request->getFullActionName() !== 'quickorder_items_cartcheckout')/** @phpstan-ignore-line */
        ) {
            throw new LocalizedException(__($this->viewModelConfigurator->getRequiredTextNote()));
        }

        return $product;
    }

    protected function addImageOptionToQuoteItem(
        DataObject $buyRequest,
        Product $product
    ): Product
    {
        $imagesData = $this->file->prepareForCart($buyRequest, $this->viewModelConfigurator->getImageExtensions());
        if ($oldData = $buyRequest->getData(self::BUY_REQUEST_IMAGE_PARAM . '_old')) {
            // Append also the old values
            $oldData = $this->json->unserialize($oldData);
            if (is_array($oldData)) {
                $imagesData = [...$imagesData, ...$oldData];
            }
        }

        $sku = '';
        if (
            ($options = $buyRequest->getData('options'))
            && !empty($options[self::BUY_REQUEST_IMAGE_PARAM])
        ) {
            try {
                $imageOption = $options[self::BUY_REQUEST_IMAGE_PARAM];
                $imageOptionData = $this->json->unserialize($imageOption);
                $sku = $imageOptionData['sku'] ?? '';
            } catch (\Exception $e) {
            }
        }

        if (count($imagesData)) {
            foreach ($imagesData as $index => $imageData) {
                if (!$imageData) {
                    continue;
                }
                $imageData = $this->json->serialize($imageData);
                $product->addCustomOption(self::OPTION_CODE_IMAGE . '_' . $index, $imageData);

                //change sku if adv personalisation is chosen
                if (!$sku) {
                    $sku = $this->viewModelConfigurator->getImageSku();
                }

                if ($product->getCustomOption(self::OPTION_ADV_PERSONALISATION) && $product->getCustomOption
                    (self::OPTION_ADV_PERSONALISATION)->getValue() == 1) {
                    $sku = $this->viewModelConfigurator->getAdvPersonalisationSku();
                }
                $product->addCustomOption(
                    self::OPTION_CODE_IMAGE . '_' . $index . '_sku',
                    $sku
                );

                // Add image data to info_buyRequest for every product under group, for reordering
                $buyRequest = $product->getCustomOption('info_buyRequest');
                if ($buyRequest && $buyRequest->getValue()) {
                    $buyRequestData = $this->json->unserialize($buyRequest->getValue());
                    if (is_array($buyRequestData)) {
                        // Buy Request shouldn't contain the encoded file content because of limited DB storage
                        if (isset($buyRequestData['options'][self::BUY_REQUEST_IMAGE_PARAM])) {
                            unset($buyRequestData['options'][self::BUY_REQUEST_IMAGE_PARAM]);
                        }
                        $buyRequestData[self::BUY_REQUEST_IMAGE_PARAM][$index] = $imageData;
                        $product->addCustomOption('info_buyRequest', $this->json->serialize($buyRequestData));
                    }
                }
            }
        } elseif (
            $this->viewModelConfigurator->hasImageOptionRequired()
            // Do not show error messages from quick order module, as these are shown next to every item
            && ($this->request->getFullActionName() !== 'quickorder_items_cartcheckout')/** @phpstan-ignore-line */
        ) {
            throw new LocalizedException(__($this->viewModelConfigurator->getRequiredImageNote()));
        }

        return $product;
    }

    /**
     * @param array<mixed> $options
     * @param ItemInterface $item
     * @return array<mixed>
     * @throws LocalizedException
     */
    public function prepareOptionsForCart(
        array $options,
        ItemInterface $item
    ): array
    {
        $this->viewModelConfigurator->setProduct($item->getProduct());

        if ($this->viewModelConfigurator->hasCustomTextOption()) {
            $options = $this->prepareTextOptionForCart($options, $item);
        }

        if ($this->viewModelConfigurator->hasImageOption()) {
            $options = $this->prepareImageOptionForCart($options, $item);
        }

        return $options;
    }

    /**
     * @param array<mixed> $options
     * @param ItemInterface $item
     * @return array<mixed>
     */
    public function prepareTextOptionForCart(
        array $options,
        ItemInterface $item
    ): array
    {
        if (
            $item->getOptionByCode(self::OPTION_CODE_TEXT) /** @phpstan-ignore-line */
            && !empty($textValue = $item->getOptionByCode(self::OPTION_CODE_TEXT)->getValue())
        ) {
            $customText = $this->getTextValueForRendering($textValue);

            if (!$customText) {
                return $options;
            }
            //change sku if adv personalisation is chosen
            $sku = $this->viewModelConfigurator->getTextSku();
            /** @phpstan-ignore-next-line */
            if (!empty($item->getOptionByCode(self::OPTION_ADV_PERSONALISATION)?->getValue())) {
                $sku = $this->viewModelConfigurator->getAdvPersonalisationSku();
            }
            $options[] = [
                'label' => __('Text'),
                'value' => $customText,
                'print_value' => $customText,
                'code' => $sku,
                'id' => self::OPTION_CODE_TEXT,
                'sku' => $sku
            ];
        }

        return $options;
    }

    /**
     * @param array<mixed> $options
     * @param ItemInterface $item
     * @return array<mixed>
     * @throws LocalizedException
     */
    public function prepareImageOptionForCart(
        array $options,
        ItemInterface $item
    ): array
    {
        foreach ($this->viewModelConfigurator->getPossibleImageOptionIds() as $possibleImageOption) {
            if (
                $item->getOptionByCode($possibleImageOption) /** @phpstan-ignore-line */
                && !empty($imageValue = $item->getOptionByCode($possibleImageOption)->getValue())
            ) {
                $formattedValue = $this->file->getFormattedOptionValue(
                    (int)$item->getOptionByCode($possibleImageOption)->getId(), /** @phpstan-ignore-line */
                    $imageValue
                );

                if (!$formattedValue) {
                    return $options;
                }
                //change sku if adv personalisation is chosen
                $sku = $this->viewModelConfigurator->getImageSku();
                /** @phpstan-ignore-next-line */
                if (!empty($item->getOptionByCode(self::OPTION_ADV_PERSONALISATION)?->getValue())) {
                    $sku = $this->viewModelConfigurator->getAdvPersonalisationSku();
                }

                $options[] = [
                    'label' => __('Image'),
                    'value' => $formattedValue,
                    'print_value' => $formattedValue,
                    'code' => $sku,
                    'id' => $possibleImageOption,
                    'sku' => $sku
                ];
            }
        }

        return $options;
    }

    protected function getTextValueForRendering(string $textValue): ?string
    {
        $textData = $this->json->unserialize($textValue);

        if (!$textData || !is_array($textData)) {
            return null;
        }

        $renderValue = '<span class="custom_text">';
        foreach ($textData as $row) {
            $renderValue .= '<span class="row">';
            foreach ($row as $line) {
                if (!is_null($line) && !empty(trim($line))) {
                    $renderValue .= nl2br($line) . '<br/>';
                }
            }
            $renderValue .= '<br/></span>';
        }
        $renderValue .= '</span>';

        return $renderValue;
    }

    /**
     * @param Product $product
     * @param array<mixed> $options
     * @return array<mixed>
     * @throws LocalizedException
     */
    public function addOptionsToOrderItem(
        Product $product,
        array $options
    ): array
    {
        $this->viewModelConfigurator->setProduct($product);

        if ($this->viewModelConfigurator->hasCustomTextOption()) {
            $options = $this->addTextOptionToOrderItem($product, $options);
        }

        if ($this->viewModelConfigurator->hasImageOption()) {
            $options = $this->addImageOptionToOrderItem($product, $options);
        }

        return $options;
    }

    /**
     * @param Product $product
     * @param array<mixed> $options
     * @return array<mixed>
     */
    protected function addTextOptionToOrderItem(
        Product $product,
        array $options
    ): array
    {
        $option = $product->getCustomOption(self::OPTION_CODE_TEXT);

        if (
            !$option
            || empty($option->getValue())
        ) {
            return $options;
        }

        $customText = $this->getTextValueForRendering($option->getValue());

        // change sku if we have adv options
        $sku = $this->viewModelConfigurator->getTextSku();
        try {
            // Check if SKU option is available on quote item
            if ($option->getCode()) { /** @phpstan-ignore-line */
                $skuOptionCode = $option->getCode() . '_sku'; /** @phpstan-ignore-line */
                $skuOption = $product->getCustomOption($skuOptionCode);
                $sku = $skuOption->getValue();
            }
        } catch (\Exception $e) {}

        $advancedPersonalisationSku = $this->viewModelConfigurator->getAdvPersonalisationSku();
        if (!empty($options['info_buyRequest']) && !empty($options['info_buyRequest'][self::OPTION_ADV_PERSONALISATION])) {
            $sku = $advancedPersonalisationSku;
        }

        $personalisationType = ($sku === $advancedPersonalisationSku) ?
            $this->viewModelConfigurator->getAdvPersonalisationName() :
            $this->viewModelConfigurator->getBasicPersonalisationName();

        $options['options'][] = [
            'label' => 'Text',
            'value' => $customText,
            'print_value' => $customText,
            'option_id' => self::OPTION_CODE_TEXT,
            'option_sku' => $sku,
            'personalisation_technique' => $personalisationType,
            'option_value' => $option->getValue()
        ];

        return $options;
    }

    /**
     * @param Product $product
     * @param array<mixed> $options
     * @return array<mixed>
     * @throws LocalizedException
     */
    protected function addImageOptionToOrderItem(
        Product $product,
        array $options
    ): array
    {
        foreach ($this->viewModelConfigurator->getPossibleImageOptionIds() as $possibleImageOptionId) {
            $option = $product->getCustomOption($possibleImageOptionId);

            if (!$option || empty($option->getValue())) {
                continue;
            }

            $optionValue = $option->getValue();
            $formattedValue = $this->file->getFormattedOptionValue(
                (int)$option->getId(), /** @phpstan-ignore-line */
                $optionValue
            );
            $optionData = $this->json->unserialize($optionValue);
            $printValue = !empty($optionData['title']) ? $optionData['title'] : '';

            try {
                $this->file->copyQuoteToOrder($option);
            } catch (\Exception $e) {
                // Will work without copy, so it can be caught
                $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
            }

            // change sku if we have adv options
            $sku = $this->viewModelConfigurator->getImageSku();
            try {
                // Check if SKU option is available on quote item
                if ($option->getCode()) { /** @phpstan-ignore-line */
                    $skuOptionCode = $option->getCode() . '_sku'; /** @phpstan-ignore-line */
                    $skuOption = $product->getCustomOption($skuOptionCode);
                    $sku = $skuOption->getValue();
                }
            } catch (\Exception $e) {}

            $advancedPersonalisationSku = $this->viewModelConfigurator->getAdvPersonalisationSku();
            if (!empty($options['info_buyRequest']) && !empty($options['info_buyRequest'][self::OPTION_ADV_PERSONALISATION])) {
                $sku = $advancedPersonalisationSku;
            }

            $personalisationType = ($sku === $advancedPersonalisationSku) ?
                $this->viewModelConfigurator->getAdvPersonalisationName() :
                $this->viewModelConfigurator->getBasicPersonalisationName();

            $options['options'][] = [
                'label' => 'Image',
                'value' => $formattedValue,
                'print_value' => $printValue,
                'option_id' => $possibleImageOptionId,
                'option_sku' => $sku,
                'personalisation_technique' => $personalisationType,
                'option_value' => $optionValue,
                'option_type' => 'file',
                'custom_view' => true
            ];
        }

        return $options;
    }

    private function getQuoteByCartId(int $cartId): CartInterface
    {
        return $this->quoteRepository->getActive($cartId);
    }
}
