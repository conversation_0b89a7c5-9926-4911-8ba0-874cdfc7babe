<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Configurator\Plugin;

use <PERSON><PERSON>mans\Configurator\Model\Configurator;
use <PERSON>iemans\Configurator\ViewModel\Configurator as ViewModelConfigurator;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Type\AbstractType;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Message\ManagerInterface;
use Psr\Log\LoggerInterface;

class ProcessOptions
{
    private Configurator $configurator;
    private ViewModelConfigurator $viewModelConfigurator;
    private LoggerInterface $logger;
    private ManagerInterface $messageManager;

    public function __construct(
        Configurator $configurator,
        ViewModelConfigurator $viewModelConfigurator,
        ManagerInterface $messageManager,
        LoggerInterface $logger
    ) {
        $this->configurator = $configurator;
        $this->viewModelConfigurator = $viewModelConfigurator;
        $this->logger = $logger;
        $this->messageManager = $messageManager;
    }

    public function afterHasOptions(
        AbstractType $subject,
        bool $result,
        Product $product
    ): bool {
        try {
            $this->viewModelConfigurator->setProduct($product);
            if ($this->viewModelConfigurator->hasOptions()) {
                return true;
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
        }

        return $result;
    }

    public function afterCanConfigure(
        AbstractType $subject,
        bool $result,
        Product $product
    ): bool {
        try {
            $this->viewModelConfigurator->setProduct($product);
            if ($this->viewModelConfigurator->hasOptions()) {
                return true;
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * @param AbstractType $subject
     * @param Product[]|string $products
     * @param DataObject $buyRequest
     * @param Product $originalProduct
     * @param string|null $processMode
     * @return Product[]|string
     */
    public function afterProcessConfiguration(
        AbstractType $subject,
        $products,
        DataObject $buyRequest,
        Product $originalProduct,
        ?string $processMode = null
    ) {
        try {
            if (is_array($products)) {
                foreach ($products as &$product) {
                    $this->configurator->addOptionsToQuoteItem($buyRequest, $product);
                }
            }
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
        }

        return $products;
    }

    /**
     * @param AbstractType $subject
     * @param Product[]|string $products
     * @param DataObject $buyRequest
     * @param Product $originalProduct
     * @param string|null $processMode
     * @return Product[]|string
     */
    public function afterPrepareForCartAdvanced(
        AbstractType $subject,
        $products,
        DataObject $buyRequest,
        Product $originalProduct,
        ?string $processMode
    ) {
        try {
            if (is_array($products)) {
                foreach ($products as &$product) {
                    $this->configurator->addOptionsToQuoteItem($buyRequest, $product);
                }
            }
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
        }

        return $products;
    }

    /**
     * @param AbstractType $subject
     * @param array<mixed> $options
     * @param Product $product
     * @return array<mixed>
     */
    public function afterGetOrderOptions(
        AbstractType $subject,
        array $options,
        Product $product
    ): array {
        try {
            $options = $this->configurator->addOptionsToOrderItem($product, $options);
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
        }

        return $options;
    }
}
