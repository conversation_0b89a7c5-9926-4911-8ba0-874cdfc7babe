<?php

declare(strict_types=1);

namespace <PERSON>iemans\Configurator\Plugin;

use <PERSON><PERSON><PERSON>\Configurator\Model\Configurator;
use Magento\Catalog\Model\CustomOptions\{
    CustomOption,
    CustomOptionFactory
};
use Magento\Catalog\Model\Product\Option\UrlBuilder;
use Magento\Catalog\Model\ProductOptionProcessorInterface;
use Magento\Framework\DataObject;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;

class AddOptionsToOrderApi
{
    private Json $json;
    private LoggerInterface $logger;
    private CustomOptionFactory $customOptionFactory;
    private UrlBuilder $urlBuilder;

    public function __construct(
        Json $json,
        CustomOptionFactory $customOptionFactory,
        UrlBuilder $urlBuilder,
        LoggerInterface $logger
    )
    {
        $this->json = $json;
        $this->customOptionFactory = $customOptionFactory;
        $this->urlBuilder = $urlBuilder;
        $this->logger = $logger;
    }

    /**
     * @param ProductOptionProcessorInterface $subject
     * @param array<mixed> $options
     * @param DataObject $request
     * @return array<mixed>
     */
    public function afterConvertToProductOption(
        ProductOptionProcessorInterface $subject,
        array $options,
        DataObject $request
    ): array
    {
        try {
            $productOptions = $request->getData('product_options');
            if (
                is_array($productOptions)
                && isset($productOptions['options'])
                && is_array($productOptions['options'])
            ) {
                $data = $this->getConfiguratorOptions($productOptions['options']);

                if (empty($data)) {
                    return $options;
                }

                if (!isset($options['custom_options'])) {
                    $options['custom_options'] = $data;
                } else {
                    $this->mergeCustomOptions($options['custom_options'], $data);
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_Configurator: ' . $e->getMessage());
        }

        return $options;
    }

    /**
     * @param array<mixed> $productOptions
     * @return array<mixed>
     */
    protected function getConfiguratorOptions(array $productOptions): array
    {
        $data = [];
        foreach ($productOptions as $optionData) {
            if (
                !empty($optionData)
                && isset($optionData['option_id'])
                && isset($optionData['option_value'])
                && (
                    str_contains($optionData['option_id'], Configurator::OPTION_CODE_IMAGE)
                    || str_contains($optionData['option_id'], Configurator::OPTION_CODE_TEXT)
                    || str_contains($optionData['option_id'], Configurator::OPTION_ADV_PERSONALISATION)
                )
            ) {
                /** @var CustomOption $option */
                $option = $this->customOptionFactory->create();
                $option->setOptionId($optionData['option_id']);
                $option->setOptionValue($this->processOptionValue($optionData));

                $data[] = $option;
            }
        }

        return $data;
    }

    /**
     * @param array<mixed> $optionData
     * @return string
     */
    private function processOptionValue(array $optionData): string
    {
        $value = [
            'sku' => $optionData['option_sku'] ?? '',
            'value' => $optionData['option_value']
        ];

        try {
            $optionData = $this->json->unserialize($optionData['option_value']);

            if (
                isset($optionData['url'])
                && isset($optionData['url']['route'])
                && isset($optionData['url']['params'])
            ) {
                $value['url'] = $this->urlBuilder->getUrl(
                    $optionData['url']['route'],
                    $optionData['url']['params']
                );
            }
        } catch (\Exception $e) {
            // Nothing
        }

        return (string)$this->json->serialize($value);
    }

    /**
     * Method merges custom options checking their id. Simple array_merge will not work. We cant have same option_id
     * in the response.
     * @param mixed[] $existedCustomOptions
     * @param mixed[] $newCustomOptions
     * @return void
     */
    private function mergeCustomOptions(array &$existedCustomOptions, array $newCustomOptions): void
    {
        foreach ($newCustomOptions as $newCustomOption) {
            foreach ($existedCustomOptions as $existedCustomOption) {
                if ($existedCustomOption->getData('option_id') === $newCustomOption->getData('option_id')) {
                    break 2;
                }
                $existedCustomOptions[] = $newCustomOption;
            }
        }
    }
}
