<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Catalog\Model\Product\Type\AbstractType">
		<plugin name="biemans_configurator_process_options" type="Biemans\Configurator\Plugin\ProcessOptions"/>
	</type>
	<type name="Magento\Catalog\Model\Product\Type\Price">
		<plugin name="biemans_configurator_calculate_options_price" type="B<PERSON>mans\Configurator\Plugin\CalculateOptionsPrice"/>
	</type>
	<type name="Magento\Catalog\Helper\Product\Configuration">
		<plugin name="biemans_configurator_render_options" type="Biemans\Configurator\Plugin\RenderOptions"/>
	</type>
	<type name="Magento\Catalog\Model\ProductOptionProcessorInterface">
		<plugin name="biemans_configurator_add_options_for_api" type="Biemans\Configurator\Plugin\AddOptionsToOrderApi"/>
	</type>
	<type name="Magento\Sales\Model\Reorder\OrderInfoBuyRequestGetter">
		<plugin name="biemans_configurator_prepare_info_for_reorder" type="Biemans\Configurator\Plugin\PrepareReorderInfo"/>
	</type>
	<type name="Magento\Quote\Model\Quote\Item\AbstractItem">
		<plugin name="biemans_validate_configurator_data" type="Biemans\Configurator\Plugin\CartItem\ValidateConfiguratorData"/>
	</type>
</config>
