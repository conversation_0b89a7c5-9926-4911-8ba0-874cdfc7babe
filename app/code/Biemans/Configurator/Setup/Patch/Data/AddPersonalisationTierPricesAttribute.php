<?php

declare(strict_types=1);

namespace <PERSON>iemans\Configurator\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\Backend\JsonEncoded;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\
{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};

class AddPersonalisationTierPricesAttribute implements DataPatchInterface
{
    const TIER_PRICES_ATTRIBUTE = 'personalisation_tier_prices';

    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TIER_PRICES_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => JsonEncoded::class,
                'comparable' => false,
                'default' => '',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'input' => 'text',
                'label' => 'Personalisation Tier Prices',
                'required' => false,
                'searchable' => false,
                'type' => 'text',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '300',
                'group' => 'Biemans Configurator'
            ]
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
