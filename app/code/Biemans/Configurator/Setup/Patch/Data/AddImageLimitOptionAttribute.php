<?php

declare(strict_types=1);

namespace Biemans\Configurator\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{EavSetup, EavSetupFactory};
use Magento\Framework\Setup\{ModuleDataSetupInterface, Patch\DataPatchInterface};

class AddImageLimitOptionAttribute implements DataPatchInterface
{
    const IMAGES_LIMIT_ATTRIBUTE = 'images_limit';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory
    ) {}

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $eavSetup->addAttribute(
            $entityTypeId,
            self::IMAGES_LIMIT_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '3',
                'filterable' => false,
                'frontend' => '',
                'frontend_class' => 'validate-digits',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'input' => 'text',
                'label' => 'Maximum number of images',
                'required' => false,
                'searchable' => false,
                'type' => 'varchar',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '120',
                'group' => 'Biemans Configurator'
            ]
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
