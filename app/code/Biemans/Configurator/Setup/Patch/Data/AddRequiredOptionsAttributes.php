<?php

declare(strict_types=1);

namespace <PERSON>ie<PERSON>\Configurator\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\
{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};

class AddRequiredOptionsAttributes implements DataPatchInterface
{
    const IMAGE_REQUIRED_ATTRIBUTE = 'image_required';
    const TEXT_REQUIRED_ATTRIBUTE = 'text_required';

    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $eavSetup->addAttribute(
            $entityTypeId,
            self::IMAGE_REQUIRED_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '0',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'input' => 'select',
                'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                'label' => 'Image Option Required',
                'required' => false,
                'searchable' => false,
                'type' => 'int',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '120',
                'group' => 'Biemans Configurator'
            ]
        );

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TEXT_REQUIRED_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '0',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'input' => 'select',
                'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                'label' => 'Text Option Required',
                'required' => false,
                'searchable' => false,
                'type' => 'int',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '215',
                'group' => 'Biemans Configurator'
            ]
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
