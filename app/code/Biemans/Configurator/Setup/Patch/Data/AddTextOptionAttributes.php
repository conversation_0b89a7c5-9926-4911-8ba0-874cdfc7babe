<?php

declare(strict_types=1);

namespace Bie<PERSON>\Configurator\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\{
    EavSetup,
    EavSetupFactory
};
use Magento\Framework\Setup\
{
    ModuleDataSetupInterface,
    Patch\DataPatchInterface
};

class AddTextOptionAttributes implements DataPatchInterface
{
    const TEXT_SKU_ATTRIBUTE = 'text_option_sku';
    const TEXT_PRICE_ATTRIBUTE = 'text_option_price';
    const TEXT_LINES_ATTRIBUTE = 'text_lines';
    const TEXT_LINES_LIMIT_ATTRIBUTE = 'text_lines_limit';
    const TEXT_LINES_MAX_CHARS_ATTRIBUTE = 'max_chars_per_line';

    private ModuleDataSetupInterface $moduleDataSetup;
    private EavSetupFactory $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $eavSetup->getEntityTypeId(Product::ENTITY);

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TEXT_SKU_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'input' => 'text',
                'label' => 'Text Option SKU',
                'required' => false,
                'searchable' => false,
                'type' => 'varchar',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '200',
                'group' => 'Biemans Configurator'
            ]
        );

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TEXT_PRICE_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '',
                'filterable' => false,
                'frontend' => '',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'input' => 'price',
                'label' => 'Text Option Price',
                'required' => false,
                'searchable' => false,
                'type' => 'decimal',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '210',
                'group' => 'Biemans Configurator'
            ]
        );

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TEXT_LINES_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '3',
                'filterable' => false,
                'frontend' => '',
                'frontend_class' => 'validate-digits',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'input' => 'text',
                'label' => 'Number of custom text lines',
                'required' => false,
                'searchable' => false,
                'type' => 'varchar',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '220',
                'group' => 'Biemans Configurator'
            ]
        );

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TEXT_LINES_LIMIT_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '150',
                'filterable' => false,
                'frontend' => '',
                'frontend_class' => 'validate-digits',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'input' => 'text',
                'label' => 'Limit of custom text lines',
                'required' => false,
                'searchable' => false,
                'type' => 'varchar',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '230',
                'group' => 'Biemans Configurator'
            ]
        );

        $eavSetup->addAttribute(
            $entityTypeId,
            self::TEXT_LINES_MAX_CHARS_ATTRIBUTE,
            [
                'apply_to' => '',
                'backend' => '',
                'comparable' => false,
                'default' => '150',
                'filterable' => false,
                'frontend' => '',
                'frontend_class' => 'validate-digits',
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'input' => 'text',
                'label' => 'Maximum characters per line',
                'required' => false,
                'searchable' => false,
                'type' => 'varchar',
                'wysiwyg_enabled' => false,
                'unique' => false,
                'used_in_product_listing' => true,
                'user_defined' => true,
                'visible' => true,
                'visible_on_front' => false,
                'sort_order' => '240',
                'group' => 'Biemans Configurator'
            ]
        );

        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
