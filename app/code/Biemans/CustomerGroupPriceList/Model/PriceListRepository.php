<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupPriceList\Model;

use <PERSON>iemans\CustomerGroupPriceList\Api\{
    PriceListRepositoryInterface,
    Data\PriceListInterface,
    Data\PriceListInterfaceFactory,
    Data\PriceListSearchResultsInterfaceFactory
};
use Biemans\CustomerGroupPriceList\Model\ResourceModel\{
    PriceList as ResourcePriceList,
    PriceList\CollectionFactory as PriceListCollectionFactory
};
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\{
    CouldNotDeleteException,
    CouldNotSaveException,
    NoSuchEntityException
};

class PriceListRepository implements PriceListRepositoryInterface
{
    protected CollectionProcessorInterface $collectionProcessor;
    protected ResourcePriceList $resource;
    protected PriceListInterfaceFactory $priceListFactory;
    protected PriceListSearchResultsInterfaceFactory $searchResultsFactory;
    protected PriceListCollectionFactory $priceListCollectionFactory;

    public function __construct(
        ResourcePriceList $resource,
        PriceListInterfaceFactory $priceListFactory,
        PriceListCollectionFactory $priceListCollectionFactory,
        PriceListSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->priceListFactory = $priceListFactory;
        $this->priceListCollectionFactory = $priceListCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    public function save(PriceListInterface $priceList): PriceListInterface
    {
        try {
            /** @var \Biemans\CustomerGroupPriceList\Model\PriceList $priceList */
            $this->resource->save($priceList);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the price list: %1',
                $exception->getMessage()
            ));
        }

        return $priceList;
    }

    public function get($priceListId)
    {
        /** @var \Biemans\CustomerGroupPriceList\Model\PriceList $priceList */
        $priceList = $this->priceListFactory->create();
        $this->resource->load($priceList, $priceListId);
        if (!$priceList->getId()) {
            throw new NoSuchEntityException(__('Price list with id "%1" does not exist.', $priceListId));
        }

        return $priceList;
    }

    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->priceListCollectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }

    public function delete(PriceListInterface $priceList)
    {
        try {
            /** @var \Biemans\CustomerGroupPriceList\Model\PriceList $priceListModel */
            $priceListModel = $this->priceListFactory->create();
            $this->resource->load($priceListModel, $priceList->getEntityId());
            $this->resource->delete($priceListModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the price list: %1',
                $exception->getMessage()
            ));
        }

        return true;
    }

    public function deleteById($priceListId)
    {
        return $this->delete($this->get($priceListId));
    }
}
