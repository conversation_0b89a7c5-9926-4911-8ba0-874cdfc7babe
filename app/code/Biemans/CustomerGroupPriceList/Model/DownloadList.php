<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerGroupPriceList\Model;

use <PERSON>iemans\CustomerGroupPriceList\ViewModel\PriceList;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Psr\Log\LoggerInterface;

class DownloadList
{
    private FileFactory $fileFactory;
    private PriceListFile $priceListFile;
    private PriceList $priceList;
    private LoggerInterface $logger;

    public function __construct(
        FileFactory $fileFactory,
        PriceListFile $priceListFile,
        PriceList $priceList,
        LoggerInterface $logger
    ) {
        $this->fileFactory = $fileFactory;
        $this->priceListFile = $priceListFile;
        $this->priceList = $priceList;
        $this->logger = $logger;
    }

    /**
     * @return ResponseInterface|null
     */
    public function getFileStream(int $groupId, string $type, ?bool $keepName = false)
    {
        try {
            if (strtolower(trim($type)) === 'pdf') {
                $fileName = $this->priceList->getPdfPriceList($groupId);
            } else {
                $fileName = $this->priceList->getExcelPriceList($groupId);
            }

            if ($fileName) {
                $content = $this->priceListFile->getFileContent($fileName);
                $mimeType = $this->priceListFile->getMimeType($fileName);
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);

                $streamFileName = 'price_list.' . $extension;
                if ($keepName) {
                    $streamFileName = $fileName;
                }

                return $this->fileFactory->create(
                    $streamFileName,
                    [
                        'type' => 'string',
                        'value' => $content,
                        'rm' => true
                    ],
                    DirectoryList::VAR_DIR,
                    $mimeType
                );
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_CustomerInvoices: ' . $e->getMessage());
        }

        return null;
    }
}
