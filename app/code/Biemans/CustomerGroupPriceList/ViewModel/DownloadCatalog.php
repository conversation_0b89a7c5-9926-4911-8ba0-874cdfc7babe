<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerGroupPriceList\ViewModel;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class DownloadCatalog implements ArgumentInterface
{
    const XML_PATH_BASE = 'biemans_customer_catalog/general/';
    const XML_PATH_LABEL = self::XML_PATH_BASE . 'label';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly StoreManagerInterface $storeManager
    ) {}

    private function getConfig(string $path, int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            $path,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getLabel(): string
    {
        return $this->getConfig(self::XML_PATH_LABEL);
    }

    public function getPdf(string $path): string
    {
        return $this->getConfig(self::XML_PATH_BASE . $path);
    }

    public function getMediaUrl(string $filePath): string
    {
        return trim($this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA), '/')
            . '/customer_catalog/' . trim($filePath, '/');
    }

    public function getPdfLabel(string $path): string
    {
        return $this->getConfig(self::XML_PATH_BASE . 'button_' . $path);
    }
}
