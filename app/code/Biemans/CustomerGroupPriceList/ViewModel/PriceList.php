<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerGroupPriceList\ViewModel;

use Magento\Customer\Api\Data\GroupInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Psr\Log\LoggerInterface;

class PriceList implements ArgumentInterface
{
    private GroupRepositoryInterface $groupRepository;
    private LoggerInterface $logger;

    public function __construct(
        GroupRepositoryInterface $groupRepository,
        LoggerInterface $logger
    ) {
        $this->groupRepository = $groupRepository;
        $this->logger = $logger;
    }

    public function getPdfPriceList(int $groupId): string
    {
        if ($customerGroup = $this->getCustomerGroup($groupId)) {
            $extensionAttributes = $customerGroup->getExtensionAttributes();

            return $extensionAttributes->getPdfFile() ?? '';
        }

        return '';
    }

    public function getExcelPriceList(int $groupId): string
    {
        if ($customerGroup = $this->getCustomerGroup($groupId)) {
            $extensionAttributes = $customerGroup->getExtensionAttributes();

            return $extensionAttributes->getExcelFile() ?? '';
        }

        return '';
    }

    protected function getCustomerGroup(int $groupId): ?GroupInterface
    {
        static $group = null;

        if (!$group) {
            try {
                if ($groupId) {
                    $group = $this->groupRepository->getById($groupId);
                }
            } catch (\Exception $e) {
                $this->logger->critical('Biemans_CustomerGroupPriceList: ' . $e->getMessage());
            }
        }

        return $group;
    }
}
