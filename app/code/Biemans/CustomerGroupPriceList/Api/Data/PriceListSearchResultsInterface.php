<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupPriceList\Api\Data;

interface PriceListSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{
    /**
     * Get price list items
     * @return \Biemans\CustomerGroupPriceList\Api\Data\PriceListInterface[]
     */
    public function getItems();

    /**
     * Set price list items
     * @param \Biemans\CustomerGroupPriceList\Api\Data\PriceListInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
