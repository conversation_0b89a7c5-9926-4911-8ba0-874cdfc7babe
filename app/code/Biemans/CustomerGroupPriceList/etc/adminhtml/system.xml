<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd"
>
    <system>
        <section
            id="biemans_customer_catalog"
            type="text"
            translate="label"
            sortOrder="20"
            showInDefault="1"
            showInWebsite="1"
            showInStore="1"
        >
            <label>Customer Catalog</label>
            <tab>biemans</tab>
            <resource>Biemans_CustomerGroupPriceList::system_config</resource>
            <group
                id="general"
                type="text"
                translate="label"
                sortOrder="10"
                showInDefault="1"
                showInWebsite="1"
                showInStore="1"
            >
                <label>General</label>
                <field
                    id="label"
                    translate="label"
                    type="text"
                    sortOrder="10"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Label</label>
                </field>
                <field
                    id="pdf"
                    type="file"
                    sortOrder="20"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Catalog PDF/XLSX 1</label>
                    <backend_model>Biemans\CustomerGroupPriceList\Model\Config\Backend\Pdf</backend_model>
                    <upload_dir config="system/filesystem/media" scope_info="1">customer_catalog</upload_dir>
                    <base_url type="media" scope_info="1">customer_catalog</base_url>
                </field>
                <field
                    id="button_pdf"
                    translate="label"
                    type="text"
                    sortOrder="25"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Button Label PDF/XLSX 1</label>
                </field>
                <field
                    id="second_pdf"
                    type="file"
                    sortOrder="30"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Catalog PDF/XLSX 2</label>
                    <backend_model>Biemans\CustomerGroupPriceList\Model\Config\Backend\Pdf</backend_model>
                    <upload_dir config="system/filesystem/media" scope_info="1">customer_catalog</upload_dir>
                    <base_url type="media" scope_info="1">customer_catalog</base_url>
                </field>
                <field
                    id="button_second_pdf"
                    translate="label"
                    type="text"
                    sortOrder="35"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Button Label PDF/XLSX 2</label>
                </field>
                <field
                    id="third_pdf"
                    type="file"
                    sortOrder="40"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Catalog PDF/XLSX 3</label>
                    <backend_model>Biemans\CustomerGroupPriceList\Model\Config\Backend\Pdf</backend_model>
                    <upload_dir config="system/filesystem/media" scope_info="1">customer_catalog</upload_dir>
                    <base_url type="media" scope_info="1">customer_catalog</base_url>
                </field>
                <field
                    id="button_third_pdf"
                    translate="label"
                    type="text"
                    sortOrder="45"
                    showInDefault="1"
                    showInWebsite="1"
                    showInStore="1"
                >
                    <label>Button Label PDF/XLSX 3</label>
                </field>
            </group>
        </section>
    </system>
</config>
