<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupPriceList\Plugin;

use Magento\Customer\Api\Data\GroupInterfaceFactory;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Customer\Block\Adminhtml\Group\Edit\Form;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\UrlInterface;
use Psr\Log\LoggerInterface;

class AddExtraFormFields
{
    private GroupRepositoryInterface $groupRepository;
    private GroupInterfaceFactory $groupDataFactory;
    private RequestInterface $request;
    private UrlInterface $urlBuilder;
    private LoggerInterface $logger;

    public function __construct(
        GroupRepositoryInterface $groupRepository,
        GroupInterfaceFactory $groupDataFactory,
        RequestInterface $request,
        UrlInterface $urlBuilder,
        LoggerInterface $logger
    ) {
        $this->groupRepository = $groupRepository;
        $this->groupDataFactory = $groupDataFactory;
        $this->request = $request;
        $this->urlBuilder = $urlBuilder;
        $this->logger = $logger;
    }

    public function afterSetLayout(Form $subject, Form $result): Form
    {
        try {
            $groupId = (int)$this->request->getParam('id');
            if (!$groupId) {
                $customerGroup = $this->groupDataFactory->create();
            } else {
                $customerGroup = $this->groupRepository->getById($groupId);
            }

            /** @var \Magento\Framework\Data\Form $form */
            $form = $result->getForm();
            $form->setData('enctype', 'multipart/form-data');
            $fieldset = $form->addFieldset('files_fieldset', ['legend' => __('Currency & Price Lists')]);
            $extensionAttributes = $customerGroup->getExtensionAttributes();

            $fieldset->addField(
                'currency',
                'text',
                [
                    'name' => 'currency',
                    'label' => __('Currency Code'),
                    'title' => __('Currency Code'),
                    'required' => true,
                    'value' => $extensionAttributes->getCurrency()
                ]
            );

            if ($extensionAttributes->getPdfFile()) {
                $fieldset->addField('old_pdf_file', 'hidden',
                    ['name' => 'old_pdf_file', 'value' => $extensionAttributes->getPdfFile()]
                );
                $fieldset->addField(
                    'pdf_file',
                    'file',
                    [
                        'label' => __('PDF Price List'),
                        'title' => __('PDF Price List'),
                        'name' => 'pdf_file',
                        'note' => __('Upload a PDF file with prices for this group.'),
                        'after_element_html' => '<a href="'
                            . $this->urlBuilder->getUrl('biemans_price_list/pricelist/download', [
                                'id' => $groupId,
                                'type' => 'pdf'
                            ])
                            . '">'
                            . $extensionAttributes->getPdfFile()
                            . '</a>'
                    ]
                );

                $fieldset->addField(
                    'remove_pdf_file',
                    'checkbox',
                    [
                        'label' => __('Remove PDF File'),
                        'name' => 'remove_pdf_file',
                        'value' => 1, // Value to be submitted when checked
                        'after_element_html' => '&nbsp;' . __('Check to remove the uploaded file.')
                    ]
                );
            } else {
                $fieldset->addField(
                    'pdf_file',
                    'file',
                    [
                        'label' => __('PDF Price List'),
                        'title' => __('PDF Price List'),
                        'name' => 'pdf_file',
                        'required' => false,
                        'note' => __('Upload a PDF file with prices for this group.')
                    ]
                );
            }

            if ($extensionAttributes->getExcelFile()) {
                $fieldset->addField('old_excel_file', 'hidden',
                    ['name' => 'old_excel_file', 'value' => $extensionAttributes->getExcelFile()]
                );
                $fieldset->addField(
                    'excel_file',
                    'file',
                    [
                        'label' => __('Excel Price List'),
                        'title' => __('Excel Price List'),
                        'name' => 'excel_file',
                        'note' => __('Upload an Excel file with prices for this group.'),
                        'after_element_html' => '<a href="'
                            . $this->urlBuilder->getUrl('biemans_price_list/pricelist/download', [
                                'id' => $groupId,
                                'type' => 'excel'
                            ])
                            . '">'
                            . $extensionAttributes->getExcelFile()
                            . '</a>'
                    ]
                );

                $fieldset->addField(
                    'remove_excel_file',
                    'checkbox',
                    [
                        'label' => __('Remove Excel File'),
                        'name' => 'remove_excel_file',
                        'value' => 1, // Value to be submitted when checked
                        'after_element_html' => '&nbsp;' . __('Check to remove the uploaded file.')
                    ]
                );
            } else {
                $fieldset->addField(
                    'excel_file',
                    'file',
                    [
                        'label' => __('Excel Price List'),
                        'title' => __('Excel Price List'),
                        'name' => 'excel_file',
                        'required' => false,
                        'note' => __('Upload an Excel file with prices for this group.')
                    ]
                );
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_CustomerGroupPriceList: ' . $e->getMessage());
        }

        return $result;
    }
}
