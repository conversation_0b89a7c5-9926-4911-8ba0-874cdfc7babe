<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerGroupPriceList\Plugin;

use Magento\Customer\Model\ResourceModel\Group\Collection;

class AddPriceListToCustomerGroupCollection
{
    public function beforeGetItems(Collection $subject): Collection
    {
        $subject->getSelect()->joinLeft( /** @phpstan-ignore-line */
            ['biemans_customer_group_pricelist' => $subject->getTable('biemans_customer_group_pricelist')],
            'main_table.customer_group_id = biemans_customer_group_pricelist.customer_group_id',
            ['pdf_file', 'excel_file']
        );

        return $subject;
    }
}
