<?php

declare(strict_types=1);

namespace Biemans\CustomerGroupPriceList\Controller\Adminhtml\Pricelist;

use <PERSON><PERSON>mans\CustomerGroupPriceList\Model\DownloadList;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface as HttpGetActionInterface;
use Magento\Framework\Controller\Result\RedirectFactory;

class Download extends Action implements HttpGetActionInterface
{
    const ADMIN_RESOURCE = 'Magento_Customer::group';

    private RedirectFactory $redirectFactory;
    private DownloadList $downloadList;

    public function __construct(
        Context $context,
        RedirectFactory $redirectFactory,
        DownloadList $downloadList
    ) {
        parent::__construct($context);
        $this->downloadList = $downloadList;
        $this->redirectFactory = $redirectFactory;
    }

    public function execute()
    {
        $groupId = (int)$this->getRequest()->getParam('id');
        $type = (string)$this->getRequest()->getParam('type');

        if ($stream = $this->downloadList->getFileStream($groupId, $type, true)) {
            return $stream;
        }

        $this->messageManager->addWarningMessage(__('Price list is not available!'));
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->redirectFactory->create();
        $resultRedirect->setPath('customer/group/edit', ['id' => $groupId]);

        return $resultRedirect;
    }
}
