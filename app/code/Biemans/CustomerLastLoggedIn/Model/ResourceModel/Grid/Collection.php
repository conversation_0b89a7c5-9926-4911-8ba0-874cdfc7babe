<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomerLastLoggedIn\Model\ResourceModel\Grid;

use Magento\Customer\Model\ResourceModel\Grid\Collection as ParentCollection;

class Collection extends ParentCollection
{
    protected function _initSelect():void
    {
        parent::_initSelect();
        $this->getSelect()->joinLeft(
            ['customer_log' => $this->getTable('customer_log')],
            'customer_log.customer_id = main_table.entity_id',
            ['last_login_at']
        );
    }
}
