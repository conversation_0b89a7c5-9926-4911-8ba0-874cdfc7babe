<?php

declare(strict_types=1);

namespace Biemans\CustomCatalog\Model\Product\Attribute\Source;

use Biemans\CustomCatalog\Model\CustomCatalogFactory;
use Magento\Store\Model\StoreManagerInterface;

class CustomCatalogs extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    private CustomCatalogFactory $customCatalogFactory;

    public function __construct(
        CustomCatalogFactory $customCatalogFactory
    ) {
        $this->customCatalogFactory = $customCatalogFactory;
    }

    /**
     * @return array<mixed>|bool|float|int|null|string
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getAllOptions()
    {
        /** @var \Biemans\CustomCatalog\Model\CustomCatalog $customCatalog */
        $customCatalog = $this->customCatalogFactory->create();
        /** @var \Biemans\CustomCatalog\Model\ResourceModel\CustomCatalog\Collection $collection */
        $collection = $customCatalog->getResourceCollection();
        $options = $collection->load()->toOptionArray();

        // Add empty value
        $options = array_merge([['value' => '0', 'label' => __('No Custom Catalog')]], $options);

        return $options;
    }
}
