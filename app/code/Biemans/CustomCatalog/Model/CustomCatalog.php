<?php

declare(strict_types=1);

namespace Biemans\CustomCatalog\Model;

use <PERSON>iemans\CustomCatalog\Api\Data\CustomCatalogInterface;
use Magento\Framework\Model\AbstractModel;

class CustomCatalog extends AbstractModel implements CustomCatalogInterface
{
    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Biemans\CustomCatalog\Model\ResourceModel\CustomCatalog::class);
    }

    /**
     * @inheritDoc
     */
    public function getCustomcatalogId()
    {
        return $this->getData(self::CUSTOMCATALOG_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCustomcatalogId($customCatalogId)
    {
        return $this->setData(self::CUSTOMCATALOG_ID, $customCatalogId);
    }

    /**
     * @inheritDoc
     */
    public function getName()
    {
        return $this->getData(self::NAME);
    }

    /**
     * @inheritDoc
     */
    public function setName($name)
    {
        return $this->setData(self::NAME, $name);
    }
    /**
     * @inheritDoc
     */
    public function getIsBspecial():string
    {
        return $this->getData(self::IS_BSPECIAL);
    }

    /**
     * @inheritDoc
     */
    public function setIsBspecial($isBspecial):self
    {
        return $this->setData(self::IS_BSPECIAL, $isBspecial);
    }
}
