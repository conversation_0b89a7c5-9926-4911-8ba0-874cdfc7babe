<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Observer;

use <PERSON><PERSON>mans\CustomCatalog\Model\Context;
use <PERSON><PERSON>mans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogsProductAttribute;
use Magento\Framework\App\Http\Context as HttpContext;
use Magento\Framework\Event\{
    Observer,
    ObserverInterface
};

class IsSalable implements ObserverInterface
{
    private HttpContext $httpContext;

    public function __construct(
        HttpContext $httpContext
    ) {
        $this->httpContext = $httpContext;
    }

    public function execute(Observer $observer)
    {
        /** @var \Magento\Catalog\Model\Product $product */
        $product = $observer->getProduct();

        if (
            !empty($product->getId())
            && !empty($productCustomCatalogs = $product->getData(AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE))
        ) {
            $productCustomCatalogs = explode(',', $productCustomCatalogs);
            $customCatalogId = $this->httpContext->getValue(Context::CONTEXT_CUSTOM_CATALOG)
                ?? Context::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE;

            $observer->getSalable()->setIsSalable(
                in_array($customCatalogId, $productCustomCatalogs)
                && $observer->getSalable()->getIsSalable()
            );
        }
    }
}
