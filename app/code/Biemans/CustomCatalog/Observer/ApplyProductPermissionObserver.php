<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Observer;

use <PERSON>iemans\CustomCatalog\Model\Context;
use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogsProductAttribute;
use Magento\Framework\App\Http\Context as HttpContext;
use Magento\Framework\Event\{
    Observer,
    ObserverInterface
};
use Magento\Framework\UrlInterface;

class ApplyProductPermissionObserver implements ObserverInterface
{
    private HttpContext $httpContext;
    private UrlInterface $url;

    public function __construct(
        HttpContext $httpContext,
        UrlInterface $url
    ) {
        $this->httpContext = $httpContext;
        $this->url = $url;
    }

    public function execute(Observer $observer)
    {
        $product = $observer->getEvent()->getProduct();
        /** @var \Magento\Framework\App\Action\Action $controller */
        $controller = $observer->getEvent()->getControllerAction();

        if (
            !empty($product->getId())
            && !empty($productCustomCatalogs = $product->getData(AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE))
        ) {
            $productCustomCatalogs = explode(',', $productCustomCatalogs);
            $customCatalogId = $this->httpContext->getValue(Context::CONTEXT_CUSTOM_CATALOG)
                ?? Context::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE;

            if (!in_array($customCatalogId, $productCustomCatalogs)) {

                $controller->getResponse()->setRedirect($this->url->getBaseUrl());  /** @phpstan-ignore-line */
            }
        }
    }
}
