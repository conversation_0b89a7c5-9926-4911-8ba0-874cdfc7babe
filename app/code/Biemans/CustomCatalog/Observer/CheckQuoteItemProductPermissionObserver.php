<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\CustomCatalog\Observer;

use <PERSON>iemans\CustomCatalog\Model\Context;
use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogsProductAttribute;
use Magento\Framework\App\Http\Context as HttpContext;
use Magento\Framework\Event\{
    Observer,
    ObserverInterface
};

class CheckQuoteItemProductPermissionObserver implements ObserverInterface
{
    private HttpContext $httpContext;

    public function __construct(
        HttpContext $httpContext
    ) {
        $this->httpContext = $httpContext;
    }

    public function execute(Observer $observer)
    {
        $quoteItem = $observer->getEvent()->getQuoteItem();
        $product = $observer->getEvent()->getProduct();

        if (
            !empty($quoteItem->getId())
            && !empty($product->getId())
            && !empty($productCustomCatalogs = $product->getData(AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE))
        ) {
            $productCustomCatalogs = explode(',', $productCustomCatalogs);
            $customCatalogId = $this->httpContext->getValue(Context::CONTEXT_CUSTOM_CATALOG)
                ?? Context::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE;

            if (!in_array($customCatalogId, $productCustomCatalogs)) {
                throw new \Magento\Framework\Exception\LocalizedException(
                    __('You cannot add "%1" to the cart.', $quoteItem->getName())
                );
            }
        }
    }
}
