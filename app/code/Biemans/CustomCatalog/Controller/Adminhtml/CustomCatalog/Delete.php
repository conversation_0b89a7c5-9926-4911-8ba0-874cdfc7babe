<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Controller\Adminhtml\CustomCatalog;

use <PERSON><PERSON><PERSON>\CustomCatalog\Api\{
    CustomCatalogRepositoryInterface,
    Data\CustomCatalogInterface
};
use Magento\Backend\App\Action\Context;

class Delete extends \Magento\Backend\App\Action
{
    const ADMIN_RESOURCE = 'Biemans_CustomCatalog::CustomCatalog_delete';

    private CustomCatalogRepositoryInterface $customCatalogRepository;

    public function __construct(
        Context $context,
        CustomCatalogRepositoryInterface $customCatalogRepository
    ) {
        $this->customCatalogRepository = $customCatalogRepository;
        parent::__construct($context);
    }

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        // Check if we know what should be deleted
        $id = $this->getRequest()->getParam(CustomCatalogInterface::CUSTOMCATALOG_ID);
        if ($id) {
            try {
                $this->customCatalogRepository->deleteById($id);

                // Display success message
                $this->messageManager->addSuccessMessage(__('You deleted the custom catalog.'));

                // Go to grid
                return $resultRedirect->setPath('*/*/');

            } catch (\Exception $e) {
                // Display error message
                $this->messageManager->addErrorMessage($e->getMessage());

                // Go back to edit form
                return $resultRedirect->setPath('*/*/edit', [CustomCatalogInterface::CUSTOMCATALOG_ID => $id]);
            }
        }
        // Display error message
        $this->messageManager->addErrorMessage(__('We can\'t find a custom catalog to delete.'));

        // Go to grid
        return $resultRedirect->setPath('*/*/');
    }
}
