<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Controller\Adminhtml\CustomCatalog;

use B<PERSON>mans\CustomCatalog\Api\{
    CustomCatalogRepositoryInterface,
    Data\CustomCatalogInterface,
    Data\CustomCatalogInterfaceFactory
};
use Magento\Backend\Model\View\Result\Page;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\View\Result\PageFactory;

class Edit extends \Magento\Backend\App\Action
{
    const ADMIN_RESOURCE = 'Biemans_CustomCatalog::CustomCatalog_update';

    private CustomCatalogRepositoryInterface $customCatalogRepository;
    private CustomCatalogInterfaceFactory $customCatalogFactory;
    private PageFactory $resultPageFactory;
    private DataPersistorInterface $dataPersistor;

    public function __construct(
        Context $context,
        CustomCatalogRepositoryInterface $customCatalogRepository,
        CustomCatalogInterfaceFactory $customCatalogFactory,
        PageFactory $resultPageFactory,
        DataPersistorInterface $dataPersistor
    ) {
        $this->customCatalogRepository = $customCatalogRepository;
        $this->customCatalogFactory = $customCatalogFactory;
        $this->resultPageFactory = $resultPageFactory;
        $this->dataPersistor = $dataPersistor;
        parent::__construct($context);
    }

    /**
     * Edit action
     *
     * @return Page|\Magento\Backend\Model\View\Result\Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        // Get ID and create model
        $id = $this->getRequest()->getParam(CustomCatalogInterface::CUSTOMCATALOG_ID);

        // Initial checking
        if (!empty($id)) {
            $model = $this->customCatalogRepository->get($id);

            if (!$model->getCustomcatalogId()) {
                $this->messageManager->addErrorMessage(__('This custom catalog no longer exists.'));
                /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
                $resultRedirect = $this->resultRedirectFactory->create();

                return $resultRedirect->setPath('*/*/');
            }
        } else {
            $model = $this->customCatalogFactory->create();
        }

        /** @var \Biemans\CustomCatalog\Model\CustomCatalog $model */
        $this->dataPersistor->set('biemans_customcatalog_customcatalog', $model->getData());

        /** @var Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            $id ? __('Edit Customcatalog') : __('New Custom Catalog'),
            $id ? __('Edit Customcatalog') : __('New Custom Catalog')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Custom Catalogs'));
        $resultPage->getConfig()->getTitle()->prepend($model->getCustomcatalogId() ?
            __('Edit Custom Catalog %1', $model->getCustomcatalogId()) :
            __('New Custom Catalog')
        );

        return $resultPage;
    }

    public function initPage(Page $resultPage): Page
    {
        $resultPage->setActiveMenu('Biemans_CustomCatalog::top_level')
            ->addBreadcrumb(__('Biemans'), __('Biemans'))
            ->addBreadcrumb(__('Custom Catalog'), __('Custom Catalog'));

        return $resultPage;
    }
}
