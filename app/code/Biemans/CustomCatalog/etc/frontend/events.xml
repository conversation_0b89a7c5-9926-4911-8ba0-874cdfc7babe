<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="catalog_product_is_salable_after">
        <observer name="Biemans_CustomCatalog_IsSalable" instance="Biemans\CustomCatalog\Observer\IsSalable" />
    </event>
    <event name="catalog_controller_product_init_after">
        <observer name="Biemans_CustomCatalog_Permissions" instance="Biemans\CustomCatalog\Observer\ApplyProductPermissionObserver"/>
    </event>
    <event name="review_controller_product_init_after">
        <observer name="Biemans_CustomCatalog_Permissions" instance="Biemans\CustomCatalog\Observer\ApplyProductPermissionObserver"/>
    </event>
    <event name="checkout_cart_product_add_after">
        <observer name="Biemans_CustomCatalog_Permissions" instance="Biemans\CustomCatalog\Observer\CheckQuoteItemProductPermissionObserver"/>
    </event>
    <event name="checkout_cart_save_before">
        <observer name="Biemans_CustomCatalog_Permissions" instance="Biemans\CustomCatalog\Observer\CheckQuotePermissionsObserver"/>
    </event>
</config>
