<?xml version="1.0"?>
<requests xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:noNamespaceSchemaLocation="urn:magento:module:Smile_ElasticsuiteCore:etc/elasticsuite_search_request.xsd">
    <request name="quick_search_container" label="Catalog Product Search" index="catalog_product" track_total_hits="true" fulltext="true">
        <filters>
            <filter name="visibleInCustomCatalog">Biemans\CustomCatalog\Model\Product\Search\Request\Container\Filter\VisibleInCustomCatalog</filter>
        </filters>
    </request>
    <request name="catalog_product_autocomplete" label="Catalog Product Autocomplete" index="catalog_product" track_total_hits="0" fulltext="true">
        <filters>
            <filter name="visibleInCustomCatalog">Biemans\CustomCatalog\Model\Product\Search\Request\Container\Filter\VisibleInCustomCatalog</filter>
        </filters>
    </request>
    <request name="catalog_view_container" label="Category Product View" index="catalog_product" track_total_hits="true" fulltext="false">
        <filters>
            <filter name="visibleInCustomCatalog">Biemans\CustomCatalog\Model\Product\Search\Request\Container\Filter\VisibleInCustomCatalog</filter>
        </filters>
    </request>
</requests>
