<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Biemans\CustomCatalog\Api\CustomCatalogRepositoryInterface" type="Biemans\CustomCatalog\Model\CustomCatalogRepository"/>
	<preference for="Biemans\CustomCatalog\Api\Data\CustomCatalogInterface" type="Biemans\CustomCatalog\Model\CustomCatalog"/>
	<preference for="Biemans\CustomCatalog\Api\Data\CustomCatalogSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="Biemans\CustomCatalog\Model\ResourceModel\CustomCatalog\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">biemans_customcatalog_customcatalog</argument>
			<argument name="resourceModel" xsi:type="string">Biemans\CustomCatalog\Model\ResourceModel\CustomCatalog\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="biemans_customcatalog_customcatalog_listing_data_source" xsi:type="string">Biemans\CustomCatalog\Model\ResourceModel\CustomCatalog\Grid\Collection</item>
			</argument>
		</arguments>
	</type>

    <type name="Magento\Framework\App\ActionInterface">
        <plugin name="biemans_customcatalog_dispatch_controller_context_plugin" type="Biemans\CustomCatalog\Plugin\App\Action\ContextPlugin"/>
    </type>
	<type name="Magento\Framework\App\Http\Context">
        <plugin name="biemans_customcatalog_cache_context_plugin" type="Biemans\CustomCatalog\Plugin\CustomCatalogCacheContextPlugin" />
	</type>
	<type name="Magento\Sitemap\Model\ResourceModel\Catalog\Product">
		<plugin name="biemans_customcatalog_filter_sitemap_collection" type="Biemans\CustomCatalog\Plugin\Sitemap\FilterCollectionPlugin" />
	</type>
	<type name="Smile\ElasticsuiteCore\Index\DataSourceResolver">
		<arguments>
			<argument name="datasources" xsi:type="array">
				<item name="catalog_product" xsi:type="array">
					<item name="customCatalogs" xsi:type="object">Biemans\CustomCatalog\Model\Product\Indexer\Fulltext\Datasource\CustomCatalogs</item>
				</item>
			</argument>
		</arguments>
	</type>
</config>
