<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/biemans-customcatalog/customcatalog" method="POST">
		<service class="Biemans\CustomCatalog\Api\CustomCatalogRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Biemans_CustomCatalog::CustomCatalog_save"/>
		</resources>
	</route>
	<route url="/V1/biemans-customcatalog/customcatalog/search" method="GET">
		<service class="Biemans\CustomCatalog\Api\CustomCatalogRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="Biemans_CustomCatalog::CustomCatalog_view"/>
		</resources>
	</route>
	<route url="/V1/biemans-customcatalog/customcatalog/:customCatalogId" method="GET">
		<service class="Biemans\CustomCatalog\Api\CustomCatalogRepositoryInterface" method="get"/>
		<resources>
			<resource ref="Biemans_CustomCatalog::CustomCatalog_view"/>
		</resources>
	</route>
	<route url="/V1/biemans-customcatalog/customcatalog/:customCatalogId" method="PUT">
		<service class="Biemans\CustomCatalog\Api\CustomCatalogRepositoryInterface" method="save"/>
		<resources>
			<resource ref="Biemans_CustomCatalog::CustomCatalog_update"/>
		</resources>
	</route>
	<route url="/V1/biemans-customcatalog/customcatalog/:customCatalogId" method="DELETE">
		<service class="Biemans\CustomCatalog\Api\CustomCatalogRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="Biemans_CustomCatalog::CustomCatalog_delete"/>
		</resources>
	</route>
</routes>
