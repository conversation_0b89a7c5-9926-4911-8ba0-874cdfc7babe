<?php

declare(strict_types=1);

namespace Biemans\CustomCatalog\ViewModel\Bspecial;

use <PERSON>iemans\CustomCatalog\Api\CustomCatalogRepositoryInterface;
use Exception;
use Magento\Customer\Model\SessionFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;
use Snowdog\Menu\Block\NodeType\CustomUrl;

/**
 * Plugin updates Menu Node with B-Special Label to have custom url navigating to the
 * search page if customer allowed to see B-Special Products (Custom Catalog with that is Bspecial)
 */
class NodeUrl implements ArgumentInterface
{
    private const BSPECIAL_URL_KEY_PART = "b-special";
    private const SEARCH_PAGE_URL = "catalogsearch/result/?q=B-Special";

    public function __construct(
        private readonly SessionFactory $customerSessionFactory,
        private readonly CustomCatalogRepositoryInterface $customCatalogRepository,
        private readonly StoreManagerInterface $storeManager
    )
    {
    }

    /**
     * 1. check if menu url has b-special
     * 2. check if customer is logged in
     * 3. check customer has custom catalog
     * 4. if yes - check if custom catalog is b-special
     *
     * @param CustomUrl $block
     * @return void
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function updateUrl(CustomUrl $block): void
    {
        $customerSession = $this->customerSessionFactory->create();
        $customer = $customerSession->getCustomer();
        $customCatalogId = $customer->getData('custom_catalog');
        if (empty($customCatalogId)) {
            return;
        }
        try {
            $customCatalog = $this->customCatalogRepository->get($customCatalogId);
        } catch (Exception $exception) {
            return;
        }
        if (!$customCatalog->getIsBspecial()) {
            return;
        }

        if (!str_contains( $block->getContent(), self::BSPECIAL_URL_KEY_PART)) {
            return;
        }
        $block->setContent($this->storeManager->getStore()->getBaseUrl() . self::SEARCH_PAGE_URL);
    }
}
