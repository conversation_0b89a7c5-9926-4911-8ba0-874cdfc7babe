<?php

declare(strict_types=1);

namespace Biemans\CustomCatalog\Api\Data;

interface CustomCatalogInterface
{
    const NAME = 'name';
    const CUSTOMCATALOG_ID = 'customcatalog_id';

    public const IS_BSPECIAL = 'is_bspecial';

    /**
     * Get customcatalog_id
     *
     * @return string|null
     */
    public function getCustomcatalogId();

    /**
     * Set customcatalog_id
     *
     * @param string $customcatalogId
     * @return CustomCatalogInterface
     */
    public function setCustomcatalogId($customcatalogId);

    /**
     * Get name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Set name
     * @param string $name
     *
     * @return CustomCatalogInterface
     */
    public function setName($name);

    /**
     * Get name
     *
     * @return string|null
     */
    public function getIsBspecial(): ?string;

    /**
     * Set IsBspecial
     * @param string $isBspecial
     * @return CustomCatalogInterface
     */
    public function setIsBspecial(string $isBspecial): self;
}
