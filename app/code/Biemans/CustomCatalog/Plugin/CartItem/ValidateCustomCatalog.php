<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Plugin\CartItem;

use <PERSON>iemans\CustomCatalog\Model\Context;
use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogsProductAttribute;
use Biemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogCustomerAttribute;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Message\ManagerInterface as MessageManagerInterface;
use Magento\Quote\Model\Quote\Item\AbstractItem;

class ValidateCustomCatalog
{
    private RequestInterface $httpRequest;
    private MessageManagerInterface $messageManager;
    private CustomerSession $customerSession;

    public function __construct(
        RequestInterface $httpRequest,
        MessageManagerInterface $messageManager,
        CustomerSession $customerSession
    ) {
        $this->httpRequest = $httpRequest;
        $this->messageManager = $messageManager;
        $this->customerSession = $customerSession;
    }

    /**
     * Validate product custom_catalogs against customer custom_catalog
     *
     * @param AbstractItem $quoteItem
     * @param AbstractItem $response
     * @return AbstractItem
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function afterCheckData(AbstractItem $quoteItem, AbstractItem $response): AbstractItem
    {
        $hasErrors = false;

        if (
            (empty($this->httpRequest->getParam('update_cart_action')) || $this->httpRequest->getParam('update_cart_action') !== 'empty_cart')
            && !empty($quoteItem->getId())
            && !empty($quoteItem->getProduct()->getId())
            && !empty($productCustomCatalogs = $quoteItem->getProduct()->getData(AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE))
        ) {
            $productCustomCatalogs = explode(',', $productCustomCatalogs);

            /** @var \Magento\Customer\Api\Data\CustomerInterface|null $customerData */
            $customerData = $this->customerSession->getCustomerData();
            if (!empty($customerData)) {
                $customCatalogAttribute = $customerData->getCustomAttribute(
                    AddCustomCatalogCustomerAttribute::ATTRIBUTE_CODE
                );
                $customCatalogId = (int)($customCatalogAttribute->getValue() ?? Context::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE);

                if (!in_array($customCatalogId, $productCustomCatalogs)) {
                    $hasErrors = true;
                    $response
                        ->setHasError(true)
                        ->setMessage(__('You cannot add "%1" to the cart.', $quoteItem->getName()));
                }
            }
        }

        // For checkout page set error flag to redirect to cart
        if (
            $hasErrors
            && $this->httpRequest->getFullActionName() === 'checkout_index_index' /** @phpstan-ignore-line */
        ) {
            $quoteItem->getQuote()->setHasError(true);
            $this->messageManager->addErrorMessage(__('You cannot add "%1" to the cart.', $quoteItem->getName()));
        }
        // For cart page only add error message and disable checkout link
        elseif ($hasErrors) {
            $quoteItem->getQuote()->setDisabledCheckout(true);
        }

        return $response;
    }
}
