<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Plugin;

use <PERSON>iemans\CustomCatalog\Model\Context as CustomCatalogContext;
use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogCustomerAttribute;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\Api\AttributeInterface;
use Magento\Framework\App\Http\Context as HttpContext;

class CustomCatalogCacheContextPlugin
{
    private Session $customerSession;

    public function __construct(Session $customerSession)
    {
        $this->customerSession = $customerSession;
    }

    /**
     * Add Customer custom_catalog cache context for caching purposes
     *
     * @param HttpContext $subject
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeGetVaryString(HttpContext $subject)
    {
        $customerData = $this->customerSession->getCustomerData();

        if (!($customerData instanceof CustomerInterface)) {
            $subject->setValue(
                CustomCatalogContext::CONTEXT_CUSTOM_CATALOG,
                CustomCatalogContext::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE,
                CustomCatalogContext::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE
            );

            return [];
        }

        $customCatalogAttribute = $customerData->getCustomAttribute(
            AddCustomCatalogCustomerAttribute::ATTRIBUTE_CODE
        );

        if (!($customCatalogAttribute instanceof AttributeInterface)) {
            $subject->setValue(
                CustomCatalogContext::CONTEXT_CUSTOM_CATALOG,
                CustomCatalogContext::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE,
                CustomCatalogContext::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE
            );

            return [];
        }

        $customCatalogId = $customCatalogAttribute->getValue();
        $subject->setValue(
            CustomCatalogContext::CONTEXT_CUSTOM_CATALOG,
            $customCatalogId,
            CustomCatalogContext::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE
        );

        return [];
    }
}
