<?php

declare(strict_types=1);

namespace <PERSON>iemans\CustomCatalog\Plugin\Sitemap;

use <PERSON>iemans\CustomCatalog\Model\Context;
use <PERSON>iemans\CustomCatalog\Setup\Patch\Data\AddCustomCatalogsProductAttribute;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Sitemap\Model\ResourceModel\Catalog\Product as SitemapProduct;

class FilterCollectionPlugin
{
    private CollectionFactory $productCollectionFactory;

    public function __construct(
        CollectionFactory $productCollectionFactory
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
    }

    /**
     * Remove restricted products from sitemap (based on custom_catalogs)
     *
     * @param SitemapProduct $subject
     * @param array<\Magento\Framework\DataObject>|null $collection
     * @return array<\Magento\Framework\DataObject>|null
     */
    public function afterGetCollection(SitemapProduct $subject, $collection)
    {
        // Get restricted products IDs (custom catalog is not null and value does not contain 0)
        $restrictedProductIds = $this->productCollectionFactory->create()
            ->addAttributeToFilter(AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE, ['notnull' => true])
            ->addAttributeToFilter(AddCustomCatalogsProductAttribute::ATTRIBUTE_CODE,
                ['nin' => [Context::CONTEXT_CUSTOM_CATALOG_DEFAULT_VALUE]]
            )
            ->getAllIds();

        if (count($restrictedProductIds)) {
            foreach ($collection as $key => $item) {
                if (in_array($item->getId(), $restrictedProductIds)) {
                    unset($collection[$key]);
                }
            }
        }

        return $collection;
    }
}
