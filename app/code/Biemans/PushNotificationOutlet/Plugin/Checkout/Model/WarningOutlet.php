<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\PushNotificationOutlet\Plugin\Checkout\Model;

use <PERSON><PERSON>mans\PushNotificationOutlet\Service\Notification;
use Magento\Catalog\Model\Product;
use Magento\Checkout\Model\Cart;
use Magento\Framework\DataObject;

/**
 * Interceptor for @see Cart
 */
class WarningOutlet
{
    public function __construct(private readonly Notification $notificationService) {}

    /**
     * Intercepted method addProduct.
     *
     * @param Cart $subject
     * @param Cart $result
     * @param int|Product $productInfo
     * @param DataObject[]|int|DataObject|null $requestInfo
     *
     * @return Cart
     * @see Cart::addProduct
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterAddProduct(Cart $subject, Cart $result, $productInfo, $requestInfo = null): Cart
    {
        $this->notificationService->push($result->getQuote()->getAllItems());

        return $result;
    }
}
