<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedHyvaCheckout\Plugin;

use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartExtensionFactory;
use Magento\Quote\Api\Data\CartExtensionInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\Data\CartSearchResultsInterface;
use Psr\Log\LoggerInterface;

class CartRepository
{
    private CartExtensionFactory $cartExtensionFactory;
    private LoggerInterface $logger;

    public function __construct(
        CartExtensionFactory $cartExtensionFactory,
        LoggerInterface $logger
    ) {
        $this->cartExtensionFactory = $cartExtensionFactory;
        $this->logger = $logger;
    }

    public function afterGet(
        CartRepositoryInterface $subject,
        CartInterface $quote
    ): CartInterface {
        $this->setOrderReference($quote);
        $this->setOrderEmail($quote);

        return $quote;
    }

    public function afterGetList(
        CartRepositoryInterface $subject,
        CartSearchResultsInterface $quoteSearchResult
    ): CartSearchResultsInterface {
        foreach ($quoteSearchResult->getItems() as $quote) {
            $this->setOrderReference($quote);
            $this->setOrderEmail($quote);
        }

        return $quoteSearchResult;
    }

    public function setOrderReference(CartInterface $quote): void
    {
        try {
            $extensionAttributes = $quote->getExtensionAttributes();

            /** @var CartExtensionInterface $target */
            $target = $extensionAttributes ?? $this->cartExtensionFactory->create();
            $target->setOrderReference($quote->getOrderReference()); /** @phpstan-ignore-line */

            $quote->setExtensionAttributes($target);
        } catch (\Exception $exception) {
            $this->logger->critical($exception->getMessage());
        }
    }

    public function setOrderEmail(CartInterface $quote): void
    {
        try {
            $extensionAttributes = $quote->getExtensionAttributes();

            /** @var CartExtensionInterface $target */
            $target = $extensionAttributes ?? $this->cartExtensionFactory->create();
            $target->setOrderEmail($quote->getOrderEmail()); /** @phpstan-ignore-line */

            $quote->setExtensionAttributes($target);
        } catch (\Exception $exception) {
            $this->logger->critical($exception->getMessage());
        }
    }

    /**
     * @return array<mixed>
     */
    public function beforeSave(CartRepositoryInterface $subject, CartInterface $quote): array
    {
        try {
            $orderReference = $quote->getExtensionAttributes()->getOrderReference();
            if (is_string($orderReference)) {
                $quote->setOrderReference($orderReference); /** @phpstan-ignore-line */
            }

            $orderEmail = $quote->getExtensionAttributes()->getOrderEmail();
            if (is_string($orderEmail)) {
                $quote->setOrderEmail($orderEmail); /** @phpstan-ignore-line */
            }
        } catch (\Exception $exception) {
            $this->logger->critical($exception->getMessage());
        }

        return [$quote];
    }
}
