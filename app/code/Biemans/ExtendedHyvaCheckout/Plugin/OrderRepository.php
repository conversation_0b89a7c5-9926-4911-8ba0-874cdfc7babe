<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedHyvaCheckout\Plugin;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderExtensionInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\Collection;

class OrderRepository
{
    private OrderExtensionFactory $extensionFactory;

    public function __construct(
        OrderExtensionFactory $extensionFactory
    ) {
        $this->extensionFactory = $extensionFactory;
    }

    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $resultOrder
    ): OrderInterface {
        $orderExtensions = $resultOrder->getExtensionAttributes() ? $resultOrder->getExtensionAttributes() :
            $this->extensionFactory->create();
        $orderExtensions->setOrderReference($resultOrder->getData('order_reference')); /** @phpstan-ignore-line */
        $orderExtensions->setOrderEmail($resultOrder->getData('order_email')); /** @phpstan-ignore-line */
        $resultOrder->setExtensionAttributes($orderExtensions);

        return $resultOrder;
    }

    /**
     * @return Collection<OrderInterface>
     */
    public function afterGetList(
        OrderRepositoryInterface $subject,
        Collection $resultOrder
    ) {
        /** @var OrderInterface $order */
        foreach ($resultOrder->getItems() as $order) {
            $this->afterGet($subject, $order);
        }
        return $resultOrder;
    }
}
