<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\ExtendedHyvaCheckout\Plugin;

use Hyva\Checkout\Magewire\Checkout\AddressView\MagewireAddressFormInterface;

class TriggerBrowserEventOnAddressSave
{
    public function afterSave(MagewireAddressFormInterface $subject, bool $result): bool
    {
        /** @phpstan-ignore-next-line */
        $subject->dispatchBrowserEvent('checkout:address:form:save', ['save' => $result]);

        return $result;
    }
}
