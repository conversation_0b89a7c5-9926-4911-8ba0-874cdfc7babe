<?php

declare(strict_types=1);

namespace Biemans\ExtendedHyvaCheckout\Plugin;

use Hyva\Checkout\Magewire\Checkout\Shipping\MethodList;
use Magento\Checkout\Model\Session as SessionCheckout;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Model\ShippingMethodManagementInterface;

class ResetShippingMethod
{
    private SessionCheckout $sessionCheckout;
    private ShippingMethodManagementInterface $shippingMethodManagement;

    public function __construct(
        SessionCheckout $sessionCheckout,
        ShippingMethodManagementInterface $shippingMethodManagement
    ) {
        $this->sessionCheckout = $sessionCheckout;
        $this->shippingMethodManagement = $shippingMethodManagement;
    }

    public function aroundUpdatedMethod(MethodList $subject, callable $proceed, string $value): string
    {
        if ($value === 'reset') {
            try {
                $quote = $this->sessionCheckout->getQuote();

                if ($this->shippingMethodManagement->set($quote->getId(), '', '')) {
                    $subject->dispatchBrowserEvent('checkout:shipping:method-activate', ['method' => $value]);
                    $subject->emit('shipping_method_selected');
                }

                return $value;
            } catch (LocalizedException $exception) {
                $subject->dispatchErrorMessage('Something went wrong while saving your shipping preferences.');
            }
        }

        return $proceed($value);
    }
}
