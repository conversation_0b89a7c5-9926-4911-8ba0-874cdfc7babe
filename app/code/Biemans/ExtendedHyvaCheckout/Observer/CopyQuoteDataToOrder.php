<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedHyvaCheckout\Observer;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;

class CopyQuoteDataToOrder implements ObserverInterface
{
    public function execute(EventObserver $observer)
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $observer->getOrder();
        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $observer->getQuote();

        if (!empty($reference = $quote->getData('order_reference'))) {
            $order->setData('order_reference', $reference);
        }

        if (!empty($email = $quote->getData('order_email'))) {
            $order->setData('order_email', $email);
        }
    }
}
