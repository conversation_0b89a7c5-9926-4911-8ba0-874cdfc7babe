<?php

declare(strict_types=1);

namespace Biemans\ExtendedHyvaCheckout\Magewire\Checkout;

use Hyva\Checkout\Model\Magewire\Component\EvaluationInterface;
use Hyva\Checkout\Model\Magewire\Component\EvaluationResultFactory;
use Hyva\Checkout\Model\Magewire\Component\EvaluationResultInterface;
use Magento\Checkout\Model\Session as SessionCheckout;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Validator\EmailAddress as EmailValidator;
use Magento\Quote\Api\CartRepositoryInterface;
use Magewirephp\Magewire\Component;

class OrderEmail extends Component implements EvaluationInterface
{
    public ?string $email = null;

    private SessionCheckout $sessionCheckout;
    private CartRepositoryInterface $quoteRepository;
    private EmailValidator $emailValidator;

    public function __construct(
        SessionCheckout $sessionCheckout,
        CartRepositoryInterface $quoteRepository,
        EmailValidator $emailValidator
    ) {
        $this->sessionCheckout = $sessionCheckout;
        $this->quoteRepository = $quoteRepository;
        $this->emailValidator = $emailValidator;
    }

    /**
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function mount(): void
    {
        $quote = $this->sessionCheckout->getQuote();
        $this->email = $quote->getExtensionAttributes()->getOrderEmail(); /** @phpstan-ignore-line */
        if (!$this->email) {
            $this->email = $quote->getCustomer()->getEmail(); /** @phpstan-ignore-line */
        }
    }

    public function updatingEmail(string $value): string
    {
        if (empty($value) || $this->emailValidator->isValid($value)) {
            try {
                $quote = $this->sessionCheckout->getQuote();
                $quote->getExtensionAttributes()->setOrderEmail($value);
                /** @phpstan-ignore-line */
                $this->quoteRepository->save($quote);
            } catch (LocalizedException|\Exception $exception) {
                $this->dispatchErrorMessage('Something went wrong while saving your order email. Please try again.');
            }
        } else {
            $this->dispatchErrorMessage('Email address is not valid. Please type in a correct address.');
        }

        return $value;
    }

    public function evaluateCompletion(EvaluationResultFactory $resultFactory): EvaluationResultInterface
    {
        if (empty($this->email)) {
            return /** @phpstan-ignore-line */ $resultFactory->createErrorMessageEvent()
                ->withCustomEvent('order:email:error')
                ->withMessage(__('Email is required.')->render());
        }

        return $resultFactory->createSuccess();
    }
}
