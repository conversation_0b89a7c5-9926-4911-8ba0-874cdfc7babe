<?xml version="1.0" encoding="UTF-8" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Search\Model\AutocompleteInterface">
        <plugin name="remove_prices" type="Biemans\ExtendedElasticSearch\Plugin\AutocompleteRemovePrices" />
    </type>
    <type name="Smile\ElasticsuiteCatalog\Model\Autocomplete\Product\ItemFactory">
        <plugin name="add_sku" type="B<PERSON>mans\ExtendedElasticSearch\Plugin\AutocompleteAddSku" />
    </type>
    <type name="Smile\ElasticsuiteCatalog\Helper\AbstractAttribute">
        <plugin
            name="fix_attribute_options_values_map"
            type="Biemans\ExtendedElasticSearch\Plugin\FixOptionsValuesMapping"
        />
    </type>
</config>
