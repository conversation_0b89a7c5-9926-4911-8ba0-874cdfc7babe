<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedElasticSearch\Plugin;

use Smile\ElasticsuiteCatalog\Helper\AbstractAttribute;

/**
 * For product attributes with custom source model, the values are not mapped correctly and an error is thrown when
 * reindex is run:
 * Warning: Array to string conversion in vendor/smile/elasticsuite/src/module-elasticsuite-catalog/Helper/AbstractAttribute.php on line 274
 *
 * Values are mapped wrong: i.e.
 * array(1) {
 *   [0] => array(2) {
 *     ["value"] => string(2) "11"
 *     ["label"] => string(11) "CC_BSPECIAL"
 *   }
 * }
 *
 * Result should be:
 * array(1) {
 *   [0] => string(11) "CC_BSPECIAL"
 * }
 */
class FixOptionsValuesMapping
{
    /**
     * @see AbstractAttribute::getIndexOptionsText()
     *
     * @param array<mixed> $optionValues
     * @return array<mixed>
     */
    public function afterGetIndexOptionsText(
        AbstractAttribute $subject,
        array $optionValues
    ) {
        foreach ($optionValues as &$optionValue) {
            if (is_array($optionValue)) {
                $optionValue = $optionValue['label'] ?? '';
            }
        }

        return $optionValues;
    }
}
