<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedElasticSearch\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class RemoveFilterCoverageLimitation implements DataPatchInterface
{
    private ModuleDataSetupInterface $moduleDataSetup;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->update(
            $this->moduleDataSetup->getTable('catalog_eav_attribute'),
            [
                // Remove display of filter attributes when at least 90% products have it
                'facet_min_coverage_rate' => 0,
                // Show 50 filter options (if there are more, 'Show more' appears). Default = 10
                'facet_max_size' => 50
            ]
        );

        return $this;
    }

    public function getAliases()
    {
        return [];
    }

    public static function getDependencies()
    {
        return [];
    }
}
