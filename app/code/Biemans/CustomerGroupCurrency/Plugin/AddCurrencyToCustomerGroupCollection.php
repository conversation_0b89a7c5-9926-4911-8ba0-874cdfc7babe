<?php

namespace <PERSON><PERSON>mans\CustomerGroupCurrency\Plugin;

use Magento\Customer\Model\ResourceModel\Group\Collection;

class AddCurrencyToCustomerGroupCollection
{
    public function beforeGetItems(Collection $subject): Collection
    {
        $subject->getSelect()->joinLeft( /** @phpstan-ignore-line */
            ['biemans_customergroupcurrency_currency' => 'biemans_customergroupcurrency_currency'],
            "main_table.customer_group_id = biemans_customergroupcurrency_currency.customer_group_id",
            "currency"
        );

        return $subject;
    }
}
