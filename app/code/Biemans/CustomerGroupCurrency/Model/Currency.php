<?php

/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\CustomerGroupCurrency\Model;

use <PERSON>iemans\CustomerGroupCurrency\Api\Data\CurrencyInterface;
use Magento\Framework\Model\AbstractModel;

class Currency extends AbstractModel implements CurrencyInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Biemans\CustomerGroupCurrency\Model\ResourceModel\Currency::class);
    }

    /**
     * @inheritDoc
     */
    public function getCurrencyId()
    {
        return $this->getData(self::CURRENCY_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCurrencyId($currencyId)
    {
        return $this->setData(self::CURRENCY_ID, $currencyId);
    }

    /**
     * @inheritDoc
     */
    public function getCurrency()
    {
        return $this->getData(self::CURRENCY);
    }

    /**
     * @inheritDoc
     */
    public function setCurrency($currency)
    {
        return $this->setData(self::CURRENCY, $currency);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerGroupId()
    {
        return $this->getData(self::CUSTOMER_GROUP_ID);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerGroupId($customerGroupId)
    {
        return $this->setData(self::CUSTOMER_GROUP_ID, $customerGroupId);
    }
}
