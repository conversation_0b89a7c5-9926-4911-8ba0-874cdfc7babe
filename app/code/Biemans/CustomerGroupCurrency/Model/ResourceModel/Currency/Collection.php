<?php

/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace <PERSON><PERSON>mans\CustomerGroupCurrency\Model\ResourceModel\Currency;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'currency_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Biemans\CustomerGroupCurrency\Model\Currency::class,
            \Biemans\CustomerGroupCurrency\Model\ResourceModel\Currency::class
        );
    }
}
