<?php

/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\CustomerGroupCurrency\Model;

use <PERSON>iemans\CustomerGroupCurrency\Api\CurrencyRepositoryInterface;
use <PERSON><PERSON>mans\CustomerGroupCurrency\Api\Data\CurrencyInterface;
use Biemans\CustomerGroupCurrency\Api\Data\CurrencyInterfaceFactory;
use Biemans\CustomerGroupCurrency\Api\Data\CurrencySearchResultsInterfaceFactory;
use Biemans\CustomerGroupCurrency\Model\ResourceModel\Currency as ResourceCurrency;
use Biemans\CustomerGroupCurrency\Model\ResourceModel\Currency\CollectionFactory as CurrencyCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class CurrencyRepository implements CurrencyRepositoryInterface
{

    protected CollectionProcessorInterface $collectionProcessor;
    protected ResourceCurrency $resource;
    protected CurrencyInterfaceFactory $currencyFactory;
    protected CurrencySearchResultsInterfaceFactory $searchResultsFactory;
    protected CurrencyCollectionFactory $currencyCollectionFactory;


    /**
     * @param ResourceCurrency $resource
     * @param CurrencyInterfaceFactory $currencyFactory
     * @param CurrencyCollectionFactory $currencyCollectionFactory
     * @param CurrencySearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceCurrency $resource,
        CurrencyInterfaceFactory $currencyFactory,
        CurrencyCollectionFactory $currencyCollectionFactory,
        CurrencySearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->currencyFactory = $currencyFactory;
        $this->currencyCollectionFactory = $currencyCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(CurrencyInterface $currency): CurrencyInterface
    {
        try {
            /** @var \Biemans\CustomerGroupCurrency\Model\Currency $currency */
            $this->resource->save($currency);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the currency: %1',
                $exception->getMessage()
            ));
        }
        return $currency;
    }

    /**
     * @inheritDoc
     */
    public function get($currencyId)
    {
        /** @var \Biemans\CustomerGroupCurrency\Model\Currency $currency */
        $currency = $this->currencyFactory->create();
        $this->resource->load($currency, $currencyId);
        if (!$currency->getId()) {
            throw new NoSuchEntityException(__('Currency with id "%1" does not exist.', $currencyId));
        }
        return $currency;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->currencyCollectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(CurrencyInterface $currency)
    {
        try {
            /** @var \Biemans\CustomerGroupCurrency\Model\Currency $currencyModel */
            $currencyModel = $this->currencyFactory->create();
            $this->resource->load($currencyModel, $currency->getCurrencyId());
            $this->resource->delete($currencyModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Currency: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($currencyId)
    {
        return $this->delete($this->get($currencyId));
    }
}
