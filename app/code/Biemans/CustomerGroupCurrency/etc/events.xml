<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="currency_display_options_forming">
        <observer name="magento_currencysymbol_currency_display_options" disabled="true" />
        <observer name="biemans_customergroupcurrency_currency_display_options" instance="Biemans\CustomerGroupCurrency\Observer\CurrencyDisplayOptions" />
    </event>
    <event name="sales_order_place_after">
        <observer name="Biemans_CustomerGroupCurrency_Set_Currency_Code" instance="Biemans\CustomerGroupCurrency\Observer\SetCurrencyCodeOnOrder" />
    </event>
</config>
