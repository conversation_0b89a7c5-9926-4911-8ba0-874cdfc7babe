<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="biemans_customergroupcurrency_currency" resource="default" engine="innodb" comment="biemans_customergroupcurrency_currency Table">
		<column xsi:type="int" name="currency_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="currency_id"/>
		</constraint>
		<column name="currency" nullable="true" xsi:type="varchar" comment="currency" length="255"/>
		<column name="customer_group_id" nullable="true" xsi:type="int" comment="customer_group_id" identity="false"/>
	</table>
</schema>
