<?php

declare(strict_types=1);

namespace Biemans\DepotShipment\Observer;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;

class CopyQuoteDataToOrder implements ObserverInterface
{
    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    public function execute(EventObserver $observer)
    {
        try {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $observer->getOrder();
            /** @var \Magento\Quote\Model\Quote $quote */
            $quote = $observer->getQuote();

            if (
                !empty($depot = $quote->getData('shipping_depot'))
                && ($quote->getShippingAddress()->getShippingMethod() === 'depot_depot')
            ) {
                $order->setData('shipping_depot', $depot);
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_DepotShipment: ' . $e->getMessage());
        }
    }
}
