<?php

declare(strict_types=1);

namespace Biemans\DepotShipment\ViewModel;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;

class DepotOptions implements ArgumentInterface
{
    const PATH_DEPOT_OPTIONS = 'carriers/depot/depots';

    private ScopeConfigInterface $scopeConfig;
    private SerializerInterface $serializer;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        SerializerInterface $serializer
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->serializer = $serializer;
    }

    /**
     * @return string[]
     */
    public function getDepotOptions(string $scopeCode = null): array
    {
        $value = $this->getValue(self::PATH_DEPOT_OPTIONS, $scopeCode);

        try {
            $options = (array)$this->serializer->unserialize($value);
        } catch (\Throwable $e) {
            $options = [];
        }

        $options = array_map(function ($option) {
            return trim($option['value']);
        }, $options);

        return array_values($options);
    }

    /**
     * @return mixed
     */
    protected function getValue(string $path, string $scopeCode = null, string $scopeType = ScopeInterface::SCOPE_STORES)
    {
        return $this->scopeConfig->getValue($path, $scopeType, $scopeCode);
    }
}
