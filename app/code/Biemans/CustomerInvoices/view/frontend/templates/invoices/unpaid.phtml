<?php

declare(strict_types=1);

use Biemans\CustomerInvoices\Block\Invoices;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Helper\SecureHtmlRenderer;

/** @var Invoices $block */
/** @var Escaper $escaper */
/** @var SecureHtmlRenderer $secureRenderer */
/** @var ViewModelRegistry $viewModels */
$invoices = $block->getCurrentPageInvoices();
/** @var \Magento\Framework\Pricing\Helper\Data $priceHelper */
$priceHelper = $this->helper(\Magento\Framework\Pricing\Helper\Data::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<?php if ($invoices && count($invoices)) : ?>
    <div class="flex flex-col items-end">
        <button onClick="window.print()" class="mb-4 border-current btn btn-ghost btn-with-icon print-button">
            <?= $escaper->escapeHtml(__('Print')) ?>
            <?= $heroicons->printerHtml('w-4 h-4 !transition-none icon') ?>
        </button>
        <div class="w-full prose max-w-none">
            <table class="table-responsive" aria-labeledny="payments-due">
                <thead>
                    <tr class="border-b border-b-secondary-500">
                        <th scope="col" class="py-1 pr-1">
                            <?= $escaper->escapeHtml(__('Invoice number')); /** @phpstan-ignore-line */ ?>
                        </th>
                        <th scope="col" class="py-1 pr-1">
                            <?= $escaper->escapeHtml(__('Invoice date')); /** @phpstan-ignore-line */ ?>
                        </th>
                        <th scope="col" class="py-1 pr-1">
                            <?= $escaper->escapeHtml(__('Days open')); /** @phpstan-ignore-line */ ?>
                        </th>
                        <th scope="col" class="py-1 pr-1">
                            <?= $escaper->escapeHtml(__('Price')); /** @phpstan-ignore-line */ ?>
                        </th>
                        <th scope="col" class="py-1 pr-1">
                            <?= $escaper->escapeHtml(__('Expire')); /** @phpstan-ignore-line */ ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($invoices as $invoice): ?>
                        <tr class="border-b border-b-secondary-500">
                            <td data-label="<?= $escaper->escapeHtmlAttr(__('Invoice number')); ?>" class="py-1 pr-1">
                                <?= $escaper->escapeHtml($invoice->getData('invoiceNumberShort') ?? $invoice->getData('InvoiceNumber')); /** @phpstan-ignore-line */ ?>
                            </td>
                            <td data-label="<?= $escaper->escapeHtmlAttr(__('Invoice date')); ?>" class="py-1 pr-1">
                                <?= $escaper->escapeHtml($block->formatDate($invoice->getData('invoiceDate'))); /** @phpstan-ignore-line */ ?>
                            </td>
                            <td data-label="<?= $escaper->escapeHtmlAttr(__('Days open')); ?>"
                                class="py-1 pr-1
                                <?php if ((int)$invoice->getData('daysOpen') < 0) : ?>
                                text-primary
                                <?php endif ?>
                                ">
                                <?= (int)$invoice->getData('daysOpen'); ?>
                            </td>
                            <td data-label="<?= $escaper->escapeHtmlAttr(__('Price')); ?>" class="py-1 pr-1">
                                <?= $escaper->escapeHtml($block->formatPrice($invoice->getData('amount'))); /** @phpstan-ignore-line */ ?>
                            </td>
                            <td data-label="<?= $escaper->escapeHtmlAttr(__('Expire')); ?>" class="py-1 pr-1">
                                <?= $escaper->escapeHtml($block->formatDate($invoice->getData('dueDate'))); /** @phpstan-ignore-line */ ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if ($block->getPagerHtml()) : ?>
                <div class="toolbar bottom"><?= $block->getPagerHtml() ?></div>
            <?php endif ?>
        </div>
    </div>
<?php else: ?>
    <div class="py-8 text-xl"><span><?= $escaper->escapeHtml(__('You have no invoices.')); /** @phpstan-ignore-line */ ?></span></div>
<?php endif;
