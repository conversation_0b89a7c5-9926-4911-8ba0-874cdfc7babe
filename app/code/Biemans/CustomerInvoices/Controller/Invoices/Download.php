<?php

declare(strict_types=1);

namespace Biemans\CustomerInvoices\Controller\Invoices;

use Biemans\CustomerInvoices\Controller\InvoicesManagement;
use Biemans\CustomerInvoices\Model\Invoices;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Controller\Result\RedirectFactory;
use Psr\Log\LoggerInterface;

class Download extends InvoicesManagement
{
    private RedirectFactory $redirectFactory;
    private Invoices $invoiceModel;
    private FileFactory $fileFactory;
    private LoggerInterface $logger;

    public function __construct(
        Context $context,
        Session $customerSession,
        RedirectFactory $redirectFactory,
        Invoices $invoiceModel,
        FileFactory $fileFactory,
        LoggerInterface $logger
    ) {
        parent::__construct($context, $customerSession);
        $this->redirectFactory = $redirectFactory;
        $this->invoiceModel = $invoiceModel;
        $this->fileFactory = $fileFactory;
        $this->logger = $logger;
    }

    /**
     * @return ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        try {
            if (
                !empty($invoiceId = $this->getRequest()->getParam('invoice_id'))
                && $this->invoiceModel->canDownload($invoiceId)
                && !empty($content = $this->invoiceModel->getInvoicePdf($invoiceId))
            ) {
                return $this->fileFactory->create(
                    sprintf('invoice_%s.pdf', $invoiceId),
                    [
                        'type' => 'string',
                        'value' => $content,
                        'rm' => true
                    ],
                    DirectoryList::VAR_DIR,
                    'application/pdf'
                );
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_CustomerInvoices: ' . $e->getMessage());
        }

        $this->messageManager->addWarningMessage(__('Invoice is not available or you have no access to download it!'));
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->redirectFactory->create();
        $resultRedirect->setPath('biemans/invoices/paid');

        return $resultRedirect;
    }
}
