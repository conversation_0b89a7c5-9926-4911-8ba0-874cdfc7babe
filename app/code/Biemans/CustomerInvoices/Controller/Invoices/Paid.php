<?php

declare(strict_types=1);

namespace Biemans\CustomerInvoices\Controller\Invoices;

use Biemans\CustomerInvoices\Controller\InvoicesManagement;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\View\Result\PageFactory;

class Paid extends InvoicesManagement
{
    private PageFactory $pageFactory;

    public function __construct(
        Context $context,
        Session $customerSession,
        PageFactory $pageFactory
    ) {
        parent::__construct($context, $customerSession);
        $this->pageFactory = $pageFactory;
    }

    /**
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        $resultPage = $this->pageFactory->create();
        $resultPage->getConfig()->getTitle()->set(__('Invoices'));

        return $resultPage;
    }
}
