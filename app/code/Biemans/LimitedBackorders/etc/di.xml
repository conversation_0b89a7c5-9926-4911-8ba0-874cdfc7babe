<?xml version="1.0" encoding="UTF-8" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogInventory\Model\ResourceModel\QtyCounterInterface">
        <plugin name="process_inventory_after_sale_registration" type="Biemans\LimitedBackorders\Plugin\ProcessInventoryAfterSale" />
    </type>
    <type name="Magento\CatalogInventory\Model\StockManagement">
        <plugin name="process_indexer_inventory_after_sale_registration" type="Biemans\LimitedBackorders\Plugin\ProcessIndexerInventoryAfterSale" />
    </type>
</config>
