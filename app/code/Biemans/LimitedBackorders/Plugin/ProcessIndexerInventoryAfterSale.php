<?php

declare(strict_types=1);

namespace Biemans\LimitedBackorders\Plugin;

use Magento\CatalogInventory\Api\Data\StockItemInterface;
use Magento\CatalogInventory\Model\StockManagement;
use Psr\Log\LoggerInterface;

class ProcessIndexerInventoryAfterSale
{
    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * @param StockManagement $subject
     * @param StockItemInterface[] $fullSaveItems
     * @return StockItemInterface[]
     */
    public function afterRegisterProductsSale(StockManagement $subject, array $fullSaveItems): array
    {
        try {
            foreach ($fullSaveItems as &$stockItem) {
                if (
                    $stockItem->getQty() <= 0
                    && $stockItem->getIsInStock()
                ) {
                    $stockItem->setIsInStock(false);
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_LimitedBackorders: ' . $e->getMessage());
        }

        return $fullSaveItems;
    }
}
