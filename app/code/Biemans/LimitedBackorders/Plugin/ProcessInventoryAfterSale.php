<?php

declare(strict_types=1);

namespace Biemans\LimitedBackorders\Plugin;

use Magento\CatalogInventory\Model\ResourceModel\QtyCounterInterface;
use Psr\Log\LoggerInterface;

class ProcessInventoryAfterSale
{
    private LoggerInterface $logger;

    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * @param QtyCounterInterface $subject
     * @param null $result
     * @param array<mixed> $items
     * @param int $websiteId
     * @param string $operator
     * @return void
     */
    public function afterCorrectItemsQty(QtyCounterInterface $subject, $result, array $items, $websiteId, $operator)
    {
        if (empty($items)) {
            return;
        }

        try {
            /** @var \Magento\CatalogInventory\Model\ResourceModel\Stock $subject */
            $connection = $subject->getConnection();

            if (!$connection) {
                return;
            }

            /**
             * @see \Magento\CatalogInventory\Model\StockManagement::registerProductsSale()
             */
            if ($operator === '-') {
                $where = [
                    'product_id IN (?)' => array_keys($items),
                    'website_id = ?' => $websiteId,
                    'qty <= ?' => 0,
                    'is_in_stock = ?' => 1
                ];

                $connection->update(
                    $subject->getTable('cataloginventory_stock_item'),
                    [
                        'is_in_stock' => 0
                    ],
                    $where
                );
            }
            /**
             * Revert sale
             * @see \Magento\CatalogInventory\Model\StockManagement::revertProductsSale()
             */
            elseif ($operator === '+') {
                $where = [
                    'product_id IN (?)' => array_keys($items),
                    'website_id = ?' => $websiteId,
                    'qty > ?' => 0,
                    'is_in_stock = ?' => 0
                ];

                $connection->update(
                    $subject->getTable('cataloginventory_stock_item'),
                    [
                        'is_in_stock' => 1
                    ],
                    $where
                );
            }
        } catch (\Exception $e) {
            $this->logger->critical('Biemans_LimitedBackorders: ' . $e->getMessage());
        }

        return;
    }
}
