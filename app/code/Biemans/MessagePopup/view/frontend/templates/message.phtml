<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Biemans\MessagePopup\ViewModel\Message;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Template $block */

/** @var Message $messageDataProvider */
$messageDataProvider = $viewModels->require(Message::class);

if (!empty($message = $messageDataProvider->getContent())): ?>
    <?= /** @phpstan-ignore-line */ $block->fetchView((string)$block->getTemplateFile('Biemans_MessagePopup::message-ui.phtml')) ?>
    <div
        x-data="processPopup($el, {
            cookieName: '<?= $escaper->escapeHtml($messageDataProvider->getCookieName()); ?>',
            buttonLink: '<?= $escaper->escapeHtml($messageDataProvider->getButtonLink()); ?>',
        })"
        x-show="open"
        x-transition
        x-init="init()"
        style="display: none;"
    >
        <div
            class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50"
        >
            <div
                class="relative bg-white rounded-lg shadow-lg p-6 max-w-2xl"
            >
                <button
                    @click="open = false"
                    class="absolute top-2 right-2 text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
                <div class="text-sm">
                    <?= $message; ?>
                </div>
                <?php if (!empty ($buttonText = $messageDataProvider->getButtonText())): ?>
                    <div class="flex justify-center mt-4">
                        <button
                            class="btn btn-primary px-4 py-4"
                            @click="window.location.href = buttonLink"
                        >
                            <?= $escaper->escapeHtml($buttonText); ?>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif;
