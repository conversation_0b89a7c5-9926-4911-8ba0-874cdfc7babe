<script>
    function processPopup(element, data) {
        return {
            element: element,
            cookieName: data.cookieName ? data.cookieName : '',
            buttonLink: data.buttonLink ? data.buttonLink : '',
            open: false,

            init() {
                let self = this;
                this.showMessage()
            },

            showMessage() {
                if (!this.isCookieSet()) {
                    this.open = true;
                    this.setCookie();
                }
            },

            setCookie() {
                if (!this.isCookieSet()) {
                    let d = new Date();
                    d.setTime(d.getTime() + 315360000000); // 10 years
                    let expires = "expires=" + d.toUTCString();
                    document.cookie = this.cookieName + "=1;" + expires + ";path=/";
                }
            },

            isCookieSet() {
                let name = this.cookieName + "=";
                let decodedCookie = decodeURIComponent(document.cookie);
                let ca = decodedCookie.split(';');
                for(let i = 0; i <ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) == ' ') {
                        c = c.substring(1);
                    }
                    if (c.indexOf(name) == 0) {
                        return c.substring(name.length, c.length);
                    }
                }

                return false;
            }
        }
    }
</script>
