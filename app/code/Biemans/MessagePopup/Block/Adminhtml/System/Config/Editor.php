<?php

declare(strict_types=1);

namespace <PERSON>iemans\MessagePopup\Block\Adminhtml\System\Config;

use Magento\Backend\Block\Template\Context;
use Magento\Cms\Model\Wysiwyg\Config as WysiwygConfig;
use Magento\Config\Block\System\Config\Form\Field;
use Magento\Framework\Data\Form\Element\AbstractElement;

class Editor extends Field
{

    /**
     * @param mixed[] $data
     */
    public function __construct(
        protected readonly WysiwygConfig $wysiwygConfig,
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    protected function _getElementHtml(AbstractElement $element): string
    {
        // Set wysiwyg for element
        $element->setWysiwyg(true);
        // Set configuration values
        $element->setConfig($this->wysiwygConfig->getConfig($element));

        return parent::_getElementHtml($element);
    }
}
