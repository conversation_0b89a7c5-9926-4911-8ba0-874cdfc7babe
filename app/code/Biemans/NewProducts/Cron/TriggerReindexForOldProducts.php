<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\NewProducts\Cron;

use <PERSON>iemans\NewProducts\Model\Config\Provider;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\CatalogSearch\Model\Indexer\Fulltext\Processor;
use Magento\Framework\Exception\LocalizedException;

class TriggerReindexForOldProducts
{

    public function __construct(
        private readonly CollectionFactory $productCollectionFactory,
        private readonly Processor $productIndexer,
        private readonly Provider $configProvider
    ) {
    }

    public function execute(): void
    {
        /**
         * Invalidate reindex for products based on news_from_date with defined period buffer, used to display new label,
         * and prevent adding it to cart
         */
        $this->invalidateByNewsFromDate(
            date('Y-m-d H:i:s', strtotime("-3 days", (int)strtotime("+" .
                $this->configProvider->getDateBuffer()))),
            date('Y-m-d H:i:s', (int)strtotime("+" . $this->configProvider->getDateBuffer()))
        );

        // Invalidate reindex for products based on news_from_date with 3 days buffer
        $this->invalidateByNewsFromDate(
            date('Y-m-d H:i:s', strtotime('-3 days')),
            date('Y-m-d H:i:s')
        );

        $this->invalidateByNewsToDate();
    }

    /**
     * @param string $bufferDate
     * @param string $date
     * @return void
     */
    protected function invalidateByNewsFromDate(string $bufferDate, string $date): void
    {
        $productCollection = $this->productCollectionFactory->create()
            ->addFieldToFilter(
                'news_from_date',
                ['gt' => $bufferDate]
            )
            ->addFieldToFilter(
                'news_from_date',
                ['lteq' => $date]
            );

        $this->invalidateReindex($productCollection->getAllIds());
    }

    /**
     * @return void
     * @throws LocalizedException
     */
    protected function invalidateByNewsToDate(): void
    {
        // Take products past 3 days, in case previous cron job failed
        $productCollection = $this->productCollectionFactory->create()
            ->addFieldToFilter(
                'news_to_date',
                ['lt' => date('Y-m-d H:i:s')]
            )
            ->addFieldToFilter(
                'news_to_date',
                ['gteq' => date('Y-m-d H:i:s', strtotime('-3 day'))]
            );

        $this->invalidateReindex($productCollection->getAllIds());
    }

    /**
     * @param int[] $ids
     */
    private function invalidateReindex(array $ids): void
    {
        if (count($ids)) {
            $this->productIndexer->reindexList($ids, true);
        }
    }
}
