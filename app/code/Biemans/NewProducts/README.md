# Biemans NewProducts

### Updating Products to be New.

Module contains logic that mark products as New for Elasticsearch if set new from new to is not more defined in Config DateTime Period.
Additionally Collection Attribute is set to New, that makes possible to filter the catalog with this value.

### Features
1. <PERSON>ron is executed ones per day to update products as New (Elasticsearch and Collection Attribute).
2. CLI command to manually trigger logic

> ```biemans:new:update```
