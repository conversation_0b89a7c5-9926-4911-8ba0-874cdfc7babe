<?xml version="1.0"?>
<config
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd"
>
    <type name="Smile\ElasticsuiteCatalogRule\Model\Rule\Condition\Product\SpecialAttributesProvider">
        <arguments>
            <argument name="attributes" xsi:type="array">
                <item name="is_new_from" xsi:type="object">
                    Biemans\NewProducts\Model\Rule\Condition\Product\SpecialAttribute\IsNewFrom
                </item>
                <item name="is_new_to" xsi:type="object">
                    Biemans\NewProducts\Model\Rule\Condition\Product\SpecialAttribute\IsNewTo
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Smile\ElasticsuiteCatalog\Helper\AbstractAttribute">
        <plugin name="biemans_add_is_new_flag" type="<PERSON>iemans\NewProducts\Plugin\AddIsNewFlagToElasticSearch" />
    </type>

</config>
