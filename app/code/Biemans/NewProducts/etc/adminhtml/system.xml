<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="biemans" translate="label" sortOrder="10010">
            <label><![CDATA[B<PERSON>mans]]></label>
        </tab>
        <section id="biemans_new_products" type="text" translate="label" sortOrder="20" showInDefault="1"
                 showInWebsite="0" showInStore="0">
            <label>New Products</label>
            <tab>biemans</tab>
            <resource>Biemans_PdfCatalog::system_config</resource>
            <group id="general" type="text" translate="label" sortOrder="10" showInDefault="1" showInWebsite="0"
                   showInStore="0">
                <label>Ordering Settings</label>
                <field id="date_buffer" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <label>DateTime Buffer for the New Products</label>
                    <comment>Use acceptable for strtotime phrase. Ex: 150 days, 1 month. This buffer will be used to set products as Released and New.</comment>
                </field>
            </group>
            <group id="order" type="text" translate="label" sortOrder="10" showInDefault="1" showInWebsite="0"
                   showInStore="0">
                <label>Ordering Settings</label>
                <field id="max_items" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <label>Max Items Allowed per Product</label>
                </field>
            </group>
        </section>
    </system>
</config>
