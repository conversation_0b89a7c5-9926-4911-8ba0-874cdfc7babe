<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
      extends="pagebuilder_base_form_with_background_video">
    <fieldset name="background">
        <field name="color_theme" sortOrder="200" formElement="select"
               component="Magento_PageBuilder/js/form/element/select-filtered-options">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Color Theme</label>
                <dataScope>color_theme</dataScope>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Biemans\PageBuilderDoorwaysLayout\Model\Config\Source\ColorTheme"/>
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
</form>
