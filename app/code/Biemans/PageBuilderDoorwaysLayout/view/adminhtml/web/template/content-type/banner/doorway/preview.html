<div class="pagebuilder-content-type pagebuilder-banner type-nested"
     attr="data.main.attributes"
     ko-style="data.main.style"
     css="data.main.css"
     event="{ mouseover: onMouseOver, mouseout: onMouseOut }, mouseoverBubble: false">
    <render args="getOptions().template"></render>
    <div class="pagebuilder-banner-wrapper">
        <div class="pagebuilder-banner-image"
             attr="data.wrapper.attributes"
             ko-style="data.wrapper.style"
             css="data.wrapper.css"
             event="{mouseover: onMouseOverWrapper, mouseout: onMouseOutWrapper}">
            <scope args="getUploader().getUiComponent()">
                <render></render>
            </scope>
        </div>
        <div class="pagebuilder-overlay pagebuilder-doorway-overlay"
             attr="data.overlay.attributes"
             ko-style="data.overlay.style"
             css="data.overlay.css"
             event="mousedown: activateEditor">
            <div class="pagebuilder-doorway-content">
                <a if="data.main.attributes()['data-show-button'] !== 'never'"
                   type="button"
                   class="pagebuilder-banner-button"
                   attr="data.button.attributes"
                   ko-style="data.button.style"
                   css="data.button.css">
                    <span data-bind="liveEdit: { field: 'button_text', placeholder: buttonPlaceholder, selectAll: true }"></span>
                </a>
            </div>
        </div>
    </div>
</div>
