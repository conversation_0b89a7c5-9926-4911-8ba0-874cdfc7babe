<div attr="data.main.attributes"
     ko-style="data.main.style"
     css="data.main.css">
    <div attr="data.inner.attributes"
         ko-style="Object.assign(data.container.style(), data.inner.style())"
         css="data.inner.css">
        <div if="data.video_overlay.attributes()['data-video-overlay-color']"
             class="video-overlay"
             attr="data.video_overlay.attributes"
             ko-style="data.video_overlay.style">
        </div>
        <div class="doorways">
            <render args="masterTemplate"></render>
        </div>
    </div>
</div>
