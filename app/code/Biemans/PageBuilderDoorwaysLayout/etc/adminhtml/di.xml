<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <virtualType name="AppearanceSourceRow">
        <arguments>
            <argument name="optionsData" xsi:type="array">
                <item name="3" xsi:type="array">
                    <item name="value" xsi:type="string">doorways</item>
                    <item name="title" xsi:type="string" translate="true">Doorways</item>
                    <item name="icon" xsi:type="string">Biemans_PageBuilderDoorwaysLayout/css/images/content-type/row/appearance/doorways.svg</item>
                </item>
            </argument>
            <argument name="optionsData" xsi:type="array">
                <item name="4" xsi:type="array">
                    <item name="value" xsi:type="string">doorways-title</item>
                    <item name="title" xsi:type="string" translate="true">Doorways Title</item>
                    <item name="icon" xsi:type="string">Biemans_PageBuilderDoorwaysLayout/css/images/content-type/row/appearance/doorways-title.svg</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="AppearanceSourceBanner">
        <arguments>
            <argument name="optionsData" xsi:type="array">
                <item name="6" xsi:type="array">
                    <item name="value" xsi:type="string">doorway</item>
                    <item name="title" xsi:type="string" translate="true">Doorway</item>
                    <item name="icon" xsi:type="string">Biemans_PageBuilderDoorwaysLayout/css/images/content-type/banner/appearance/doorway.svg</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
