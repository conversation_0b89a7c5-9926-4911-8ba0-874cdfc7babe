<?php

declare(strict_types=1);

namespace <PERSON>iemans\PageBuilderDoorwaysLayout\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class ColorTheme implements OptionSourceInterface
{
    /**
     * @return array<mixed>
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 'black', 'label' => __('Black')],
            ['value' => 'white', 'label' => __('White')]
        ];
    }
}
