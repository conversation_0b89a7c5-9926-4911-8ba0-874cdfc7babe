<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Mageplaza\QuickOrder\Helper\Search">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Helper_Search"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Helper\Search"/>
    </type>
    <type name="Mageplaza\QuickOrder\Helper\Data">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Helper_Data"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Helper\Data"/>
    </type>
    <type name="Mageplaza\QuickOrder\Helper\Item">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Helper_Item"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Helper\Item"/>
    </type>
    <type name="Mageplaza\QuickOrder\Controller\Index\Index">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Index"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Controller\Index\Index"/>
    </type>
    <type name="Mageplaza\QuickOrder\Controller\Items\Preitem">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Preitem"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Controller\Items\Preitem"/>
    </type>
    <type name="Mageplaza\QuickOrder\Controller\Items\Cartcheckout">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Cartcheckout"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Controller\Items\Cartcheckout"/>
    </type>
    <type name="Mageplaza\QuickOrder\Controller\Items\Itemqty">
        <plugin name="boland_quicorder_remove_over_stock_warning"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Controller\Items\RemoveOverStockWarning"/>
    </type>
    <type name="Mageplaza\QuickOrder\Block\Dashboard">
        <plugin name="Biemans_ExtendedMageplazaQuickOrder_Dashboard"
                type="Biemans\ExtendedMageplazaQuickOrder\Plugin\Block\Dashboard"/>
    </type>

    <!-- Add quick order generate json file command to CLI -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="quick_order_generated_json_file" xsi:type="object">
                    Biemans\ExtendedMageplazaQuickOrder\Console\Command\GenerateJsonFile
                </item>
            </argument>
        </arguments>
    </type>
</config>
