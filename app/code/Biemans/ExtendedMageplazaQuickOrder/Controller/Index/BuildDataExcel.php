<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedMageplazaQuickOrder\Controller\Index;

use B<PERSON>mans\ExtendedMageplazaQuickOrder\Model\ExcelFileProcessor;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultInterface;

class BuildDataExcel extends Action
{
    private JsonFactory $jsonFactory;
    private ExcelFileProcessor $excelFileProcessor;

    public function __construct(
        Context $context,
        JsonFactory $jsonFactory,
        ExcelFileProcessor $excelFileProcessor
    ) {
        $this->jsonFactory = $jsonFactory;
        $this->excelFileProcessor = $excelFileProcessor;
        parent::__construct($context);
    }

    /**
     * @return ResponseInterface|Json|ResultInterface
     * @throws \Exception
     */
    public function execute()
    {
        $result = [];
        $fileInfo = $this->getRequest()->getFiles('files'); /** @phpstan-ignore-line */
        $type = trim(strtolower($this->getRequest()->getParam('type') ?? ''));

        if ($type && $fileInfo && is_array($fileInfo)) {
            $result = ['SKU,QTY,Option1:value1,Option2:value2'];

            $items = $this->excelFileProcessor->process($fileInfo, $type);
            foreach ($items as $item) {
                if (isset($item['A']) && strtolower($item['A']) === 'article') {
                    continue;
                }
                $node = implode(',', (array)$item);
                $result[] = $node;
            }
        }

        return $this->jsonFactory->create()->setData($result);
    }
}
