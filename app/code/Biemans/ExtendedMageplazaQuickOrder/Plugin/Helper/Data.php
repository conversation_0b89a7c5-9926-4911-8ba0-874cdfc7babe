<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\ExtendedMageplazaQuickOrder\Plugin\Helper;

class Data
{
    /**
     * Allow quick-order for all logged in users
     *
     * @param bool $result
     * @return bool
     */
    public function afterCheckPermissionAccess(\Mageplaza\QuickOrder\Helper\Data $subject, $result)
    {
        if ($subject->getCustomerLogedIn()) {
            return true;
        }

        return false;
    }
}
