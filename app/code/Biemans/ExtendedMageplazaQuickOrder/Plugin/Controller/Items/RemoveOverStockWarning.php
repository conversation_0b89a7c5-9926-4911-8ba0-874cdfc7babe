<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\ExtendedMageplazaQuickOrder\Plugin\Controller\Items;

use Magento\Framework\App\Response\Http;
use Magento\Framework\Serialize\Serializer\Json;
use Mageplaza\QuickOrder\Controller\Items\Itemqty;

class RemoveOverStockWarning
{
    private Json $json;

    public function __construct(
        Json $json
    ) {
        $this->json = $json;
    }

    /**
     * Remove overStock notification as it's not used and will prevent qty update
     *
     * @see Itemqty::execute()
     *
     * @return void
     */
    public function afterExecute(
        Itemqty $subject,
        Http $response
    ) {
        $data = $response->getContent() ? $this->json->unserialize($response->getContent()) : [];

        if (isset($data['overStock'])) {
            unset($data['overStock']);
        }

        $response->setContent($this->json->serialize($data));
    }
}
