<?php

declare(strict_types=1);

namespace Biemans\ExtendedMageplazaQuickOrder\Plugin\Controller\Items;

use <PERSON><PERSON><PERSON>\ExtendedMageplazaQuickOrder\Model\ProductExtraData;
use B<PERSON>mans\ExtendedMageplazaQuickOrder\Model\QuickOrderProducts;
use Biemans\ExtendedMageplazaQuickOrder\Model\Stock\StockConfiguration;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Type;
use Magento\Catalog\Model\ResourceModel\Product as ProductResourceModel;
use Magento\Framework\App\Response\Http;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Swatches\Block\Product\Renderer\Configurable;
use Mageplaza\QuickOrder\Controller\Items\Preitem as OriginalPreitem;
use Mageplaza\QuickOrder\Helper\Item;

class Preitem
{
    const DATA_PERSISTOR_PRODUCTS_KEY = 'quickorder_products';

    private StockConfiguration $stockConfiguration;
    private Configurable $configurableBlock;
    private ProductRepositoryInterface $productRepository;
    private Item $itemHelper;
    private ProductExtraData $extraData;
    private ProductResourceModel $productResource;
    private StoreManagerInterface $storeManager;
    private QuickOrderProducts $quickOrderProducts;
    private Json $json;

    /** @var array<mixed> */
    private array $products = [];

    public function __construct(
        StockConfiguration         $stockConfiguration,
        ProductRepositoryInterface $productRepository,
        Configurable               $configurableBlock,
        Item                       $itemHelper,
        ProductExtraData           $productExtraData,
        ProductResourceModel       $productResource,
        StoreManagerInterface      $storeManager,
        QuickOrderProducts         $quickOrderProducts,
        Json                       $json
    ) {
        $this->stockConfiguration = $stockConfiguration;
        $this->configurableBlock = $configurableBlock;
        $this->productRepository = $productRepository;
        $this->itemHelper = $itemHelper;
        $this->extraData = $productExtraData;
        $this->productResource = $productResource;
        $this->storeManager = $storeManager;
        $this->quickOrderProducts = $quickOrderProducts;
        $this->json = $json;
    }

    /**
     * @see OriginalPreitem::execute()
     *
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeExecute(
        OriginalPreitem $subject
    ) {
        $value = $subject->getRequest()->getParam('value');
        if (!$value) {
            $data = $subject->getRequest()->getContent(); /** @phpstan-ignore-line */
            parse_str($data, $parsed);
            if (isset($parsed['value'])) {
                $value = $parsed['value'];
            }
        }

        if (!$value) {
            return [];
        }

        // Prepare POST data
        $data = $this->json->unserialize($value);
        $params = $subject->getRequest()->getParams();
        $params['value'] = $data;
        $subject->getRequest()->setParams($params);

        /** Filter data, then prepare products collection */
        $products = $this->quickOrderProducts->getProducts();
        $originalData = [];

        // Prepare errors
        $errors = [];
        if ($data) {
            foreach ($data as $key => $value) { /** @phpstan-ignore-line */
                $valueArray = array_map('trim', explode(',', $value));

                try {
                    $qty = isset($valueArray[1]) ? intval($valueArray[1]) : 1;
                    $this->itemHelper->getPreItemNotMeetConditionsFilter($valueArray[0], $qty);
                    $originalData[$valueArray[0]] = $qty;
                } catch (\Exception $e) {
                    if ($key !== 0) { // Do not report error for header
                        $errors[] = [
                            'sku' => $valueArray[0],
                            'error' => __($e->getMessage())
                        ];
                    }
                    unset($data[$key]);
                }
            }
        }

        $subject->getRequest()->setParams([
            'value' => $data,
            'original_data' => $originalData,
            'errors' => $errors
        ]);

        return [];
    }

    /**
     * @see OriginalPreitem::execute()
     *
     * @return ResponseInterface|ResultInterface|mixed
     */
    public function afterExecute(
        OriginalPreitem $subject,
        Http $response
    ) {
        $json = $response->getContent();
        $products = $json ? $this->json->unserialize($json) : [];
        $products = is_array($products) ? $products : [];

        $products = array_map([$this, 'addStockInfoToProduct'], $products);
        $products = array_map([$this, 'addSwatchInfo'], $products);
        $products = array_map([$this, 'addExtraData'], $products);
        $addedItemsNumber = count($products);
        $addedItemsSku = array_column($products, 'sku');

        // Get original data from request
        $originalData = $subject->getRequest()->getParam('original_data');

        // Prepare errors
        $errors = $subject->getRequest()->getParam('errors');
        if ($errors) {
            $products = array_merge($products, $errors);
        }

        // Sort products - first the ones with errors
        $outOfStockItemsNumber = 0;
        foreach ($products as $id => $product) {
            // 'instock' is true if item is in stock and enabled, and false if it's not
            $products[$id]['sort'] = (isset($product['instock']) && $product['instock'] && !isset($product['warning'])) ? ($id+1)*10 : 0;
            $products[$id]['sort'] = (isset($product['stock_warning']) && $product['stock_warning']) ? 1 : $products[$id]['sort'];
            (isset($product['instock']) && $product['instock'] === false) ? $outOfStockItemsNumber++ : null;
        }
        $sort = array_column($products, 'sort');
        array_multisort($sort, SORT_ASC, $products);

        // Get products SKUs from request
        $requestProductsData = $subject->getRequest()->getParam('original_value');
        $requestedProductsSku = [];
        if ($requestProductsData) {
            foreach ($requestProductsData as $key => $value) {
                /** @phpstan-ignore-line */
                $valueArray = array_map('trim', explode(',', $value));
                $requestedProductsSku[] = $valueArray[0];
            }
        }
        $missedProducts = array_diff($requestedProductsSku, $addedItemsSku);

        if ($outOfStockItemsNumber > 0) {
            array_unshift($products,
                [
                    'warning' => ($outOfStockItemsNumber == 1) ?
                        __('1 item is out of stock or discontinued from our catalog') :
                        __('%1 items are out of stock or discontinued from our catalog', $outOfStockItemsNumber)
                ]
            );
        }

        if (count($missedProducts) > 0) {
            array_unshift($products,
                [
                    'error' => __('%1 item(s) cannot be found in our catalog: %2', count($missedProducts),
                        implode(', ', $missedProducts))
                ]
            );
        }

        if ($addedItemsNumber > 0) {
            array_unshift($products,
                [
                    'info' => __('%1 item(s) are successfully added to list, from %2 requested item(s).',
                        $addedItemsNumber, count($requestProductsData))
                ]
            );
        }

        // Check which products had qty adjusted
        $productsWithAdjustedQty = [];
        foreach ($products as $product) {
            if (
                isset($product['sku'])
                && isset($originalData[$product['sku']])
                && ($originalData[$product['sku']] != $product['qty'])
            ) {
                $productsWithAdjustedQty[] = $product['sku'];
            }
        }
        if (count($productsWithAdjustedQty)) {
            array_unshift($products,
                [
                    'warning' => __('%1 item(s) had the qty adjusted: %2',
                        count($productsWithAdjustedQty),
                        implode(', ', $productsWithAdjustedQty)
                    )
                ]
            );
        }

        $response->setContent($this->json->serialize($products));
        $subject->getResponse()->representJson($this->json->serialize(['products' => $products])); /** @phpstan-ignore-line */

        return $response;
    }

    /**
     * @param array<mixed> $product
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function addStockInfoToProduct($product)
    {
        if (!isset($product['product_id'])) {
            return $product;
        }

        $id = $product['product_id'];

        $product['has_increments_enabled'] = $this->stockConfiguration->hasIncrementsEnabled($id);
        $product['quantity_increments'] = $this->stockConfiguration->getQuantityIncrements($id);

        // Round qty by quantity_increments
        if (isset($product['qty']) && $product['qty']%$product['quantity_increments'] !== 0) {
            if ($product['qty'] < $product['quantity_increments']) {
                $product['qty'] = $product['quantity_increments'];
            } else {
                $product['qty'] = floor($product['qty'] / $product['quantity_increments']) * $product['quantity_increments'];
            }
        }

        return $product;
    }

    /**
     * @param array<mixed> $product
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function addSwatchInfo($product)
    {
        if ($this->getProductTypeId($product['product_id']) == \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) {
            /** @var Product $productInstance */
            $productInstance = $this->getProductById($product['product_id']);
            $this->configurableBlock->setProduct($productInstance);

            $product['numberToShow'] = $this->configurableBlock->getNumberSwatchesPerProduct();
            $product['jsonConfig'] = $this->json->unserialize($this->configurableBlock->getJsonConfig());
            $product['jsonSwatchConfig'] = $this->json->unserialize($this->configurableBlock->getJsonSwatchConfig());
            $product['jsonSwatchImageSizeConfig'] = $this->json->unserialize($this->configurableBlock->getJsonSwatchSizeConfig());
            $product['mediaCallback'] = $this->configurableBlock->getMediaCallback();
        }

        return $product;
    }

    /**
     * @param string|int $productId
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getProductTypeId($productId)
    {
        // For static attribute an array is returned with all catalog_product_entity values
        $productTypeId = $this->productResource->getAttributeRawValue(
            (int)$productId,
            'type_id',
            $this->storeManager->getStore()->getId()
        );

        return is_array($productTypeId) ? $productTypeId['type_id'] : $productTypeId;
    }

    /**
     * @param array<mixed> $product
     * @return array<mixed>
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\InventoryConfigurationApi\Exception\SkuIsNotAssignedToStockException
     */
    private function addExtraData($product)
    {
        if (isset($product['product_id']) && isset($product['type_id']) && isset($product['sku'])) {
            $productId = (int)$product['product_id'];
            $typeId = $product['type_id'];
            $sku = $product['sku'];
            $product['extra_sku'] = ($typeId === Type::TYPE_SIMPLE) ? $sku : '-';
            $product['stock_qty'] = ($typeId === Type::TYPE_SIMPLE) ? $this->extraData->getStockQty($productId) : '-';

            /**
             * Set qty_warning flag to trigger error next to product if requested qty exceeds stock
             * Warning should apply for products with disabled backorders that are in stock
             * (Mageplaza uses outofstock in a wrong way: true = in stock)
             */
            $product['qty_warning'] = (!$this->extraData->canBackorder($productId) && $product['instock']);
            // Add warning message
            if ($product['qty_warning'] && $product['qty'] > $product['qtystock']) {
                $product['qty'] = $product['qtystock'];

                // Round qty by quantity_increments
                if ($product['qty']%$product['quantity_increments'] !== 0) {
                    if ($product['qty'] < $product['quantity_increments']) {
                        $product['qty'] = $product['quantity_increments'];
                    } else {
                        $product['qty'] = floor($product['qty'] / $product['quantity_increments']) * $product['quantity_increments'];
                    }
                }

                $product['warning'] = __('The requested quantity is no longer available. The quantity has changed to the available stock.');
            }

            $product['price'] = $this->extraData->getDefaultPrice($productId);
            $product['base_price'] = $product['price'];
            $product['tier_price'] = $this->extraData->getTierPrices($productId);

        }

        return $product;
    }

    /**
     * @param string|int $productId
     * @return ProductInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getProductById($productId): ProductInterface
    {
        if (isset($this->products[$productId])) {
            return $this->products[$productId];
        }

        return $this->products[$productId] = $this->productRepository->getById((int)$productId);
    }
}
