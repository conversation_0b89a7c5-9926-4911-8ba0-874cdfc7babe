<?php

declare(strict_types=1);

namespace <PERSON>iemans\ExtendedMageplazaQuickOrder\Plugin\Controller\Items;

use Magento\Framework\Serialize\Serializer\Json;
use Mageplaza\QuickOrder\Controller\Items\Cartcheckout as OriginalCartcheckout;

class Cartcheckout
{
    private Json $json;

    public function __construct(
        Json $json
    ) {
        $this->json = $json;
    }

    /**
     * @see OriginalCartcheckout::execute()
     *
     * @return array<mixed>
     */
    public function beforeExecute(
        OriginalCartcheckout $subject
    ) {
        $value = $subject->getRequest()->getParam('listitem');

        if (!$value) {
            $value = $subject->getRequest()->getContent(); /** @phpstan-ignore-line */
            parse_str($value, $parsed);
            if (isset($parsed['listitem'])) {
                $value = $parsed['listitem'];
            }
        }

        if (!$value) {
            return [];
        }

        // Prepare formatted POST data
        $data = $this->json->unserialize($value);
        $params = $subject->getRequest()->getParams();
        $params['listitem'] = $data;
        $subject->getRequest()->setParams($params);

        return [];
    }
}
