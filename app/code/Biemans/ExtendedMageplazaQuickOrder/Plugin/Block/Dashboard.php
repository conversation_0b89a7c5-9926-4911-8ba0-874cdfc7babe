<?php

declare(strict_types=1);

namespace <PERSON><PERSON>mans\ExtendedMageplazaQuickOrder\Plugin\Block;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Store\Model\ScopeInterface;
use Mageplaza\QuickOrder\Block\Dashboard as Subject;

class Dashboard
{
    const XML_PATH_KEY_MAX_ITEMS = 'quickorder/general/max_items';

    private ScopeConfigInterface $scopeConfig;
    private Json $json;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        Json $json
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->json = $json;
    }

    /**
     * Add maximum items message to configuration data
     *
     * @return bool|false|string
     */
    public function afterGetQuickOrderConfig(
        Subject $subject,
        string $result
    ) {
        if ($maxItems = $this->getMaximumNumberOfItems()) {
            try {
                $data = $this->json->unserialize($result);
                if (!is_array($data)) {
                    $data = [];
                }
                $data['maxItemsMessage'] = __('You can import a maximum of %1 products by .csv.', $maxItems);
                $data['buildDataExcel'] = $subject->getUrl('biemans-quick-order/index/builddataexcel');
                $result = $this->json->serialize($data);
            } catch (\Exception $e) {
            }
        }

        return $result;
    }

    /**
     * Get list maximum number of items
     */
    public function getMaximumNumberOfItems(): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_KEY_MAX_ITEMS,
            ScopeInterface::SCOPE_STORE
        );
    }
}
