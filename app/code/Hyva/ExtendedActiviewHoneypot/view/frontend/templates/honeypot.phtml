<?php

/** @var \Actiview\Honeypot\Block\Honeypot $block */
/** @var \Magento\Framework\Escaper $escaper */

?>
<script defer="defer">
    function honeypot() {
        return {
            forms: <?= json_encode($block->getForms()); ?>,
            fieldName: '<?= $escaper->escapeJs($block->getFieldName()); ?>',
            fieldClass: '<?= $escaper->escapeJs($block->getFieldClass()); ?>',

            addField() {
                var element = this.createElement();

                Array.from(this.forms)
                    .map(selector => {
                        Array.from(document.querySelectorAll(selector))
                            .map(form => {
                                form.appendChild(element);
                            });
                    });
            },

            createElement: function () {
                var element = document.createElement('input');
                element.type = 'text';
                element.name = this.fieldName;
                element.className = this.fieldClass;
                element.style.cssText = 'display: none';

                return element;
            }
        }
    }
</script>

<section x-data="honeypot()" x-init="addField()" />
