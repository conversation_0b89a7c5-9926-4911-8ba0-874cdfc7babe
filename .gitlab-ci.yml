include:
  - project: 'platforms/magento/tools/gitlab-ci'
    ref: flow
    file: '.gitlab-ci-template.yml'
  - template: Security/SAST.gitlab-ci.yml

image: ${CI_REGISTRY}/docker/magento-ci:8.1.21

'Static Content: Magento/backend':
  extends: .static-content
  variables:
    LANGUAGE: en_US nl_NL
    THEME: Magento/backend

'Static Content: Biemans/default':
  extends: .static-content
  variables:
    LANGUAGE: en_US nl_NL de_DE fr_FR pl_PL
    THEME: Biemans/default

'Package Artifact':
  extends: .package-artifact
  needs:
    - !reference [.package-artifact, needs]
    - 'Static Content: Magento/backend'
    - 'Static Content: Biemans/default'
  dependencies:
    - Create source package
    - DI Compile
    - 'Static Content: Magento/backend'
    - 'Static Content: Biemans/default'

'Deploy test':
  extends: .deploy-source
  rules: !reference ['.rules', 'staging']
  needs:
    - Package Artifact
  environment:
    name: test
    url: https://test.biemans.com.mage2cloud.nl/
  variables:
    HOST: <EMAIL>
    DEPLOY_ROOT: /home/<USER>/magento2
    PUBLIC_ROOT: /home/<USER>/www/pub

'Deploy accept':
  extends: .deploy-source
  rules: !reference ['.rules', 'deploy']
  needs:
    - Package Artifact
    - Deploy test
  environment:
    name: accept
    url: https://accept.biemans.com.mage2cloud.nl/
  variables:
    HOST: <EMAIL>
    DEPLOY_ROOT: /home/<USER>/magento2
    PUBLIC_ROOT: /home/<USER>/www/pub

'Deploy production':
  extends: .deploy-source
  rules: !reference ['.rules', 'deploy']
  needs:
    - Package Artifact
    - Deploy accept
  environment:
    name: production
    url: https://www.biemans.com/
  variables:
    HOST: <EMAIL>
    DEPLOY_ROOT: /home/<USER>/magento2
    PUBLIC_ROOT: /home/<USER>/www/pub
  when: manual
