<?php

namespace Deployer;

require 'vendor/redkiwi/magento2-deployer/recipe/magento_2_4.php';

set('php-version', '8.1');

// Set artifact dir for gitlab
set('artifact_dir', 'artifacts');

// Prevent composer install during deploy (its done in build)
task('deploy:vendors', function () {
});

// Gather the public SSH host keys of deployment target and add to known_hosts file
task('ssh-keyscan', function () {
    runLocally(sprintf('ssh-keyscan %s >> ~/.ssh/known_hosts', get('hostname')));
});

$shared = get('shared_dirs');
set('shared_dirs', array_merge(
    $shared,
    [
        '{{magento_dir}}/var/biemans_price_list',
        '{{magento_dir}}/var/pdf_catalog',
        '{{magento_dir}}/var/biemans_excel_tpm'
    ]
));
