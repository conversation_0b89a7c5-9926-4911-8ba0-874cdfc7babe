#!/bin/bash

YELLOW="\033[0;33m"
PURPLE="\033[0;35m"
NC="\033[0m"

DB=$@

if [ "" == "$DB" ]
then
    DB="db"
fi

SOURCE_PATH=".db/local/${DB}"

SOURCE_FILE="$SOURCE_PATH/$(ls $SOURCE_PATH | grep ".sql.gz" | tail -1)"

if [ -e $SOURCE_FILE ]
then
    echo -e "${YELLOW}Importing ${PURPLE}$SOURCE_FILE${YELLOW} into ${PURPLE}${DB}${YELLOW}...${NC}"

    zcat $SOURCE_FILE | mysql -uroot -proot -hdb ${DB}
else
    echo -e "${YELLOW}Nothing to import${NC}"
fi
