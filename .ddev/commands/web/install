#!/bin/bash

if [ -e $WEBSERVER_DOCROOT/app/etc/env.php ] && [ "-f" = "$1" ]
then
    mv $WEBSERVER_DOCROOT/app/etc/env.php $WEBSERVER_DOCROOT/app/etc/env.php.bak
fi

$WEBSERVER_DOCROOT/bin/magento setup:install --base-url=https://localhost --db-host=db --db-name=db --db-user=db --db-password=db --search-engine=elasticsearch7 --elasticsearch-host=elasticsearch --elasticsearch-port=9200 --backend-frontname=beheer --admin-firstname=Admin --admin-lastname=Redkiwi --admin-email=<EMAIL> --admin-user=redkiwi --admin-password=r3dk1w1 --language=en_US --currency=EUR --timezone=Europe/Amsterdam --use-rewrites=1

if [ -e $WEBSERVER_DOCROOT/app/etc/env.php.bak ]
then
    mv $WEBSERVER_DOCROOT/app/etc/env.php.bak $WEBSERVER_DOCROOT/app/etc/env.php
    $WEBSERVER_DOCROOT/bin/magento app:config:import
fi
