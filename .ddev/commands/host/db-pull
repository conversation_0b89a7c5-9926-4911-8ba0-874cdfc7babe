#!/bin/bash

ARGS=()
LIVE=false

while [ -n "$1" ];
do
    case "$1" in
    --live) LIVE=true;;
    *) ARGS+=($1);;
    esac

    shift
done

HOST=${ARGS[0]}
USER=${ARGS[1]}

if [ -z "$HOST" ]; then
    echo "Specify a host"
    exit 1
fi

if [ -z "$USER" ]; then
    echo "Specify a user"
    exit 1
fi

if [ true = $LIVE ]; then
    echo "Pulling live DB..."

    ssh -J commandcloud.nl $HOST "mysqldump --no-tablespaces -u$USER -p\$(php -r \"echo (include('/home/<USER>/www/app/etc/env.php'))['db']['connection']['default']['password'];\") $USER | sed 's/DEFINER=[^\*]*\*/*/g' | gzip" | gunzip  | ddev exec -s web mysql -uroot -proot db
else
    echo "Pulling backup DB..."

    ssh -J commandcloud.nl $HOST cat /var/backups/sql/$USER.sql.gz | gunzip | sed 's/DEFINER=[^\*]*\*/*/g' | ddev exec -s web mysql -uroot -proot db
fi
