#!/bin/bash

ARGS=()
WITH_PRODUCTS=false

while [ -n "$1" ];
do
    case "$1" in
    --with-products) WITH_PRODUCTS=true;;
    *) ARGS+=($1);;
    esac

    shift
done

HOST=${ARGS[0]}
USER=${ARGS[1]}

if [ -z "$HOST" ]; then
    echo "Specify a host"
    exit 1
fi

if [ -z "$USER" ]; then
    echo "Specify a user"
    exit 1
fi

if [ true = $WITH_PRODUCTS ]; then
    rsync -azvP -e 'ssh -J commandcloud.nl' $HOST:/home/<USER>/www/pub/media/ pub/media
else
    rsync -azvP -e 'ssh -J commandcloud.nl' --exclude 'catalog/product' $HOST:/home/<USER>/www/pub/media/ pub/media
fi
