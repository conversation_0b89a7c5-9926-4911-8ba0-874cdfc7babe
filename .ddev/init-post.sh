#!/bin/bash

cd "${BASH_SOURCE%/*}/.." || exit

if [ -e .ddev/.init-pre ] && [ ! -e .ddev/.init-post ]
then
    ddev exec composer install
    ddev db-pull $1 test --live
    ddev media-pull $1 test
    ddev exec bin/magento setup:upgrade
    ddev exec bin/magento index:reindex
    npm --prefix app/design/frontend/Biemans/default/web/tailwind/ install
    npm --prefix app/design/frontend/Biemans/default/web/tailwind/ run build

    date > .ddev/.init-post
fi
