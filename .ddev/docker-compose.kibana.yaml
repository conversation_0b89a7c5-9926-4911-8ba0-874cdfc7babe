version: "3.6"

services:
  kibana:
    container_name: ddev-${DDEV_SITENAME}-kibana
    image: docker.redkiwi.nl/docker/elastic/kibana:7.9.3
    restart: always
    ports:
      - 5601
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    environment:
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=5601
      - ELASTICSEARCH_HOSTS=http://ddev-${DDEV_SITENAME}-elasticsearch.ddev_default:9200
    volumes: []
