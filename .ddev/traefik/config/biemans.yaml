#ddev-generated
# If you remove the ddev-generated line above you
# are responsible for maintaining this file. DDEV will not then
# update it, for example if you add `additional_hostnames`, etc.

http:
  routers:
    biemans-rabbitmq-5672-http:
      entrypoints:
        - http-5672
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-rabbitmq-5672"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    biemans-redis-6379-http:
      entrypoints:
        - http-6379
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-redis-6379"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    biemans-web-80-http:
      entrypoints:
        - http-80
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-web-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    biemans-web-8025-http:
      entrypoints:
        - http-8025
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-web-8025"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    biemans-xhgui-80-http:
      entrypoints:
        - http-8143
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-xhgui-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    biemans-elasticsearch-9200-http:
      entrypoints:
        - http-9200
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-elasticsearch-9200"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    biemans-kibana-5601-http:
      entrypoints:
        - http-5601
      rule: HostRegexp(`^biemans\.local$`)|| HostRegexp(`^www\.biemans\.com\.local$`)|| HostRegexp(`^www\.biemanspolska\.pl\.local$`)|| HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-kibana-5601"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "biemans-redirectHttps"
    
    
    
    
    
    biemans-web-80-https:
      entrypoints:
        - http-443
      rule: HostRegexp(`^biemans\.local$`) || HostRegexp(`^www\.biemans\.com\.local$`) || HostRegexp(`^www\.biemanspolska\.pl\.local$`) || HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-web-80"
      ruleSyntax: v3
      
      tls: true
      
    biemans-web-8025-https:
      entrypoints:
        - http-8026
      rule: HostRegexp(`^biemans\.local$`) || HostRegexp(`^www\.biemans\.com\.local$`) || HostRegexp(`^www\.biemanspolska\.pl\.local$`) || HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-web-8025"
      ruleSyntax: v3
      
      tls: true
      
    
    biemans-xhgui-80-https:
      entrypoints:
        - http-8142
      rule: HostRegexp(`^biemans\.local$`) || HostRegexp(`^www\.biemans\.com\.local$`) || HostRegexp(`^www\.biemanspolska\.pl\.local$`) || HostRegexp(`^www\.biemansuk\.com\.local$`)
      
      service: "biemans-xhgui-80"
      ruleSyntax: v3
      
      tls: true
      
    
    
    

  middlewares:
    biemans-redirectHttps:
      redirectScheme:
        scheme: https
        permanent: true

  services:
    biemans-rabbitmq-5672:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-rabbitmq:5672
        
    biemans-redis-6379:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-redis:6379
        
    biemans-web-80:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-web:80
        
    biemans-web-8025:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-web:8025
        
    
    
    biemans-xhgui-80:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-xhgui:80
        
    
    biemans-elasticsearch-9200:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-elasticsearch:9200
        
    biemans-kibana-5601:
      loadbalancer:
        servers:
          - url: http://ddev-biemans-kibana:5601
        
    

tls:
  certificates:
    - certFile: /mnt/ddev-global-cache/traefik/certs/biemans.crt
      keyFile: /mnt/ddev-global-cache/traefik/certs/biemans.key