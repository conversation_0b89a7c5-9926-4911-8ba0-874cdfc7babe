name: biemans
type: magento2
docroot: pub
php_version: "8.1"
webserver_type: nginx-fpm
router_http_port: "80"
router_https_port: "443"
xdebug_enabled: false
additional_hostnames:
- www.biemans.com
- www.biemanspolska.pl
- www.biemansuk.com
additional_fqdns: []
database:
  type: mariadb
  version: "10.4"
nfs_mount_enabled: true
use_dns_when_possible: false
timezone: Europe/Amsterdam
hooks:
  post-start:
    - exec: rm -f /usr/local/bin/magerun
    - exec-host: sh .ddev/init-post.sh vps0500.rk.prolocation.net
  pre-start:
    - exec-host: sh .ddev/init-pre.sh
omit_containers: [dba]
project_tld: local
composer_version: "2"
web_environment: []
