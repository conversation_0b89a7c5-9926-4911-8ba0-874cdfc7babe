version: '3.6'
services:
  elasticsearch:
    container_name: ddev-${DDEV_SITENAME}-elasticsearch
    build: ./elasticsearch
    ports:
      - "9200"
      - "9300"
    environment:
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=9200
      - discovery.type=single-node
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    volumes:
      - elasticsearch:/usr/share/elasticsearch/data
  web:
    links:
      - elasticsearch:elasticsearch

volumes:
  elasticsearch:
    name: "${DDEV_SITENAME}-elasticsearch"
