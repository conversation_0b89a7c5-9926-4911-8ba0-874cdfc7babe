# Magento Install

Default installation for Magento projects.

## Usage


Use Composer to set up a new project with the following command:

```
docker run -a stdout -a stderr -v $(pwd):$(pwd) -w $(pwd) --rm drud/ddev-webserver:v1.13.0 composer --repository-url=https://composer.toolscloud.nl create-project redkiwi/magento-install <project_name>
```

Replace `<project_name>` with the name for your new project.

This will run the Composer command inside a docker container. Assuming your local environment meets the requirements it is also possible to run the command on your local machine using:

```
composer --repository-url=https://composer.toolscloud.nl create-project redkiwi/magento-install <project_name>
```

## Front-end

### CSS
The primary styling is done through TailwindCSS utility classes. 
Custom styling done with stylesheets can be located at `app/design/frontend/Biemans/default/web/tailwind/` and are imported via `app/design/frontend/Biemans/default/web/tailwind/tailwind-source.css`.

Tailwinds config file is located at `app/design/frontend/Biemans/default/web/tailwind/tailwind.config.js`.

Build commands can be found at `app/design/frontend/Biemans/default/web/tailwind/package.json`.

### Images
Images can be added in `app/design/frontend/Biemans/default/web/images`.

### Fonts
Custom fonts can be found at `app/design/frontend/Biemans/default/web/fonts` and imported via `app/design/frontend/Biemans/default/web/tailwind/components/fonts.css`.