{"name": "red<PERSON><PERSON>/biemans", "description": "Biemans Magento2 B2B webshop", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "version": "1.0.0", "config": {"platform": {"php": "8.1"}, "use-include-path": true, "preferred-install": {"biemans/*": "auto", "*": "dist"}, "sort-packages": true, "allow-plugins": {"magento/magento-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/inventory-composer-installer": true, "magento/composer-dependency-version-audit-plugin": false, "magento/composer-root-update-plugin": true, "cweagans/composer-patches": true, "phpro/grumphp": true}}, "require": {"actiview/module-honeypot": "^0.1.4", "amasty/module-custom-forms-lite-subscription-package": "^1.21", "biemans/prepress": "dev-master", "community-engineering/language-de_de": "^0.0.57", "community-engineering/language-fr_fr": "^0.0.47", "community-engineering/language-nl_nl": "^0.0.61", "community-engineering/language-pl_pl": "^0.0.40", "cweagans/composer-patches": "^1.7", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-hyva-checkout": "^1.1", "hyva-themes/magento2-magepal-google-analytics4": "^1.0", "hyva-themes/magento2-smile-elasticsuite": "^1.2", "iio/libmergepdf": "^4.0", "magento/product-community-edition": "2.4.6-p11", "magento/quality-patches": "*", "mageplaza/module-core": "^1.5", "mageplaza/module-quick-order": "^4.0", "magmodules/m2-alternate-hreflang": "^1.1", "olegkoval/magento2-regenerate-url-rewrites": "^1.6", "phpoffice/phpspreadsheet": "^1.29", "redkiwi/magento2-build": "^100.0", "redkiwi/magento2-deploy": "^100.2", "redkiwi/module-api-log": "^101.0", "redkiwi/module-google-verification": "^1.0", "redkiwi/module-rich-snippets": "^103.0", "redkiwi/module-translation-editor": "^100.0", "snowdog/module-menu": "^2.23", "tecnickcom/tcpdf": "^6.6", "yireo/magento2-disable-csp": "^1.0", "yireo/magento2-next-gen-images": "^0.5.0", "yireo/magento2-webp2": "^0.13.3"}, "require-dev": {"bitexpert/phpstan-magento": "^0.30.1", "php-parallel-lint/php-parallel-lint": "^1.3"}, "replace": {"astock/stock-api-libphp": "*", "magento/adobe-stock-integration": "*", "magento/google-shopping-ads": "*", "magento/language-de_de": "*", "magento/language-en_us": "*", "magento/language-es_es": "*", "magento/language-fr_fr": "*", "magento/language-nl_nl": "*", "magento/language-pt_br": "*", "magento/language-zh_hans_cn": "*", "magento/module-cardinal-commerce": "*", "magento/module-dhl": "*", "magento/module-elasticsearch-6": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-marketplace": "*", "magento/module-multishipping": "*", "magento/module-paypal-captcha": "*", "magento/module-paypal-recaptcha": "*", "magento/module-sample-data": "*", "magento/module-securitytxt": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-webapi-security": "*", "magento/theme-frontend-luma": "*", "paypal/module-braintree": "*", "temando/module-shipping-remover": "*", "magento/module-admin-analytics": "*", "magento/module-analytics": "*", "magento/module-catalog-analytics": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-customer-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-wishlist-analytics": "*", "magento/module-re-captcha-checkout": "*", "magento/module-re-captcha-checkout-sales-rule": "*", "magento/module-re-captcha-migration": "*", "magento/module-re-captcha-paypal": "*", "magento/module-re-captcha-review": "*", "magento/module-re-captcha-send-friend": "*", "magento/module-re-captcha-store-pickup": "*", "magento/module-re-captcha-user": "*", "magento/module-re-captcha-version-2-checkbox": "*", "magento/module-re-captcha-version-2-invisible": "*"}, "conflict": {"gene/bluefoot": "*"}, "autoload": {"psr-4": {"Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}, "psr-0": {"": ["app/code/", "generated/code/"]}, "files": ["app/etc/NonComposerComponentRegistration.php", "app/etc/stores.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "minimum-stability": "stable", "repositories": {"magepal": {"type": "composer", "url": "https://composer.magepal.com/"}, "0": {"type": "path", "url": "packages/*", "options": {"symlink": false}}, "1": {"type": "vcs", "url": "https://gitlab.redkiwi.nl/customers/biemans/prepress.git"}, "2": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/biemans-com/"}, "3": {"type": "composer", "url": "https://repo.mageplaza.com"}, "4": {"type": "composer", "url": "https://repo.magento.com/", "canonical": false}, "5": {"type": "composer", "url": "https://composer.toolscloud.nl/", "canonical": false}}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "magento-deploy-ignore": {"*": ["/.editorconfig", "/.giti<PERSON>re", "/.gitlab-ci.yml"]}, "patches": {"hyva-themes/magento2-default-theme": {"Fix duplicate alias for menu": "patches/hyva-themes/magento2-default-theme/fix-menu-error.patch"}, "hyva-themes/magento2-hyva-checkout": {"Skip JS validation for hidden forms": "patches/hyva-themes/magento2-hyva-checkout/skip-js-validation-for-hidden-forms.patch"}, "magento/framework": {"Patch the schema pattern: https://docs.hyva.io/hyva-themes/building-your-theme/styling-layout-containers.html": "patches/magento/framework/allow-tailwind-css-layout.patch"}, "magento/module-catalog": {"ACSD-49129": "patches/magento/module-catalog/ACSD-49129.patch"}, "magento/module-eav": {"https://github.com/magento/magento2/pull/37677": "patches/magento/module-eav/fix-save-for-non-numeric-values.patch"}, "magento/module-page-builder": {"Patch the schema pattern: https://docs.hyva.io/hyva-themes/building-your-theme/styling-layout-containers.html": "patches/magento/module-page-builder/allow-tailwind-css-layout.patch"}, "magento/module-product-alert": {"Fix redirect to product": "patches/magento/module-product-alert/fix-redirect-to-product.patch", "Fix: main.CRITICAL: Argument 2 passed to ErrorEmailSender::execute() must be of the type int": "patches/magento/module-product-alert/fix-store-id-error.patch"}, "magento/module-sales": {"Prevent sending comment emails for missing notification flag": "patches/magento/module-sales/prevent-sending-comment-email-for-missing-flag.diff", "Order access for email sender": "patches/magento/module-sales/RSD-1563_order_access_for_email_sender.patch"}, "mageplaza/module-quick-order": {"Show product sku on error msg": "patches/mageplaza/module-quick-order/RSD-487-show_sku_in_error_msg.patch", "Show actual message": "patches/mageplaza/module-quick-order/BIEM-251-show_actual_error_message.patch", "Remove return to prevent stopping the add to cart": "patches/mageplaza/module-quick-order/RSD-1010-fix_add_to_cart_on_thrown_error.patch"}, "setasign/fpdi": {"Allow external links on PDFs merge": "patches/setasign/fpdi/allow-external-link-on-merge.patch"}}}, "scripts": {"post-install-cmd": ["@apply-quality-patches"], "post-update-cmd": ["@apply-quality-patches"]}}