/.buildpath
/.cache
/.metadata
/.project
/.settings
atlassian*
/nbproject
/sitemap
/.idea
/.gitattributes

/app/code/Magento
/app/etc/*
!/app/etc/stores.php
!/app/etc/env.php.dist
!/app/etc/env.php.review-app
!/app/etc/hyva-themes.json
/app/i18n/magento
/app/*.*
/app/design/frontend/Biemans/default/web/css/styles.css

/private/pullit/*
!/private/pullit/local.json.dist
!/private/pullit/project.json

/bin

/dev/*
!/dev/tools
/dev/tools/*
!/dev/tools/grunt
/dev/tools/grunt/*
!/dev/tools/grunt/configs/
/dev/tools/grunt/configs/*
!/dev/tools/grunt/configs/themes.js
!/dev/tools/grunt/configs/themes.loc.js
!/dev/tools/grunt/configs/themes.local.js.dist

/lib/*

/pub/

node_modules

/setup

/var

/css

/js

/vendor

/generated

.DS_Store

/phpserver

/*.*

!/.ci
!/.ddev
!/.review
!/auth.json
!/composer.json
!/composer.lock
!/README.md
!/.gitignore
!/app/etc/config.php
!Gruntfile.js
!gulpfile.js
!package.json
!.php_cs.php
!.editorconfig
!build.sh
!/deploy.php
!/deploy.sh
!magepack.config.js
!phpstan.neon
!grumphp.yml

!.gitlab-ci.yml
